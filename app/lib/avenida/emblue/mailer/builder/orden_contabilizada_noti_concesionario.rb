module Avenida
  module Emblue
    module Mailer
      module Builder
        class OrdenContabilizadaNotiConcesionario < JsonBuilder
          private
          def initialize_variables
            @event = "orden_contabilizada_noti_concesionario"
            @customer = @order.customer
            @info_package = @order.info_package
            @product = @order.customer_reservation_purchases.product
            set_seller_variables
          end

          def build_attributes
            @attributes.merge!({
              monto_prestamo_aprobado: "$ #{@order.customer_reservation_purchases.approved_lended_amount.truncate}",
              numero_de_orden: @order.id.to_s.to_s,
              id_solicitud: @info_package.solicitude_id_movilidad,
              sucursal: @info_package.office_token,
              valor_producto_reservado_cliente: "$ #{@product.price.truncate}",
              email_cliente: @customer.email,
            })
            @attributes.merge!(customer_attributes(@customer))
            @attributes.merge!(customer_reservation_attributes(@order))
            @attributes.delete(:precio)
          end
        end
      end
    end
  end
end
