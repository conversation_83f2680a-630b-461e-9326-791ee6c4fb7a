module Avenida
  module Emblue
    module Mailer
      module Builder
        class JsonBuilder
          attr_reader :order_id, :attributes

          include Support::CommonAttributes

          def initialize(order_id)
            @order = Mkp::Order.find(order_id)
            @attributes = {}
            initialize_variables
            build_attributes
          end

          def build_json
            {
              email: @email,
              eventName: @event,
              attributes: @attributes,
            }
          end

          private

          def set_customer_variables
            @customer = @order.customer
            @email = @customer.email
            @attributes.merge!({
              nombre: @customer.first_name,
              apellido: @customer.last_name,
            })
          end

          def set_seller_variables
            @shop = @order.suborders.take.shop
            @email = @shop.notify_purchases
            @attributes.merge!({
              nombre_concesionaria: @shop.title.humanize.titleize,
            })
          end

          def set_sucursal_variables
            @office = @order.suborders.take.office
            @email = @office.email
            @attributes.merge!({
              nombre_sucursal: @office.token,
            })
          end

        end
      end
    end
  end
end
