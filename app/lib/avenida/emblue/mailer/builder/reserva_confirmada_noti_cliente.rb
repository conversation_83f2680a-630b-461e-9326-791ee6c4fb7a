module Avenida
  module Emblue
    module Mailer
      module Builder
        class ReservaConfirmadaNotiCliente < JsonBuilder
          private
          def initialize_variables
            @event = "reserva_confirmada_noti_cliente"
            set_customer_variables
          end

          def build_attributes
            @attributes.merge!({
              nombre: @customer.first_name,
              apellido: @customer.last_name,
            })
            @attributes.merge!(customer_reservation_attributes(@order))
          end
        end
      end
    end
  end
end
