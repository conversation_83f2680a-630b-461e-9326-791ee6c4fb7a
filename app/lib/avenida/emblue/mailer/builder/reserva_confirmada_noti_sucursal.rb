module Avenida
  module Emblue
    module Mailer
      module Builder
        class ReservaConfirmadaNotiSucursal < JsonBuilder
          private
          def initialize_variables
            @event = "reserva_confirmada_noti_sucursal"
            @customer = @order.customer
            @info_package = @order.info_package
            @shop = @order.suborders.take.shop
            set_sucursal_variables
          end

          def build_attributes
            @attributes.merge!({
              id_solicitud: @info_package.solicitude_id_movilidad,
              sucursal: @info_package.office_token.to_s,
              email_solicitante: @customer.email,
              cod_reserva_detalle: "#{@order.customer_reservation_purchases.id}-(#{@order.id})",
            })
            @attributes.merge!(customer_reservation_attributes(@order))
            @attributes.merge!(customer_attributes(@customer))
            @attributes.merge!(seller_attributes(@shop))
            @attributes.delete(:moto)
            @attributes.delete(:color)
            @attributes[:cuil] = @attributes.delete(:cuil_cliente)
            @attributes[:telefono_1] = @attributes.delete(:telefono_cliente_1)
            @attributes[:telefono_2] = @attributes.delete(:telefono_cliente_2)
          end
        end
      end
    end
  end
end
