module Avenida
  module Emblue
    module Mailer
      module Builder
        class OrdenCanceladaNotiConcesionario < JsonBuilder
          private
          def initialize_variables
            @event = "orden_cancelada_noti_concesionario"
            set_seller_variables
          end

          def build_attributes
            @attributes.merge!({
              numero_de_orden: @order.id.to_s,
            })
            @attributes.merge!(customer_reservation_attributes(@order))
          end
        end
      end
    end
  end
end
