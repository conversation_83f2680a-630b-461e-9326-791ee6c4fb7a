module Avenida
  module Emblue
    module Mailer
      module Builder
        class ReservaAprobadaNotiCliente < JsonBuilder
          private
          def initialize_variables
            @event = "reserva_aprobada_noti_cliente"
            @shop = @order.suborders.take.shop
            set_customer_variables
          end

          def build_attributes
            @attributes.merge!({
              nombre: @customer.first_name,
              apellido: @customer.last_name,
              monto_prestamo_aprobado: "$ #{@order.customer_reservation_purchases.approved_lended_amount.truncate}",
              numero_de_orden: @order.id.to_s
            })
            @attributes.merge!(customer_reservation_attributes(@order))
            @attributes.merge!(seller_attributes(@shop))
            @attributes.delete(:precio)
          end
        end
      end
    end
  end
end
