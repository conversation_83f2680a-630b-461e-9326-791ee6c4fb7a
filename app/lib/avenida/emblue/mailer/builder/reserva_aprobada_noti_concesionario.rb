module Avenida
  module Emblue
    module Mailer
      module Builder
        class ReservaAprobadaNotiConcesionario < JsonBuilder
          private
          def initialize_variables
            @event = "reserva_aprobada_noti_concesionario"
            @customer = @order.customer
            set_seller_variables
          end

          def build_attributes
            @attributes.merge!({
              monto_prestamo_aprobado: "$ #{@order.customer_reservation_purchases.approved_lended_amount.truncate}",
              email_cliente: @customer.email,
              numero_de_orden: @order.id.to_s
            })
            @attributes.merge!(customer_reservation_attributes(@order))
            @attributes.merge!(customer_attributes(@customer))
            @attributes.delete(:precio)
          end
        end
      end
    end
  end
end
