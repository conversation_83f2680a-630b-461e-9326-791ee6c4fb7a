module Avenida
  module Emblue
    module Mailer
      module Builder
        class OrdenNoAprobadaNotiCliente < JsonBuilder
          private
          def initialize_variables
            @event = "orden_no_aprobada_noti_cliente"
            @shop = @order.suborders.take.shop
            set_customer_variables
          end

          def build_attributes
            @attributes.merge!({
              numero_de_orden: @order.id.to_s,
            })
            @attributes.merge!(customer_reservation_attributes(@order))
          end
        end
      end
    end
  end
end
