module Avenida
  module Emblue
    module Mailer
      module Builder
        class ProductoEntregadoNotiCliente < JsonBuilder
          private
          def initialize_variables
            @event = "producto_entregado_noti_cliente"
            @shop = @order.suborders.take.shop
            set_customer_variables
          end

          def build_attributes
            @attributes.merge!({
              numero_de_orden: @order.id.to_s,
            })
          end
        end
      end
    end
  end
end
