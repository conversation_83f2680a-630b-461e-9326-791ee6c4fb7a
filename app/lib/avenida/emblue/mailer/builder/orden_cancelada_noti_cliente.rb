module Avenida
  module Emblue
    module Mailer
      module Builder
        class OrdenCanceladaNotiCliente < JsonBuilder
          private
          def initialize_variables
            @event = "orden_cancelada_noti_cliente"
            set_customer_variables
          end

          def build_attributes
            @attributes.merge!({
              nombre: @customer.first_name,
              apellido: @customer.last_name,
              numero_de_orden: @order.id.to_s,
            })
            @attributes.merge!(customer_reservation_attributes(@order))
          end
        end
      end
    end
  end
end
