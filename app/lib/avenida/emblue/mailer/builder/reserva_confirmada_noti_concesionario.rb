
module Avenida
  module Emblue
    module Mailer
      module Builder
        class ReservaConfirmadaNotiConcesionario < JsonBuilder
          private
          def initialize_variables
            @event = "reserva_confirmada_noti_concesionario"
            set_seller_variables
          end

          def build_attributes
            @attributes.merge!(customer_reservation_attributes(@order))
          end
        end
      end
    end
  end
end
