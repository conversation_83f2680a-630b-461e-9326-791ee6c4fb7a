module Avenida
  module Emblue
    module Mailer
      module Builder
        class ReservaAprobadaNotiSucursal < JsonBuilder
          private
          def initialize_variables
            @event = "reserva_aprobada_noti_sucursal"
            @customer = @order.customer
            @shop = @order.suborders.take.shop
            @info_package = @order.info_package
            set_sucursal_variables
          end

          def build_attributes
            @attributes.merge!({
              monto_prestamo_aprobado: "$ #{@order.customer_reservation_purchases.approved_lended_amount.truncate}",
              numero_de_orden: @order.id.to_s,
              id_solicitud: @info_package.solicitude_id_movilidad,
              valor_producto_reservado_cliente: @attributes[:valor_producto_reservado],
              sucursal: @info_package.office_token,
              email_cliente: @customer.email,
            })
            @attributes.merge!(customer_attributes(@customer))
            @attributes.merge!(seller_attributes(@shop))
            @attributes.merge!(customer_reservation_attributes(@order))
            @attributes.delete(:precio)
          end
        end
      end
    end
  end
end
