module Avenida
  module Emblue
    module Mailer
      module Builder
        class CancelacionReservaNotiSucursal < JsonBuilder
          private
          def initialize_variables
            @event = "cancelacion_reserva_noti_sucursal"
            @info_package = @order.info_package
            @customer = @order.customer
            @shop = @order.suborders.take.shop
            @product = @order.customer_reservation_purchases.product
            set_sucursal_variables
          end

          def build_attributes
            @attributes.merge!({
              numero_de_reserva: @order.customer_reservation_purchases.id.to_s,
              id_solicitud: @info_package.solicitude_id_movilidad,
              sucursal: @info_package.office_token,
              valor_producto_reservado_cliente: "$ #{@product.price.truncate}",
              email_cliente: @customer.email,
              nombre_concesionaria: @shop.title.humanize.titleize,
            })
            @attributes.merge!(customer_attributes(@customer))
            @attributes.merge!(customer_reservation_attributes(@order))
          end

        end
      end
    end
  end
end


