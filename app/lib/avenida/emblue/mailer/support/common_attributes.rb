module Avenida
  module Emblue
    module Mailer
      module Support
        module CommonAttributes
          def customer_attributes(customer)
            {
              nombre: customer.first_name,
              apellido: customer.last_name,
              cuil_cliente: customer.doc_number,
              telefono_cliente_1: customer.telephone,
              telefono_cliente_2: "",
            }
          end
        
          def seller_attributes(shop)
            warehouse = shop.warehouse
            {
              nombre_concesionaria: shop.title.humanize.titleize,
              cuit_concesionaria: shop.cuit,
              provincia_concesionaria: warehouse.state,
              localidad_concesionaria: warehouse.city,
              domicilio_concesionaria: warehouse.address,
              email_concesionaria: shop.notify_purchases,
              telefono_contacto_concesionaria: warehouse.telephone,
            }
          end

          def customer_reservation_attributes(order)
            reservation = order.customer_reservation_purchases
            suborder = order.suborders.first
            orderItem = Mkp::OrderItem.find_by(suborder_id: suborder.id)
            product = Mkp::Product.find(orderItem.product_id)
            variant = Mkp::Variant.find_by(id: orderItem.variant_id)
            model = product.title.split(" - ")[0]&.humanize&.titleize
            {
              cod_reserva: "#{reservation.id}-(#{order.id})",
              imagen_producto: product.picture.url,
              fecha_hora: reservation.created_at.strftime("%d/%m/%Y - %I:%M"),
              moto: product.title,
              color: variant&.color_hex,
              modelo: model,
              metodo_pago: "Préstamo BNA",
              metodo_envio: "Retiro por concesionaria",
              valor_producto_reservado: "$ #{orderItem.price.truncate}",
              precio: "$ #{product.regular_price.truncate}",
            }
          end
        end
      end
    end
  end
end
