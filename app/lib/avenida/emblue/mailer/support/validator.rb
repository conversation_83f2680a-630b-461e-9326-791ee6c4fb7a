module Avenida
  module Emblue
    module Mailer
      module Support
        module Validator
          EVENT_NAMES = [
            "reserva_confirmada_noti_cliente",
            "reserva_confirmada_noti_concesionario",
            "reserva_confirmada_noti_sucursal",
            "orden_cancelada_noti_concesionario",
            "orden_cancelada_noti_cliente",
            "orden_no_aprobada_noti_concesionario",
            "reserva_aprobada_noti_cliente",
            "reserva_aprobada_noti_concesionario",
            "reserva_aprobada_noti_sucursal",
            "orden_contabilizada_noti_concesionario",
            "producto_entregado_noti_sucursal",
            "producto_entregado_noti_cliente",
            "cancelacion_reserva_noti_sucursal",
            "orden_no_aprobada_noti_cliente"
          ]

          def validate
            validate_event_name
          end

          private

          def validate_event_name
            raise standarderror.new("event name is required") unless @event.present?
            raise standarderror.new("unknown event name") unless EVENT_NAMES.include?(@event)
          end
        end
      end
    end
  end
end
