module Avenida
  module Emblue
    module Mail<PERSON>
      class NotifyEvent

        def initialize(order_id)
          @order_id = order_id
        end

        def notify_new_reservation
          Avenida::Emblue::Mailer::EmailEventSender.new("reserva_confirmada_noti_cliente", @order_id).send_event_email
          Avenida::Emblue::Mailer::EmailEventSender.new("reserva_confirmada_noti_concesionario", @order_id).send_event_email
          Avenida::Emblue::Mailer::EmailEventSender.new("reserva_confirmada_noti_sucursal", @order_id).send_event_email
        end

        def notify_approved_order
          Avenida::Emblue::Mailer::EmailEventSender.new("reserva_aprobada_noti_cliente", @order_id).send_event_email
          Avenida::Emblue::Mailer::EmailEventSender.new("reserva_aprobada_noti_concesionario", @order_id).send_event_email
        end

        def notify_cancelled_order
          Avenida::Emblue::Mailer::EmailEventSender.new("orden_cancelada_noti_concesionario", @order_id).send_event_email
          Avenida::Emblue::Mailer::EmailEventSender.new("orden_cancelada_noti_cliente", @order_id).send_event_email
        end

        def notify_declined_order
          Avenida::Emblue::Mailer::EmailEventSender.new("orden_no_aprobada_noti_concesionario", @order_id).send_event_email
          Avenida::Emblue::Mailer::EmailEventSender.new("orden_no_aprobada_noti_cliente", @order_id).send_event_email
        end

        def notify_billed_order 
          Avenida::Emblue::Mailer::EmailEventSender.new("reserva_aprobada_noti_sucursal", @order_id).send_event_email
        end

        def notify_posted_order
          Avenida::Emblue::Mailer::EmailEventSender.new("orden_contabilizada_noti_concesionario", @order_id).send_event_email
        end

        def notify_delivered_order
          Avenida::Emblue::Mailer::EmailEventSender.new("producto_entregado_noti_sucursal", @order_id).send_event_email
          Avenida::Emblue::Mailer::EmailEventSender.new("producto_entregado_noti_cliente", @order_id).send_event_email
        end

        def notify_pending_cancelation
          Avenida::Emblue::Mailer::EmailEventSender.new("cancelacion_reserva_noti_sucursal", @order_id).send_event_email
        end
      end
    end
  end
end