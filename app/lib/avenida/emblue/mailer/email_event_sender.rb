module Avenida
  module Emblue
    module Mailer
      class EmailEventSender

        include Support::<PERSON><PERSON><PERSON>

        def initialize(event, order_id)
          @order_id = order_id
          @event = event
          validate
        end

        def send_event_email
          send_email(build_json)
        end

        private

        def build_json
          event_class = "Avenida::Emblue::Mailer::Builder::#{@event.camelize}".constantize
          event = event_class.new(@order_id)
          event.send(:build_json)
        end

        def send_email(body_request)
          client = ::AvenidaWrappers::Emblue::Mailer::Client.new({api_endpoint: EMBLUE_MAILER_API_ENDPOINT, auth_token: EMBLUE_MAILER_TOKEN})
          client.event(body_request)
        end
      end
    end
  end
end