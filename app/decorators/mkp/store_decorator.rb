class Mkp::StoreDecorator < Draper::Decorator
  delegate_all

  def logo_shipped_to_user
    'https://statics.avenida.com/logos/isologo-tienda-bna-color.png'
  end

  def logo_delivered_to_customer
    'https://statics.avenida.com/logos/isologo-tienda-bna-color.png'
  end

  def logo_purchase_confirmation
    'https://statics.avenida.com/logos/isologo-tienda-bna-color.png'
  end

  def logo_purchase_cancellation
    'https://statics.avenida.com/logos/isologo-tienda-bna-color.png'
  end
end
