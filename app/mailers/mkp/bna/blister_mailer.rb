module Mkp
  module Bna
    class BlisterMailer < ::Mailer

      def send_policy_to_customer(url, order)
        @order = order
        email = order.customer.email
        @url = url
        @current_store = order.store
        layout = 'v5/mailer/bna/shipped_to_user'
        subject = 'Envío Póliza de Garantía BNA'

        mail(from: from_store, to: email, subject: subject) do |format|
          format.html {render layout: layout}
        end
      end
    end
  end
end
