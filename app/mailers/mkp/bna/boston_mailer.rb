module Mkp
  module Bna
    class BostonMailer < ::Mailer

      def send_policy_to_customer(warranty_pdf, order)
        attachments['Garantia Extendida.pdf'] = {
          mime_type: 'application/pdf',
          content: warranty_pdf
        }

        @order = order
        email = order.customer.email
        @current_store = order.store
        subject = 'Envío Póliza de Garantía BNA'
        layout = 'v5/mailer/bna/shipped_to_user'

        mail(from: from_store, to: email, subject: subject) do |format|
          format.html { render layout: layout }
        end
      end
    end
  end
end
