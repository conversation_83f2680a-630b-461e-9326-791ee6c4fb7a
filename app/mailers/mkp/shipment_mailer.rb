class Mkp::ShipmentMailer < Mailer
  include NetworkExposure
  include CurrencyExposure

  def notify_shipped_to_user(shipment)
    initialize_mailer_variables(shipment)
    layout = Mkp::Store::BNA_STORE_IDS.include?(shipment.order.store.id) ? 'v5/mailer/bna/shipped_to_user' : nil
    send_mail(shipment_subject, @customer.email, layout)
  end

  def notify_pick_up_to_user(shipment)
    initialize_mailer_variables(shipment)
    layout = Mkp::Store::BNA_STORE_IDS.include?(shipment.order.store.id) ? 'v5/mailer/bna/shipped_to_user' : nil
    send_mail(shipment_subject, @customer.email, layout)
  end

  def notify_pick_up_delivered_to_user(shipment)
    initialize_mailer_variables(shipment)
    subject = t('mkp.shipment_mailer.notify_delivered_to_customer.subject')
    layout = Mkp::Store::BNA_STORE_IDS.include?(shipment.order.store.id) ? 'v5/mailer/bna/delivered_to_customer' : nil
    send_mail(subject, @customer.email, layout)
  end

  def notify_delivered_to_customer(shipment)
    initialize_mailer_variables(shipment)
    subject = shipment.order.store.name == 'bancomacro' ? t('mkp.shipment_mailer.notify_delivered_to_customer.subject_macro') : t('mkp.shipment_mailer.notify_delivered_to_customer.subject')
    layout = Mkp::Store::BNA_STORE_IDS.include?(shipment.order.store.id) ? 'v5/mailer/bna/delivered_to_customer' : nil
    send_mail(subject, @customer.email, layout)
  end

  def notify_delivered_to_shop(shipment)
    initialize_mailer_variables(shipment)
    shipment_subject = t('mkp.shipment_mailer.notify_delivered_to_shop.success', title: @order.title, store: @order.store.title.titleize)
    send_mail(shipment_subject, shipment.shop_email)
  end

  def notify_not_delivered_to_customer(shipment)
    initialize_mailer_variables(shipment)
    subject = shipment.order.store.name == 'bancomacro' ? t('mkp.shipment_mailer.notify_not_delivered_to_customer.subject_macro') : t('mkp.shipment_mailer.notify_not_delivered_to_customer.subject')
    send_mail(subject, @customer.email)
  end

  def notify_not_delivered_to_shop(shipment)
    initialize_mailer_variables(shipment)
    subject = shipment.order.store.name == 'bancomacro' ? t('mkp.shipment_mailer.notify_not_delivered_to_shop.subject_macro') : t('mkp.shipment_mailer.notify_not_delivered_to_shop.subject')
    send_mail(subject, shipment.shop_email)
  end

  def current_currency_format
    return super unless Network[@network].thin?
    super + ' '
  end

  private

  def initialize_mailer_variables(shipment)
    @shipment = shipment
    @suborders = @shipment.suborders
    @order_items = @suborders.flat_map(&:items)
    @order = @suborders.first.order
    @current_store  = @order.store
    @logo_ref='mailers/shipping_confirmation.png'

    @orders_being_tracked = orders_being_tracked

    @customer = @order.customer
    @network = @order.network

    I18n.locale = locale_to_use(@network)

    @shipment_title = shipment_title
    @shipment_title_pick_up = shipment_title_pick_up

    set_google_analytics_campaign('user-shipping')
  end

  def send_mail(subject, to, layout = nil)
    if layout.present?
      mail(from: from_store, to: to, subject: subject) do |format|
        format.html {render layout: layout}
      end
    else
      mail(from: from_store, to: to, subject: subject)
    end
  end

  def shipment_subject
    if shipping_all_products?
      all_products_subject
    else
      some_products_subject
    end
  end

  def shipping_all_products?
    @order.total_products == order_items_shipped
  end

  def all_products_subject
    if @shipment.pickable?
      t(
        'mkp.shipment_mailer.notify_pick_up_to_user.complete_shipping.subject',
        title: @order.title, store: @order.store.title.titleize
      )
    else
      t(
        'mkp.shipment_mailer.notify_shipped_to_user.complete_shipping.subject',
        title: @order.title, store: @order.store.title.titleize
      )
    end
  end

  def some_products_subject
    if @shipment.pickable?
      t(
        'mkp.shipment_mailer.notify_pick_up_to_user.partial_shipping.subject',
        order_total_items: @order.total_products,
        order_items: order_items_shipped,
        store: @order.store.title.titleize
      )
    else
      t(
        'mkp.shipment_mailer.notify_shipped_to_user.partial_shipping.subject',
        order_total_items: @order.total_products,
        order_items: order_items_shipped,
        store: @order.store.title.titleize
      )
    end

  end

  def shipment_title
    if @shipment.items.size == 1
      t('mkp.shipment_mailer.notify_shipped_to_user.single', title: @order.title)
    else
      t('mkp.shipment_mailer.notify_shipped_to_user.multiple',
        order_total_items: @order.total_products,
        title: @order.title
      )
    end
  end

  def shipment_title_pick_up
    if @shipment.items.size == 1
      t('mkp.shipment_mailer.notify_pick_up_to_user.single', title: @order.title)
    else
      t('mkp.shipment_mailer.notify_pick_up_to_user.multiple',
        order_total_items: @order.total_products,
        title: @order.title
      )
    end
  end

  def order_items_shipped
    @suborders.to_a.sum(&:total_products)
  end

  def orders_being_tracked
    @suborders.map(&:public_id).join(', ')
  end
end
