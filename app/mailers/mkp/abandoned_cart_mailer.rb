class Mkp::AbandonedCartMailer < ::Mailer
  SUBJECT = "Tu carrito te esta esperando!!".freeze

  def notify_customer(cart)
    @cart = cart
    @customer = cart.customer
    @current_store = cart.store
    @logo_ref='mailers/invoice.png'
    # @coupon = build_coupon
    store_title = @current_store.title.titleize

    mail(from: from_store, to: @customer.email, subject: SUBJECT)
  end


  private

  def build_coupon
    Mkp::Coupon::Network.create do |coupon|
      coupon.code = Mkp::Coupon.random_coupon
      coupon.amount = 150
      coupon.minimum_value = 0
      coupon.percent = 0
      coupon.description = ''
      coupon.starts_at = 1.day.ago
      coupon.expires_at = 7.days.since
      coupon.total_available = 1
      coupon.restrictions = {}
      coupon.network = 'AR'
      coupon.policy = 'value'
      coupon.apply_on_sale = true
      coupon.discount_limit = 0
      coupon.store_id = @current_store.id
    end
  end
end
