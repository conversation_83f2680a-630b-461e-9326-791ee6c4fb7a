class Mailer < ActionMailer::Base
  include AbstractController::Callbacks
  include ::ApplicationHelper

  layout 'v5/mailer/customer'

  DEFAULT_GOOGLE_ANALYTICS_PARAMS = {
    utm_medium: 'email',
    utm_source: 'avenida'
  }.freeze

  default from: 'Avenida.com <<EMAIL>>',
          date: Time.now,
          css: 'v5/mailer/general',
          content_type: 'text/html'

  def default_url_options
    super if GOOGLE_ANALYTICS.blank?
    @url_options ||= super.merge(google_url_options)
  end

  protected

  def google_url_options
    @google_url_options ||= DEFAULT_GOOGLE_ANALYTICS_PARAMS.dup
  end

  def google_analytics_params
    google_url_options.to_param
  end

  def set_google_analytics_campaign(campaign)
    google_url_options.merge! utm_campaign: campaign
  end

  def from_store
    "#{@current_store.title.titleize} <#{@current_store.email}>"
  end

  def forward_only_bna
    @current_store.id == 41 ? ["<EMAIL>"] : []
  end
end
