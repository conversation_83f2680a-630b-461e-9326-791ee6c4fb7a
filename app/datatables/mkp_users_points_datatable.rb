class MkpUsersPointsDatatable < AjaxDatatablesRails::Base
  extend Forwardable

  def_delegators :@view, :link_to, :mail_to, :points_debit_users_points_path, :points_accredit_users_points_path, :users_point_path

  def view_columns
    @view_columns ||= {
      email:        { source: "Mkp::Customer.email", cond: :start_with, searchable: true, orderable: true },
      full_name:    { source: "Mkp::Customer.last_name", cond: :start_with, searchable: true, orderable: true },
      created_at:   { source: "Mkp::Member.created_at" },
      points:       { source: "Mkp::Customer.points", searchable: false, orderable: false },
      debit:        { source: "Mkp::Customer.id" },
      accredit:     { source: "Mkp::Customer.id" },
      transactions: { source: "Mkp::Customer.id" }
    }
  end

  def data
    records.map do |record|
      customer = record.customer
      {
        email: mail_to(customer.email),
        full_name: customer.full_name,
        created_at: customer.created_at.strftime('%Y-%m-%d'),
        points: (customer.points rescue 0),
        debit: link_to('Debit points', points_debit_users_points_path(customer.id), {class: 'button tiny right'}),
        accredit: link_to('Accredit points', points_accredit_users_points_path(customer.id),{class: 'button tiny right'}),
        transactions: link_to('Transactions', users_point_path(customer.id),{class: 'button tiny right'})
      }
    end
  end

  private

  def store_ids
    options[:store_ids].present? ? options[:store_ids] : Mkp::Store.all.ids
  end

  def get_raw_records
    Mkp::Member.includes(customer: [subscription: [:service]]).references(:customer).where(mkp_customers: { store_id: store_ids } )
  end

end
