class AttemptedAnswerDniWorker
	include Sidekiq::Worker
	sidekiq_options queue: `hostname`.strip, retry: false

	def perform(answer_attempted, pioneer_admin_id, valid_store_ids)
		return unless attempted_answer_attempt_valid?(answer_attempted, valid_store_ids)

		3.times do AnswerAttempt::AttemptedAnswer.create!(doc_number: answer_attempted['DNI'],
                                                 store_id: answer_attempted['ID_TIENDA'],
                                                 question_id: 99,
                                                 question: "INGRESADO MANUALMENTE",
                                                 pioneer_admin_id: pioneer_admin_id,
                                                 type: "AnswerAttempt::Equifax")
		end
	end

	private

	def attempted_answer_attempt_valid?(answer_attempted, valid_store_ids)
		answer_attempted['DNI'].present? && answer_attempted['ID_TIENDA'].present? && valid_store_ids.include?(answer_attempted['ID_TIENDA'])
	end
end