require 'faraday'
require 'fileutils'

class EmblueFileUpdater
  include Sidekiq::Worker
  sidekiq_options queue: :solr

  def perform
    emblue_xml
    file_key = 'products.xml'
    file_path = './products.xml'

    client = AvenidaAws::Client.new(AWS_EMBLUE_ACCESS_KEY, AWS_EMBLUE_SECRET_ACCESS_KEY, AWS_EMBLUE_BUCKET_NAME, AWS_EMBLUE_REGION)
    client.update_file(file_key, file_path)
    client.make_file_public(file_key)
  end

  private

  def emblue_xml
    url = 'https://api-bna.avenida.com/api/angular_app/products.xml?store_id=41'

    connection = Faraday.new(url: url) do |faraday|
      faraday.adapter Faraday.default_adapter
      faraday.options.timeout = 360
      faraday.options.open_timeout = 5
    end

    response = connection.get do |request|
      request.headers['Api-key'] = ANGULAR_API_KEY
    end

    File.open('products.xml', 'w:ASCII-8BIT') { |file| file.write(response.body) }
  end
end
