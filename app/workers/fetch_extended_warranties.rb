class FetchExtendedWarranties
  include Sidekiq::Worker
  sidekiq_options retry: false

  def perform(category_ids)
    return unless category_ids.any?

    body_request = Ochenta::Request.new(habilitadas: true, category_ids: category_ids).build
    response = Ochenta::Coverages::WarrantiesService.call(body_request: body_request)

    return unless response&.success?

    Ochenta::ProductWarranty.new(
      warranties: response.payload,
      shop_id: OCHENTA_SHOP_ID,
      category_id: OCHENTA_CATEGORY_ID,
      manufacturer_id: OCHENTA_MANUFACTURER_ID
    ).create_or_update
  end
end
