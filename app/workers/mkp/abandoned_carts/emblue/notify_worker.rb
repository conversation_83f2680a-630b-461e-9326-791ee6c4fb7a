module Mkp
  module AbandonedCarts
    module Emblue
      class NotifyWorker
        include Sidekiq::Worker
        sidekiq_options queue: 'abandoned_carts', retry: false

        def perform(cart_id)
          cart = Mkp::Cart.find(cart_id)

          body_request = Avenida::Emblue::BodyRequest::Builder.new(cart: cart).call
          send_event_for(body_request) if body_request.success?
        end

        private

        def send_event_for(body_request)
          client = AvenidaWrappers::Emblue::Client.new({
            auth_token: EMBLUE_TOKEN,
            api_endpoint: EMBLUE_API_ENDPOINT
          })
          client.event(body_request.payload)
        end
      end
    end
  end
end
