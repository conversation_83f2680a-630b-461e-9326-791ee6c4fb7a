# module Mkp
#   class ReindexWorker
#     include Sidekiq::Worker
#     sidekiq_options queue: :solr

#     def reindexes_logger
#       @@reindexes_logger ||= Logger.new("#{Rails.root}/log/smart_reindex.log")
#     end

#     def perform(shop_id)
#       products = Mkp::Product.where(shop_id: shop_id)
#       variants = products.flat_map(&:variants)
#       reindexes_logger.info("Update and Save Products and Variants from shop_id: #{shop_id}")

#       products.each { |p| p.updated_at = Time.now; p.save }

#       variants.each(&:save)
#     end
#   end
# end
