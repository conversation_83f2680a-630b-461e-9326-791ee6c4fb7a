module Mkp
  class Warranties
    class WarrantyRequestWorker
      include Sidekiq::Worker
      sidekiq_options queue: :warranties, retry: true

      def perform(order_id, klass)
        order = Mkp::Order.find(order_id)
        shipment = ::Mkp::Shipment.where(suborder: order.suborders.detect { |s| !s.warranty? }).first
        warranty_suborder = order.suborders.find(&:warranty?)
        warranty_product = warranty_suborder.items.first
        associated_product = warranty_suborder.order.data['associated_variant'].product

        "Avenida::#{klass}::PolicyRequest".constantize.call(
          order: order,
          shipment: shipment,
          warranty_suborder: warranty_suborder,
          warranty_product: warranty_product,
          associated_product: associated_product
        )
      end
    end
  end
end
