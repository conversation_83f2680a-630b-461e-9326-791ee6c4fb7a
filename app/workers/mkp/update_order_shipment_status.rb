module Mkp
  class UpdateOrderShipmentStatus
    include Sidekiq::Worker

    def perform(shipment_id, status_number)
      shipment = Mkp::Shipment.find(shipment_id)

      shipment&.update(status: mapper(status_number)) if shipment&.pickable_and_not_delivered?
    end

    private

    def mapper(status_number)
      case status_number
      when '1'
        'delivered'
      when '2'
        'ready_to_pick_up'
      else
        Rails.logger.info 'Parámetro inválido'
      end
    end
  end
end
