module Mkp
  module Bna
    class RemoveCustomerReservationWorker
      def perform(order_id)
        record = CustomerReservationPurchases.find_by(mkp_order_id: order_id)
        if record
          record.destroy!
        else
          Rails.logger.warn("No se encontró una reserva para la orden con ID #{order_id}")
        end
      end

      def remove_many(cuils)
        cuils.each do |cuil|
          perform(cuil)
        end
      end
    end
  end
end
