;(function(){
  var View = gp.App.View.extend({
    t: I18n.withScope('js.v5.partials.cart'),
    itemTemplate: null,
    $list: null,

    events: {
      'click .item .i-icon-trash-bin': 'removeItem',
      'click .actions .checkout:not(.disabled)': 'goToCheckout',
      'click .actions .continue:not(.disabled)': 'hide',
      'click .empty-msg': 'hide'
    },

    initialize: function(){
      _.bindAll(
        this,
        'onC',
        'show',
        'reset',
        'add',
        'remove',
        'update',
        'updateTotal',
        'updateQuantities'
      )

      this.$list = this.$('.list')
      this.itemTemplate = this.template('item-template')

      this.updateDebounced = _.debounce(this.update, 10)

      this.onC('reset', this.reset)
      this.onC('add', this.add)
      this.onC('remove', this.remove)
      this.onC('cart:change', this.updateDebounced)

      this.reset()
      this.$el.removeClass('loading')
    },

    onC: function(event, callback){
      return this.listenTo(this.collection, event, callback)
    },

    show: function(){
      var self = this
      $.magnificPopup.open({
        showCloseBtn: true,
        mainClass: 'cart-dropdown-popup',
        items: {
          src: self.$el,
          type: 'inline'
        },
        callbacks: {
          open: function(){
            gp.Helpers.lockScroll.lock(self.$list)
          },
          close: gp.Helpers.lockScroll.unlock
        }
      })
    },

    hide: function(){
      $.magnificPopup.close()
    },

    reset: function(){
      var that = this
      this.$list.html('')
      this.collection.each(function(n){ that.add(n) })
      this.updateTotal()
    },

    add: function(m){
      var obj = m.toJSON()
      var that = this
      var attrs = []
      if( obj.properties.color && obj.properties.color.name ){
        var cTitle = this.t('color')
        var cName = obj.properties.color.name
        attrs.push('<div>'+cTitle+': '+cName+'</div>')
      }

      _.each([
        'size',
        'dimensions',
        'hardness',
        'length',
        'material',
        'percentage',
        'payment_method',
        'period',
        'external',
        'articulo',
        'coef',
        'date'
      ], function(v){
        if( obj.properties[v] ){
          var name;
          if(obj.product.available_properties_names[v] !== undefined && obj.product.available_properties_names[v] !== "") {
            name = obj.product.available_properties_names[v]
          } else if(gp.compatibility_properties && gp.compatibility_properties[v] !== undefined) {
            name = gp.compatibility_properties[v]
          } else {
            name = that.t(''+v)
          }

          attrs.push('<div>'+name+': '+obj.properties[v]+'</div>')
        }
      });


      // _.each([
      //   'size',
      //   'dimensions',
      //   'hardness',
      //   'length',
      //   'material'
      // ], function(v){
      //   if( obj.properties[v] ){
      //     attrs.push('<div>'+that.t(''+v)+': '+obj.properties[v]+'</div>')
      //   }
      // })
      obj.attrs = attrs.join('')
      obj.quantity = this.collection.cartCountOf(m)
      obj.price = gp.cart.cartTotalOf(m)
      var pic = m.picture()
      obj.thumb = pic && pic.thumb

      var $item = this.itemTemplate(obj)
      this.$list.append($item)
      this.$el.removeClass('empty')
    },

    remove: function(m){
      this.$list.find('[data-item-id="'+m.id+'"]').remove()
    },

    update: function(){
      this.updateTotal()
      this.updateQuantities()
    },

    removeItem: function(e){
      var $el = $(e.currentTarget).parent()
      var model = this.collection.get($el.attr('data-item-id'))
      var item = _(this.collection.items).select(function(i) { return i.variant_id == model.id })[0]
      this.collection.remove(model)
      if( !this.collection.length ){
        this.$el.addClass('empty')
      }
      gp.pubSub.trigger('cart:remove', item, model)
    },

    $total: null,
    updateTotal: function(){
      if( !this.$total ){
        this.$total = this.$('.total .value')
      }
      this.$total.html(gp.cart.cartTotal())
    },

    updateQuantities: function(){
      var that = this
      _.each(this.collection.items, function(item){
        var $item = that.$list.find('[data-item-id="'+item.variant_id+'"]')
        $item.find('.qty .val').html(item.quantity)
      })
    },

    goToCheckout: function(e){
      if( gp.cart.items.length > 0 ){
        var $el = $(e.currentTarget)
        $el.parent().children('a').addClass('disabled')
        $el.html(this.t('going_to_checkout'))
        this.showLoader()
      } else {
        return e.stop()
      }
    }
  })

  var V = gp.Views['partials/cart'] = {}

  var view

  V.init = _.once(function() {
    view = new View({
     collection: gp.cart,
     el: $('#cart .cart')
    })
  })

  V.open = function() {
    V.init()
    view.show()
  }

  V.close = function() {
    view.hide()
  }
})()

String.prototype.capitalize = function() {
  return this.charAt(0).toUpperCase() + this.slice(1);
};