// Set box-sizing globally to handle padding and border widths
*, *:before, *:after {
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}

// <PERSON>rid Defaults to get images and embeds to work properly
img, object, embed {
  max-width: 100%; height: auto;
}

object, embed {
  height: 100%;
}

img {
  display: inline-block;
  vertical-align: middle;
  -ms-interpolation-mode: bicubic;
}

.left          { float: left !important; }
.right         { float: right !important; }
.full-width    { width: 100% !important; }
.text-left     { text-align: left !important; }
.text-right    { text-align: right !important; }
.text-center   { text-align: center !important; }
.text-justify  { text-align: justify !important; }
.bold          { font-weight: bold !important; }
.hide, .hidden { display: none !important; }
.clear         { clear: both !important; }

@mixin with-middle-text {
  display: flex;
  justify-content: center;
  align-content: center;
  flex-direction: column;
}

.text-middle { @include with-middle-text; }

.cf, .clearfix {
  @include clearfix();
}

.full-link {
  position: absolute;
  left: 0; top: 0;
  width: 100%; height: 100%;
}

.hide-text {
  text-indent: 100%;
  white-space: nowrap;
  overflow: hidden;
  font-size: 0;
  color: transparent;
}

@mixin ellipsis {
  padding-right: 0.4em;
  white-space: nowrap;
  overflow: hidden !important;
  @include prefixer(text-overflow, ellipsis, o ms spec);
  -moz-binding: url("/xml/ellipsis.xml#ellipsis");
}

.ellipsis {
  @include ellipsis;
}

@mixin multiline-ellipsis($font-size: 12px,
                          $line-height: 1.6,
                          $lines-to-show: 5) {
  display: block; /* Fallback for non-webkit */
  display: -webkit-box;
  height: $font-size * $line-height * $lines-to-show; /* Fallback for non-webkit */
  font-size: $font-size;
  line-height: $line-height;
  -webkit-line-clamp: $lines-to-show;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

@mixin unselectable {
  @include user-select(none);
  -webkit-user-drag: none;
  user-drag: none;
  cursor: default;
}

.unselectable {
  @include unselectable;
}

.preload-image {
  display: inline;
  width: 0;
  height: 0;
  background-repeat: no-repeat;
  background-position: -100% -100%;
}

@mixin reset-button {
  width: auto;
  border: 0 none;
  margin: 0;
  padding: 0;
  background: transparent;
  overflow: visible;
  /* Stupid IE */
  color: $body-font-color;
  font-family: $body-font-family;
  /* #vertical-align: middle; /* IE6,IE7 */
  &::-moz-focus-inner {
    border: 0;
    padding: 0;
  }
  &:hover, &:active, &:focus {
    outline: 0;
  }
}
.reset-button {
  @include reset-button;
}

.with-scrollbar {
  ::-webkit-scrollbar {
    width: 5px;
    height: 10px;
  }
  ::-webkit-scrollbar-button:start:decrement,
  ::-webkit-scrollbar-button:end:increment {
    height: 3px;
    display: block;
    background-color: transparent;
  }
  ::-webkit-scrollbar-track-piece  {
    background-color: transparent;
    border-radius: 6px;
  }
  ::-webkit-scrollbar-thumb:vertical {
    height: 50px;
    background-color: #D9D9D9;
    border: 1px solid #D9D9D9;
    border-radius: 6px;
  }
}

.noscroll { overflow: hidden !important; }
.fullsize {
  width: 100% !important;
  height: 100% !important;
}

/**
* Mixin to mimic CSS3 rem length unit
* @param $em: desired rem value in ems
* @param $local-base: current font size in ems that you have in the scope you're working
* some help: http://snook.ca/archives/html_and_css/font-size-with-rem
*/
@function rem($em, $local-base) {
  @return round((($em / $local-base) * 1em) * 1000) / 1000;
}

@mixin background-color-with-transparency($color, $alpha:1) {
  $rgba: rgba($color, $alpha);
  $ie-hex-str: ie-hex-str($rgba);
  background-color: $color;
  background-color: $rgba;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#{$ie-hex-str},endColorstr=#{$ie-hex-str});
  -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#{$ie-hex-str}, endColorstr=#{$ie-hex-str})";
}

@mixin background-image-data($image-url) {
  *background-image: image-url($image-url);
  background-image: asset-data-url($image-url);
}

@mixin filter($var) {
  @include prefixer(filter, $var, webkit moz ms o spec);
}

@mixin opacity($value: 1) {
  opacity: $value;
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity="$value * 100")";
  filter: alpha(opacity= $value * 100 );
  zoom: 1;
}

@mixin calc($property, $value) {
  #{$property}: -webkit-calc(#{$value});
  #{$property}:         calc(#{$value});
}

@mixin responsive-width($fallback, $percentage, $pixels) {
  width: #{$fallback};
  width: -webkit-calc(#{$percentage} - #{$pixels});
  width: calc(#{$percentage} - #{$pixels});
}

/**
* Mixin to apply a style only when the html has the .no-touch class
*/
@mixin no-touch {
  @include prepend(".no-touch") {
    @content;
  }
}

@mixin prepend($selector) {
  #{$selector} & {
    @content;
  }
}
