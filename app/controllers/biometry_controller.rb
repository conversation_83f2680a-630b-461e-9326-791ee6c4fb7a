class BiometryController < ApplicationController
  require 'openssl'
  require 'base64'
  
  def biometry_response
    externalID = params[:externalID] || params[:externaltrxid]
    decision = params[:decision]

    externalTokens = externalID.split('*')
    cart = Mkp::Cart.find_by(purchase_id: externalTokens[0])
    loan = false
  
    if decision == 'HIT'
      cart.biometry_id = params[:idtx]
      cart.save
      loan = true
    end
  
    # 🧠 Payload generado con datos reales
    payload = {
      auth_token: externalTokens[1],
      purchase_id: cart.purchase_id,
      items: cart.items.map do |item|
        {
          variant_id: item.variant_id,
          quantity: item.quantity,
          points: item.try(:points)
        }
      end,
      loan: {
        applyLoan: true,
        isLoan: loan
      }
    }
  
    # 🔐 Encriptación con AES-256-CBC usando 'ENCRYPT_KEY'
    key = ENCRYPT_KEY
    raise "ENCRYPT_KEY no está definido" unless key.present?
  
    cipher = OpenSSL::Cipher.new('aes-256-cbc')
    cipher.encrypt
    cipher.key = Digest::SHA256.digest(key)
    cipher.iv = iv = OpenSSL::Random.random_bytes(16)
  
    encrypted_data = cipher.update(payload.to_json) + cipher.final
    full_data = iv + encrypted_data
    encrypted_payload = Base64.strict_encode64(full_data)
    # 🔁 Redirigir al checkout con el token encriptado
    redirect_to "#{QR_FRONT_DECISION_BASE_URL}?#{URI.encode_www_form(loan: encrypted_payload)}"
  end
end
