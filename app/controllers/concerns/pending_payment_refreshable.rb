module PendingPaymentRefreshable
  class NoStockError < StandardError; end;
  extend ActiveSupport::Concern

  def confirm_pending_payment(payment)
    if payment.collected?
      confirm_pending_order(payment.sale_item)
    end
  end

  def cancel_pending_order(order)
    payments = order.payments # order.payments.where.not(gateway: 'VisaPuntos')
    payments.each(&:cancel!)
    shipment_cancel(order)
  end

  def refresh_payment!(payment)
    old_status = payment.status
    order = payment.sale_item
    payment.refresh

    return payment.status if (payment.status != old_status)
    nil
  end

  private

  def confirm_pending_order(order)
    check_stock!(order) unless PENDING_PAYMENTS_AS_CONFIRMED
    process_after_checkout(order)
  rescue NoStockError => e
    #cancel all payments visa & modo
    cancel_pending_order(order)
    raise Api::AngularApp::V1::CheckoutController::ProductAvailabilityException, e.message, []
  end

  def process_after_checkout(order)
    shipment_confirm(order)
    unless PENDING_PAYMENTS_AS_CONFIRMED
      ::Mkp::Integration::PuntosYPremios.new.redemption(order, nil) if order.store.name == 'bancomacro'
      complete_after_checkout(order)
    end
  end

  def check_stock!(order)
    service = Checkout::ValidateProductAvailability.new(order.items, order.store)
    service.perform
    raise NoStockError, service.error, [] unless service.valid
  end

  def shipment_confirm(order)
    shipments_status_change(order, 'unfulfilled')
  end

  def shipment_cancel(order)
    shipments_status_change(order, 'cancelled')
  end

  def complete_after_checkout(order)
    ::Mkp::PurchaseProcessor.run_hooks_update_stock(order)
    ::Mkp::PurchaseProcessor.run_hooks_after_confirmation(order)

    if defined?(Lux::IntegrationSaleNotificationWorker)
      # extracted from run_integration_services method
      order.suborders.each do |suborder|
        Lux::IntegrationSaleNotificationWorker.perform_async(suborder.shop.id, suborder.id, "Mercadolibre")
      end
    end
    ###
  end

  def shipments_status_change(order, new_status)
    order.shipments.each do |shipment|
      ::Mkp::StatusChange::EntityStatusManage.status_change(shipment, new_status)
    end
  end

end
