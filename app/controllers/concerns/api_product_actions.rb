module ApiProductActions
  extend ActiveSupport::Concern

  private

  def post_create_actions(product_attr_builder)
    @entity.variants.each do |variant|
      variant.gp_sku = nil
      variant.send(:build_gp_sku, [])
      variant.save!
    end
    post_general_actions(product_attr_builder)
  end

  def post_update_actions(product_attr_builder)
    ::Mkp::Variant.where(id: @variants_to_delete).destroy_all if @variants_to_delete.present?
    ::Mkp::Package.where(id: @packages_to_delete).destroy_all if @packages_to_delete.present?
    post_general_actions(product_attr_builder)
  end

  def post_general_actions(product_attr_builder)
    product_attr_builder.relate_images(@entity.id)
    @entity.recreate_variants_visibility
    @entity.set_approved_for_stores(true)
  end

  def initialize_variants_to_delete(product_attr_builder)
    return unless product_attr_builder.result[:variants_attributes]

    sku_to_update = product_attr_builder.result[:variants_attributes].map {|each| each[:sku]}
    if API_DELETE_PRODUCT_ATTRIBUTES
      #comportamiento anterior
      @variants_to_delete = (@entity.variants.select {|variant| (sku_to_update.include? variant.sku) || variant.sku.nil? }).map(&:id)
    end
  end

  def initialize_package_to_delete(product_attr_builder)
    return unless product_attr_builder.result[:packages_attributes]

    if API_DELETE_PRODUCT_ATTRIBUTES
      @packages_to_delete = @entity.packages.map(&:id) #comportamiento anterior
    else
      packages_to_update = product_attr_builder.result[:packages_attributes].map {|pkg| pkg[:id] }.compact
      @packages_to_delete = @entity.packages.where.not(id: packages_to_update).pluck(:id).presence
    end
  end
end
