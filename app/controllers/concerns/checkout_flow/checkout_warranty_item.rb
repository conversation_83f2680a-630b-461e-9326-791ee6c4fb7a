module CheckoutFlow
  class CheckoutWarrantyItem < Struct.new(:variant, :quantity, :modified_price, :points)
    extend Forwardable
    attr_accessor :variant, :quantity, :points, :payment, :modified_price

    def initialize(variant, quantity, modified_price, points = 0)
      @variant = variant
      @quantity = quantity
      @points = points
      @modified_price = modified_price
      super
    end

    def_delegators :@variant, :currency, :product, :shop
    def_delegator :@variant, :title, :name

    alias_method :title, :name

    def hs_tariff_number
      ''
    end

    def on_sale?
      false
    end

    def price
      modified_price
    end

    def sale_price
      Mkp::ProductSaleDetector.calculate(product) if on_sale?
    end

    def points_by_store(store_id)
      product.points_by_store(store_id)
    end

    def total
      quantity * ( on_sale? ? sale_price : price )
    end

    def total_with_points_discount(store)
      total = self.total
      if points > 0 && store.present?
        total -= variant.product.points_value_by_store(store, points)
      end
      total
    end

    def tax(state)
      if shop.charge_tax_to?(state)
        shop.tax_rates[state][:rate].to_f * total / 100.0
      end || 0
    end
  end
end
