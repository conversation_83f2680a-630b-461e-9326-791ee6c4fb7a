module Mkp
  class CheckoutController < ActionController::Base
    force_ssl if SSL_ENABLED

    protect_from_forgery with: :exception

    layout 'mkp/checkout'

    # Concerns required by the before_filter explicitly set by this controller.
    include NetworkAffiliates
    include CheckoutFlow
    include NetworkExposure
    include ReturnLocationStorer
    include SetLocale
    include UserSessionator
    include AuthorizeTodoPago
    include CheckoutProcessable

    # Concerns required by dependencies, actions, views or helpers.
    include RenderProperly

    # Filters provided by the included concerns.
    prepend_before_filter :set_network

    before_filter :setup_flow_id, only: :init
    before_filter :ensure_pickit_quotation, only: :buy

    #before_filter :verify_flow_id, except: [:init, :stompedit]

    before_filter :set_country_cookie,
                  :set_locale,
                  :store_return_to_location,
                  :load_new_session, except: :init

    before_filter :find_current_store
    before_filter :set_current_checkout_session_and_cart, except: [:init, :stompedit]

    # Filters provided in this class.
    before_filter :ensure_checkout_cart_has_items, only: [:ride]

    # Helpers.
    include CheckoutHelper
    helper :layout

    def init
      redirect_to mkp_checkout_ride_path(
        @network.downcase, { fid: @checkout_flow_id }
      )
    end

    def ride
      setup_customer_ride
      setup_items
      setup_available_payment_gateways

      render_properly :ride
    end

    def buy
      @checkout_cart.address_id = params[:address_id] if params[:address_id].present?
      override_choosen_delivery_options(params[:pickit_quotation_id]) if params[:is_pickit] == "1"
      if @checkout_cart.balance_due? && @checkout_cart.choosen_delivery_options.present?
        process_payment_params if params[:payment].present?
        collect_payment
      end

      # Todo
      # We need to find the real solution
      # It seems like we're not entering to the todopago gateway,
      # so, we're going to force that
      force_todopago

      if ( @checkout_cart.balance_due? && (@payment.blank? || @payment.cancelled?) ) || @checkout_cart.choosen_delivery_options.nil?
        error = @payment && @payment.cancelled? ? get_payment_error_message : missing_payment_error_message
        error = "Debes elegir opciones de envío" if @checkout_cart.choosen_delivery_options.nil?

        if @payment.get_error[:status] == "gateway_error"
          ExceptionNotifier.notify_exception(::Avenida::Payments::FailedPaymentAttemptException.new, data: {
            error: error,
            payment: @payment ? @payment : '',
            checkout_cart: @checkout_cart,
            params: params.dup,
            session: session.dup
          })
        end

        flash[:alert] = error
        redirect_to mkp_checkout_ride_path(
          @network.downcase, { fid: @checkout_flow_id }
        )
      else
        create_order(@current_store.id)
        create_and_associate_shipments
        # confirm_pickit_order(params[:pickit_quotation_id]) if params[:is_pickit] == "1"
        wipe_session!
        allow_google_analytics_to_track_conversion

        redirect_to mkp_checkout_stompedit_path(
          @network.downcase, { purchase_id: @order.purchase_id }
        )
      end
    end

    def force_todopago
      return if @payment.present? || params[:payment][:gateway] != "todopago"

      if params[:payment][:request_key].present? && params[:payment][:operation_id].present?
        @payment = Mkp::Payment.create do |object|
          object.status = 'pending'
          object.gateway = 'Todopago'
          object.gateway_data = {request_key: params[:payment][:request_key], public_key: ""}
          object.gateway_object_id = params[:payment][:operation_id]
          object.payment_method = "creditcard"
        end
      end
    rescue
      return
    end

    def stompedit
      @order = Order.find_by_purchase_id(params[:purchase_id])

      unless @order.present?
        redirect_to mkp_catalog_root_path(network: @network.downcase) and return
      end

      setup_featured_brands

      setup_ticket_download_link if order_payed_with_ticket?

      render_properly :stompedit
    end

    private

    def setup_customer_ride
      if current_customer
        @current_customer = current_customer
        @addresses = possible_destinations(current_customer.addresses)
        @address_form = Address.build_from(current_customer)

        cart = @current_customer.get_or_create_regular_cart(@network)
        cart.set_checkout_status!
      else
        @address_form = Address.new
      end

      @checkout_cart.remove_coupon
    end

    def setup_items
      @checkout_items = @checkout_cart.items.map do |item|
        CartItemStruct.new(*item.values)
      end
    end

    def setup_available_countries
      @countries = @checkout_cart.countries.join(',')
    end

    def possible_destinations(addresses)
      allowed_countries_to_ship = GeoConstants::Countries.get_list(@network)
      addresses.select { |address| allowed_countries_to_ship.key?(address.country) }
    end

    def setup_available_payment_gateways
      default_gateway = Setting.first
      @payment_gateways = Avenida::Payments.for(@network).select do |gateway|
        gateway.downcase == default_gateway.value
      end
    end

    def setup_ticket_download_link
      @ticket = @order.payment.gateway_data[:transaction_details][:external_resource_url]
    end

    def order_payed_with_ticket?
      return false unless @order.payment.present?
      @order.payment.gateway_data[:payment_type_id] == 'ticket'
    end

    def wipe_session!
      remove_affiliate_cookie(@order.affiliate_type) unless @order.affiliate_type.blank?
      remove_bought_items_from_cookies(@checkout_cart)
      session.delete(@uuid)
    end

    def setup_featured_brands
      successfull_conditions = {
        group: 'shop_id',
        select: 'shop_id, COUNT(*) as count',
        order: 'count desc'
      }

      successfull_shops = Suborder.joins(:shop)
                                  .where('mkp_shops.network = ?', @network)
                                  .all(successfull_conditions)

      @featured_brands = successfull_shops.take(4).flat_map(&:shop).map(&:brand)
    end

    def ensure_checkout_cart_has_items
      if @checkout_cart.items.empty?
        session.delete(@uuid)

        flash[:error] = I18n.t("v5.controllers.checkout.errors.no_items")

        redirect_to mkp_catalog_root_path(network: @network.downcase) and return
      end
    end

    def override_choosen_delivery_options(quotation_id)
      request_body = {
        cotizacionId: quotation_id,
        tokenId: PICKIT_TOKEN_ID
      }
      response = Gateways::Shipments::Pickit.get_pickup_point_information(request_body.to_json)
      create_pickup_delivery_option(response, quotation_id)
    end

    def create_pickup_delivery_option(location_point, quotation_id)
      old_delivery_option = @checkout_cart.choosen_delivery_options.first
      delivery_option = Mkp::Shipping::DeliveryOption::Pickup.build(location_point)

      result = {
        shop_title: @checkout_cart.items.first[:product][:shop][:title],
        shop_id: @checkout_cart.items.first[:product][:shop][:id],
        delivery_speed: "3 a 5 días",
        price: location_point[:amount],
        bonificated_price: 0.0,
        carrier: "Pickit",
        delivery_option: delivery_option,
        is_pickup: true,
        quotation_id: quotation_id
      }

      @checkout_cart.delivery_options = {
        pickup: [result]
      }
    end

    def confirm_pickit_order(quotation_id)
      PurchaseProcessor.confirm_pickit_order(quotation_id, @order)
    end

    def allow_google_analytics_to_track_conversion
      session[:track_conversion] = true
    end

    def ensure_pickit_quotation
      if params[:is_pickit] == "1" && (not params[:pickit_quotation_id].present?)
        raise "Pickit quotation not set"
      end
    end

    def find_current_store
      @current_store = Mkp::Store.find_by_name("avenida")
    end
  end
end
