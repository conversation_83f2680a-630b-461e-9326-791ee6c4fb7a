class Api::V2::Tdd::RegistersController < Api::V2::ApplicationController
  before_filter :find_customer, only: [:update, :show, :destroy]

  def create
    registration = ::Tdd::Registration.new(customer_params, payment_params, @current_store)
    if registration.save
      render json: { message: "Register created successfully" }, status: :created
    else
      render json: { message: registration.errors.join(',') }, status: 422
    end
  end

  def show
    render json: data_response, status: :ok
  end

  def update
    if @customer.update(customer_params)
      render json: data_response, status: :ok
    else
      render json: { message: @customer.errors.full_messages }, status: :not_acceptable
    end
   rescue ActionController::ParameterMissing => e
     render json: { mesagge: e.message }, status: :bad_request
  end

  def destroy
    return record_not_found if @customer.nil?
    if @customer&.destroy
      @customer&.subscription&.payments&.delete_all
      @customer&.member&.cards&.delete_all
      render json: { mesagge: "Resource destroyed" }, status: :ok
    else
      render json: { mesagge: "Resource not found" }, status: :not_found
    end
  end

  private
  def data_response
    response = { attributes: @customer.as_json }
    response[:attributes][:member_attributes] = @customer.member.as_json
    response[:attributes][:cards_attributes] = @customer.member.cards.first.as_json(:only => [:id, :card_type, :brand])
    response[:attributes][:addresses_attributes] = @customer.addresses.as_json
    response[:attributes][:service_attributes] = @customer&.subscription&.service&.attributes&.slice('id', 'amount')&.as_json
    response
  end

  def customer_params
    params.require(:attributes)
          .permit(
            :first_name, :last_name, :doc_type, :doc_number, :birthday_at,
            :gender, :telephone, :email, :password, :password_confirmation,
            member_attributes: [ :id, :nationality, :mobile, :origin, :enabled, :token ],
            subscription_attributes: [:service_id],
            addresses_attributes: [ :id, :internal_country_address,
                                    :first_name, :last_name, :telephone,
                                    :address, :address_2, :city,
                                    :state, :country, :zip, :street_number,
                                    :floor, :dpto, :tower, :body, :lateral_street_1,
                                    :lateral_street_2, :county, :country_club
                                  ]
          )
  end

  def find_customer
    @customer = @current_store.customers.not_deleted.find_by_doc_number(params[:id]) || @current_store.customers.not_deleted.find_by_id(params[:id])
    record_not_found unless @customer.present?
  end

  def payment_params
     params.require(:attributes).permit(payment_attributes: [:amount, :status, :billable])[:payment_attributes]
  end

  def service_params
     params.require(:attributes).permit(service: :id)[:service]
  end

  def set_generated_password
    return if customer_params[:password].present?
    @password = Devise.friendly_token.first(12)
    @customer.assign_attributes(password: @password, password_confirmation: @password)
  end
end
