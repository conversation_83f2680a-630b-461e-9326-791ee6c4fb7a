class Api::V2::Tdd::MembersController < Api::V2::ApplicationController

  protect_from_forgery with: :exception

  before_filter :find_customer, only: [:update]

  def update
    return record_not_found if @customer.nil? && params[:service_id] && Service.exists?(ERB::Util.html_escape(params[:service_id]))
    @customer.subscription.update(service_id: params[:service_id])
    render json: { member_id: @customer.member.id, service_id: params[:service_id] } , status: :ok
  end

  private

  def find_customer
    @customer = Mkp::Customer.not_deleted.includes(:member).find_by(mkp_members: params.slice(:id))
  end
end
