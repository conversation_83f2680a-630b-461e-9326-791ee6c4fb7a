class Api::ApplicationController < ::ApplicationController
  protect_from_forgery with: :null_session
  skip_before_action  :verify_authenticity_token

  before_action :validate_network
  before_action :validate_token

  rescue_from Exception, with: :notify_exception

  private

  def validate_network
    @network = "AR"
    params[:network] = @network.downcase
  end

  def validate_token
    api_key = request.headers['Api-Key']
    @current_store = Mkp::Store.active.find_by_token(api_key)
    return unauthenticated! if @current_store.nil?
  end

  def unauthenticated!
    response.headers['WWW-Authenticate'] = "Token realm=Application"
    render json: { error: 'Bad credentials' }, status: 401
  end

  def record_not_found
    render json: { error: 'Record not found' }, status: 404
  end

  def notify_exception(exception)
    if exception.is_a?(ActiveRecord::RecordNotFound)
      record_not_found
    elsif exception.is_a?(RSolr::Error::ConnectionRefused)
      self.response_body = nil
      render json: { error: 'Service Solr not found' }, status: 500
    else
      Rails.logger.info "---------- Exception JESUS -----------"
      Rails.logger.info "--------------------------------------"
      Rails.logger.info "------------ #{exception} ------------"
      Rails.logger.info "--------------------------------------"
      Rails.logger.info "#{exception.backtrace.join("\n")}"
      #Stores::ErrorMailer.throw_error_in_store(exception.to_s, exception.backtrace, @current_store).deliver_later
      render json: {
          error: exception.respond_to?(:message) ? exception.message : "An error has ocurred"
        }, status: 400
    end
  end
end
