module Api
  module V1
    class CategoriesController < EntitiesController
      before_action :find_store

      private

      def entity_class
        ::Mkp::Category
      end

      def available_entities
        @entities = @store.nil? ? entity_class.all : @store.categories
        @entities = @entities.where(ancestry_depth: params[:ancestry_depth]) if params[:ancestry_depth].present?
        @entities = @entities.active if params[:active] == 'true'
        @entities
      end

      def find_store
        @store = ::Mkp::Store.where(id: params[:store_id]).take
      end
    end
  end
end
