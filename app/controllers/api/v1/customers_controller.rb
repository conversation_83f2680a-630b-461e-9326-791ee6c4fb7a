module Api
  module V1
    class CustomersController < EntitiesController
      skip_before_action :authenticate_request

      def check_credentials
        return render json: { success: false, message: 'Missing params' }, status: '422' if params[:email].nil? || params[:password].nil?

        email = params[:email].to_s.downcase
        password = params[:password].to_s
        customer = ::Mkp::Store.find_by(id: 41).customers.not_deleted.find_by(email: email)

        if customer && customer.valid_password?(password)
          render json: { success: true, customer_data: customer_data(customer), message: 'Valid credentials' }, status: '200'
        else
          render json: { success: false, message: 'Invalid credentials' }, status: '401'
        end
      end

      private

      def customer_data(customer)
        address = customer.addresses.last
        {
          doc_number: customer.doc_number,
          doc_type: customer.doc_type,
          email: customer.email,
          first_name: customer.first_name,
          last_name: customer.last_name,
          gender: customer.gender,
          birthday_at: customer.birthday_at,
          telephone: customer.telephone,
          address: address.try(:address),
          address2: address.try(:address_2),
          city: address.try(:city),
          state: address.try(:state),
          country: address.try(:country),
          zip_code: address.try(:zip)
        }
      end
    end
  end
end
