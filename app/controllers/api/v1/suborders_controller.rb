module Api
  module V1
    class SubordersController < EntitiesController
      skip_before_action :verify_authenticity_token # NOTE: add to skip verify token CSRF
      
      def update_shipment_status
        # Posibles estados de shipping: not_delivered, in_process o delivered
        unless ['not_delivered', 'in_process', 'delivered'].include?(params[:status])
          render json: "Status must be only one of those: not_delivered, in_process or delivered", adapter: :json, status: :not_acceptable
        else
          suborder = ::Mkp::Suborder.where(shop: @current_user.shops, id: params[:id]).first
          if suborder.present?
            begin
              #Actualizo el shipment usando este manager que actualiza la suborden y el item tambien
              ::Mkp::StatusChange::EntityStatusManage.status_change(suborder.shipment, params[:status])
              render json: "Shipment updated", adapter: :json, status: :ok
            rescue
              render json: "Shipment not found", adapter: :json, status: :not_found
            end
          else
            render json: "Suborder not found", adapter: :json, status: :not_found
          end
        end
      end 

      private

      def entity_class
        ::Mkp::Suborder.joins(order: :payments)
                       .where.not(mkp_payments: { status: 'pending' })
                       .where.not(mkp_payments: { status: 'expired' })
      end

      def available_entities
        # from format 'dd/mm/yyyy'

        from = Time.zone.parse(params[:date_from]) if params[:date_from]
        to = Time.zone.parse(params[:date_to]) if params[:date_to]
        @entities = entity_class.where(shop: @current_user.shops)
        @entities = @entities.where('mkp_suborders.created_at >= ?', from) if from
        @entities = @entities.where('mkp_suborders.created_at <= ?', to) if to

        @entities
      end

      def initialize_entity
        @entity = available_entities.where(id: params[:id])
        @entity = @entity.last
        raise ActiveRecord::RecordNotFound unless @entity
      end
    end
  end
end
