class Api::AngularApp::V1::VerifyCardController < Api::AngularApp::V1::ApplicationController
  before_filter :authenticate_user!, only: [:index, :create]

  def index
    render json: { 
      points_enabled: @current_user.points_enabled_at.present?,
      doc_number: @current_user.doc_number,
      cuit: @current_user.cuit,
      card_number_suffix: @current_user.points_card_number_suffix,
      card_type: "any",
      current_customer_points: @current_user.points_enabled_at.present? ? @current_user.points : 0
      }, status: 200
  end

  def create
    if !verify_card_params[:cuit].to_s.include?(@current_user.doc_number.to_s) ||
       !CuitHelper.validar_cuit(verify_card_params[:cuit])
      return render json: {error: 'Cuit inválido', error_code: 'invalid_cuit'}, status: 422
    end
    
    response = Loyalty::BnaService.new.verify_card(
                document_number: @current_user.doc_number.to_s,
                cuil_number: verify_card_params[:cuit].to_s,
                card_number_suffix: verify_card_params[:card_number_suffix].to_s)
    if response['ResponseCode'] == '0'
      @current_user.update(
        points_card_number_suffix: verify_card_params[:card_number_suffix],
        cuit: verify_card_params[:cuit],
        points_enabled_at: Time.now)
      render json: { 
        points_enabled: @current_user.points_enabled_at.present?,
        doc_number: @current_user.doc_number,
        cuit: @current_user.cuit,
        card_number_suffix: @current_user.points_card_number_suffix,
        card_type: "any",
        current_customer_points: @current_user.points_enabled_at.present? ? @current_user.points : 0
        }, status: 201
    else
      render json: {error: 'Error al verificar la tarjeta.', error_code: 'card_not_verified'}, status: 422
    end
  end

  def verify_card_params
    params.permit(:cuit, :card_number_suffix)
  end
end