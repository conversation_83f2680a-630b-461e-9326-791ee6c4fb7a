class Api::AngularApp::V1::MenusController < Api::AngularApp::V1::ApplicationController
  def index
    @menus = Mkp::Menu.prepare_menu(@current_store.id)
    @menus.each{|m| add_empty_childs_attr(m)}
  end

  def sticky_navbars
    @sticky_navbars = Mkp::Menu.sticky_navbar.active
  end

  private

  def add_empty_childs_attr(menu)
    if(menu[:childs].present?) then
      menu[:childs].each{|m| add_empty_childs_attr(m)}
    else
      menu[:childs] = []
    end
  end
end
