class Api::AngularApp::V1::Boca::RegistersController < Api::AngularApp::V1::ApplicationController

  def sign_up
    @customer = @current_store.customers.not_deleted.find_or_create_by(id: customer_params[:id])
    @customer.attributes = customer_params
    @customer.build_default_subscription
    unless @customer.valid?
      render json: @customer.errors.full_messages, status: :unprocessable_entity
    end
  end

  def payment
    @customer = @current_store.customers.not_deleted.build(customer_params)
    @customer.build_default_subscription
    return render json: @customer.errors.full_messages, status: :unprocessable_entity unless @customer.valid?
    payment = ::Tdd::Decidir::Client.collect(build_decidir_params)
    return render json: payment, status: :unprocessable_entity if payment[:status_details].present?
    @payment = @customer.subscription.payments.build(payment)
    return render json: @payment.errors, status: :unprocessable_entity unless @customer.save
    Tdd::RegisterMailer.notify_subscription(@customer).deliver
    @customer.member.update(code: params[:attributes][:payment][:security_code])
  end

  def build_decidir_params
    OpenStruct.new(
      token: payment_params[:payment][:id],
      payment_method_id: payment_params[:payment][:payment_method_id],
      bin: payment_params[:payment][:bin],
      amount: @customer.subscription.amount,
      user_id: @customer.email,
      user_email: @customer.email
    )
  end

  private

  def customer_params
    params.require(:attributes)
          .permit( :id,
            :first_name, :last_name, :doc_type, :doc_number, :birthday_at,
            :gender, :telephone, :email, :password, :password_confirmation,
            member_attributes: [ :id, :nationality, :mobile, :origin ],
            addresses_attributes: [ :id, :internal_country_address,
                                    :first_name, :last_name, :telephone,
                                    :address, :address_2, :city,
                                    :state, :country, :zip, :street_number,
                                    :floor, :dpto, :tower, :body, :lateral_street_1,
                                    :lateral_street_2, :county, :country_club
                                  ]
          )
  end

  def payment_params
     params.require(:attributes).permit(user: [:id], payment: [:id, :status, :card_number_length,
                    :bin, :last_four_digits, :security_code_length, :expiration_month, :expiration_year,
                    :payment_method_id, :security_code])
  end
end
