module Api
  module AngularApp
    module V1
      module Bna
        class HashValidatorController < ApplicationController

          def validate
            service = HashValidatorService.new(hash_params)

            if service.invalid_params
              return render json: { error: service.error.to_s }, status: :bad_request
            end

            render json: service.build_json, status: :ok
          end

          def hash_params
            params.permit(:cuil, :idSolicitud, :nombre, :apellido, :monto, :Sucursal, :email,
                          :perfil, :programa, :fecha, :hash,
                          :controller, :action, :network)
          end
        end
      end
    end
  end
end
