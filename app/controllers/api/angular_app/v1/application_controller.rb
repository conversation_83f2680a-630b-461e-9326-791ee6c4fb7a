class Api::AngularApp::V1::ApplicationController < ::Api::ApplicationController
  include Api::AngularApp::V1::Concerns::Authenticable

  def answers_attempted
    if answers_attempted_params[:purchase_id].present?
      cart = ::Mkp::Cart.find_by_purchase_id(answers_attempted_params[:purchase_id])
      if cart.nil?
        cart_not_found
        return
      end
    end

    response = GatewayIdValidator.call(
      doc_number: answers_attempted_params[:doc_number],
      gender: answers_attempted_params[:gender],
      purchase_id: answers_attempted_params[:purchase_id],
      current_store: @current_store,
      quiz: answers_attempted_params[:quiz])

    if response.valid?
      render json: response.payload, status: response.status || :ok
    else
      render json: { error: response.error }, status: response.status
    end
  end

  def password_recovery
    layout_name = "layouts/mailer/password_recovery"
    customer = @current_store.customers.not_deleted.find_by(email: recovery_password_params[:email]) 
    if customer.present?
      password = generate_password
      customer.update(password: password, password_confirmation: password)

      RecoveryPasswordMailer.perform(customer.email, password, @current_store).deliver!
    end
    render json: {status: :ok}
  end

  private
  
  def not_found
    render json: { error: 'Not Found' }, status: :not_found
  end

  def cart_not_found
    render json: { error: 'You need to build your own cart' }, status: 404
  end

  def answers_attempted_params
    params.permit(:doc_number, :gender, :purchase_id,
                  quiz: [:type, :transaction_id, :questionnaire_id, { questions: [:id, :question, :response, { answers: %i[id answer] }] }])
  end

  def recovery_password_params
    params.permit(:email)
  end

  def generate_password
    Digest::MD5.hexdigest("#{rand.to_s}#{Time.now.to_i.to_s}")[0..7]
  end
end
