class Api::AngularApp::V1::ProfilesController < Api::AngularApp::V1::ApplicationController
  before_filter :authenticate_user!, only: [:show, :update]
  
  def show
    @current_user_points_error = nil
    @current_user_points = 0
    begin
      @current_user_points = @current_user.points
    rescue Loyalty::ApiExceptions::ApiExceptionError => e
      @current_user_points_error = {"message_type"=>"text", "message"=> "En este momento, no se pueden visualizar tus puntos. Te invitamos a ingresar en unos minutos."}.to_json
    rescue StandardError => e
      @current_user_points_error = e.message
    end
  end
  
  def update
    if update_attributes
      render :show
    else
      render json: {errors: @current_user.errors}, status: 406
    end
  end

  protected

  def update_attributes
    if params[:user][:current_password].present?
      @current_user.update_with_password(user_params)
    else
      return false if !@current_user.update_attributes(user_params)
      @current_user.update_attribute(:temporary_email, false) if @current_user.temporary_email && user_params[:email].present?
      true
    end
  end

  private

  def user_params
    params.require(:user).permit(:email, :first_name, :last_name, :current_password, :password, :password_confirmation, :image, :doc_type, :doc_number, :gender, :birthday_at, :telephone,:cuit)
  end
end
