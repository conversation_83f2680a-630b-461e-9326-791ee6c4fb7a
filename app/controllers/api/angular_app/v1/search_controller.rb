class Api::AngularApp::V1::SearchController < Api::AngularApp::V1::ApplicationController
  include ::Mkp::CatalogLegacy

  # TODO, there are a lot of things that we need to move to a concern
  # Before doing that, we need to create test cases for every single method
  # That is the reason that I'm not doing a code refactor
  # I don't want to break the current application
  # This file shares the same code with app/controllers/mkp/catalog_controller.rb
  # --- Lucas ---

  before_filter :ensure_params_value_types, only: [:index, :show]
  before_filter :append_to_params, only: [:index, :show]
  before_filter :create_var_not_found

  def index
    @models = retrieve_models(nil, @current_store)
    build_variants
    render :show
  end

  def show
    UrlMapper.match(params).present?
    @models = retrieve_models(nil, @current_store)
    raise ActiveRecord::RecordNotFound if @models.empty?
    build_variants
  end

  def suggestions
    if params[:query]
      # Normalización de la consulta
      params[:query] = params[:query].strip.force_encoding('ISO-8859-1').encode('UTF-8') unless params[:query].valid_encoding?
    end

    # Convertir todo a minúsculas y normalizar
    @query = params[:query].to_s.downcase.strip
    keywords = @query.split(' ').map(&:downcase)

    # Dividimos las palabras clave en texto y números
    text_keywords = keywords.select { |kw| kw.match(/[a-zA-Z]/) }
    number_keywords = keywords.select { |kw| kw.match(/\d+/) }

    begin
      # Búsqueda principal en Solr
      solr_search = Mkp::Product.search  do
        # Búsqueda fuzzy (difusa) basada en palabras clave
        fulltext text_keywords.map { |keyword| "#{keyword}~" }.join(" ")

        # Filtrar por números en el título
        if number_keywords.any?
          number_keywords.each do |number|
            with(:title).starting_with(number)
          end
        end

        # Aplicar filtros: productos visibles, con stock, y tiendas visibles
        with :deleted, false
        with :visible_shop, true
        with :with_stock, true
        with(:available_on).less_than Time.zone.now
      end

      # Resultados de la búsqueda
      fuzzy_products = solr_search.results

      # Fallback: Si no hay resultados, hacer una búsqueda más amplia
      if fuzzy_products.empty?
        solr_search = Mkp::Product.search do
          # Fuzzy matching manual para búsquedas más permisivas
          fulltext @query.split(" ").map { |word| "#{word}~" }.join(" ")

          # Aplicar filtros: productos visibles, no eliminados, con stock, etc.
          with :deleted, false
          with :visible_shop, true
          with :with_stock, true
          with(:available_on).less_than Time.zone.now
        end
        fuzzy_products = solr_search.results
      end

      # Filtrar variantes (fallback a SQL para mayor precisión)
      product_ids = fuzzy_products.map(&:id)
      fuzzy_variants = Mkp::Variant.joins(:shop)
                                   .where(product_id: product_ids)
                                   .where("quantity > ?", 0)
                                   .where(visible: 1)
                                   .where(mkp_shops: { visible: 1 })

      # Obtener las categorías de los productos encontrados
      @categories = fuzzy_products.map(&:category).compact.uniq

      # Variantes encontradas
      @variants = fuzzy_variants

    rescue => e
      Rails.logger.error("Error en la búsqueda de sugerencias: #{e.message}")
      @categories, @variants = [], []
    end
  end


  private

  def ensure_params_value_types
    params.tap do |p|
      p[:b] = p[:b].values if p[:b] && p[:b].is_a?(Hash)
      p[:sp] = p[:sp].values if p[:sp] && p[:sp].is_a?(Hash)
      p[:g] = p[:g].values if p[:g] && p[:g].is_a?(Hash)

      # Ensure this params are string and not array
      p[:pr] = nil if p[:pr].present? && p[:pr].is_a?(Array)
      p[:o] = nil if p[:o].present? && p[:o].is_a?(Array)
    end
  end

  def append_to_params
    params.merge!({
                    :network => @network.downcase,
                    :path => params[:id],
                    :p => params[:p] || "1"
                  })

    params.merge!(c: @category.id.to_s) unless @category.nil?
    params.merge!(c: params[:id]) if @category.nil?
    params.merge!(b: @manufacturer.id.to_s) unless @manufacturer.nil?
  end

  def build_variants
    if params[:query]
      val = params[:query]
      params[:query] = val.force_encoding('ISO-8859-1').encode('UTF-8')
    end

    if params[:term].blank? && params[:query].present?
      increase_suggestion_count if params[:query].valid_encoding?
    end

    if params[:query].blank? && params[:o].blank?
      @seed = (params[:s] || Random.rand(999).to_s(36))
      params[:s] = @seed if params[:s].blank?
    end

    search = Mkp::Catalog::Finder.find(normalized_params)
    @variants = search.results

    if @variants.empty?
      search = Mkp::Catalog::Finder.find(normalized_params.except(:query))
      @variants = search.results
      @not_found = true
    end

    raise ActiveRecord::RecordNotFound if @variants.empty?

    @breadcrumb = build_breadcrumb
    @filters = Mkp::Catalog::Filter.new(search, normalized_params, @models, @current_store).arrange
  end

  def normalized_params
    other_params = HashWithIndifferentAccess.new(params.dup)

    other_params.reject! do |key, value|
      unless value.respond_to?(:to_ary)
        value.blank?
      else
        value.all?(&:blank?)
      end
    end

    if params[:b].is_a?(Array) || params[:b].is_a?(String)
      other_params[:b] = get_manufacturers.map(&:id)
    end

    other_params[:store_id] = @current_store.id

    if params[:sp].is_a?(Array)
      other_params[:sp] = get_sports.map(&:id)
    end

    if other_params[:query].present?
      other_params[:query] = '' unless other_params[:query].valid_encoding?
    end

    if other_params[:price].present?
      if @current_store.name != "jubilo"
        other_params[:price] = nil
      else
        other_params[:price] = nil if other_params[:price] == "null"
      end
    end

    other_params
  end

  def increase_suggestion_count
    if (suggestion = Suggestion.find_by_term(params[:query])).present?
      suggestion.update_attributes(count: suggestion.count + 1)
    else
      Suggestion.create({
                          term: params[:query].downcase,
                          network: @network.downcase
                        })
    end
  end

  def build_breadcrumb
    parameters = HashWithIndifferentAccess.new(params.select do |key|
      %w(network controller action).include?(key)
    end)

    network = parameters[:network].downcase

    breadcrumb = [
      {
        name: 'Shop',
        href: Rails.application.routes.url_helpers.mkp_catalog_root_path(network: network)
      }
    ]

    models = []

    if @categories.present?
      @categories.first.path.each do |category|

        category_models = models + [category]

        breadcrumb << {
          name: category.name,
          href: UrlMapperHelper.absolute_mapper_path(category_models, network)
        }
      end
      models += @categories
    end

    if @manufacturers.present? && @manufacturers.size == 1
      manufacturer = @manufacturers.first

      breadcrumb << {
        name: manufacturer.name,
        href: UrlMapperHelper.absolute_mapper_path(models + [manufacturer], network),
        href_remove: UrlMapperHelper.absolute_mapper_path(models, network)
      }
    end
    if params[:d].present?
      breadcrumb << {
        name: I18n.t('mkp.catalog.v5.filter.sale.on_sale'),
        href: UrlMapperHelper.absolute_mapper_path(models, network, {d: 1}),
        href_remove: UrlMapperHelper.absolute_mapper_path(models, network)
      }
    end

    if params[:query].present? && params[:term].blank?
      breadcrumb << {
        query: true,
        name: params[:query]
      }
    end

    breadcrumb
  end

  def create_var_not_found
    @not_found = false
  end
end