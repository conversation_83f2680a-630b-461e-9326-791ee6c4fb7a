module Api::AngularApp::V1::Concerns::CheckoutPendingPayment
  extend ActiveSupport::Concern

  def payment_intent_status
    @order = ::Mkp::Order.find_by_purchase_id(params['purchase_id'])
    return false unless @order

    @payment = [@order.payment]
    render json: response_pending_payment
    true
  end

  def response_pending_payment
    payment = @payment.last
    {
        success: true,
        order: @order.suborders.flat_map(&:public_id).join(', #'),
        vouchers: @order.items.map { |item| item.coupon&.code }.compact,
        reservation_code: @order.reservation_code.presence,
        customer: @order.customer.attributes.slice('email', 'first_name', 'last_name')
    }.tap do |resp|
      payment_last_info= payment.last_gateway_data.except(:request)
      payment_last_info[:gateway] = payment_last_info.delete(:response) || payment_last_info["gateway"] || payment.gateway_data["gateway"]
      payment_last_info[:callbacks] = payment_last_info["callbacks"] || payment.gateway_data.dig(:original_payload, :callbacks) || payment.gateway_data["callbacks"]
      payment_last_info.except!(:notifications_payloads, :original_payload)
      resp.merge!(payment: payment_last_info) if payment_last_info.present?
    end
  end

  def payment_intent
    return head :not_found unless (params[:gateway] == 'modo' || params[:gateway] == 'mododistributed')

    authenticate_user!
    find_cart
    if @cart.present?
      begin
        build_loyalty_program_discount(params)
      rescue => e
        log_payment_fail(@payment, @checkout_cart.items, e) if @payment.present?
        raise Api::AngularApp::V1::CheckoutController::CreateOrderException, 'Error en el sistema de puntos. Intente en unos minutos.', []
      end
      build_checkout_cart
      raise Api::AngularApp::V1::CheckoutController::CheckoutCart::Error, 'Intención de pago necesita monto', [] unless @checkout_cart.total > 0
    end
    # TODO: return 404 if @order status exists and not pending or @cart.blank?
    payment_intent_status || payment_modo(params[:gateway] == 'mododistributed')
  end

  def payment_modo(distributed = false)
    service = Checkout::ValidateProductAvailability.new(@cart.items_to_checkout, @current_store)
    service.perform
    unless service.valid
      log_payment_fail(@payment, @checkout_cart.items, OpenStruct.new(full_message: service.error))
      raise Api::AngularApp::V1::CheckoutController::CreateOrderException, Rack::Utils.escape_html("El pago a través de MODO no pudo ser procesado."), []
    end

    build_checkout_cart
    verify_checkout_cart!
    Rails.logger.info("<Checkout Controller> Items to Checkout: #{@cart.items_to_checkout}")

    params[:payment] = distributed ? mododistributed : modo

    process_payment_angular if params[:payment].present?
    begin
      ActiveRecord::Base.transaction do
        Rails.logger.info("<Checkout Controller> Checkout Cart Items: #{@checkout_cart.items}")
        collect_payment
        if (@checkout_cart.balance_due? && ( @payment.blank? || @payment.any? { |payment| payment.cancelled? }))
          exit_payment_process and return true
        end
        create_order(@current_store.id)
        Insurance::InsuranceProcessor.new(@checkout_cart, @order).process_checkout_insurance(insurance_params)
        create_and_associate_shipments
        ::Mkp::Integration::PuntosYPremios.new.redemption(order, nil) if PENDING_PAYMENTS_AS_CONFIRMED && order.store.name == 'bancomacro'
        suborders_coupon_generator if @order.coupon.present?
        remove_current_cart(params[:gateway]) if @cart.present?
        set_gross_total
        render json: response_pending_payment
      end
    rescue Avenida::Payments::InvalidAmount
      raise
    rescue => e
      if @payment.present?
        @payment.each do |payment|
          next if !payment.collected?
          "Avenida::Payments::#{payment.gateway}".constantize.cancel_payment_by_store!(payment.gateway_object_id, @current_store, payment, @checkout_cart)
          # modo_logger.info("cancel_payment_by_store: #{payment.gateway_object_id}")
        end
      end
      log_payment_fail(@payment, @checkout_cart.items, e) if @payment.present?
      raise Api::AngularApp::V1::CheckoutController::CreateOrderException, Rack::Utils.escape_html("El pago a través de MODO no pudo ser procesado."), []
    end
  end

end
