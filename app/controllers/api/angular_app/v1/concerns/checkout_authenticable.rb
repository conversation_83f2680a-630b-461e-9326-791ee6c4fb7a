require 'json_web_token'

module Api::AngularApp::V1::Concerns::CheckoutAuthenticable
  class UnauthorizedError < StandardError; end
  extend ActiveSupport::Concern

  # Validates the token and user and sets the @current_user scope
  def authenticate_user!
    unless !payload || !JsonWebToken.valid_payload(payload.first)
      load_current_user!
    end
  rescue StandardError => _e
    nil
  ensure
    raise UnauthorizedError if @current_store.require_login? && @current_user.blank?
  end

  def authenticate_user
    authenticate_user!
  rescue StandardError => _e
    nil
  end

  private
  # Deconstructs the Authorization header and decodes the JWT token.
  def payload
    auth_header = request.headers['auth-token']
    token = auth_header.split(' ').last
    JsonWebToken.decode(token)
  end

  # Sets the @current_user with the user_id from payload
  def load_current_user!
    @current_user = @current_store.customers.not_deleted.find_by_id(payload[0]['user_id'])
  end
end
