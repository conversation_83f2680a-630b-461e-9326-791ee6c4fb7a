module Api::AngularApp::V1::Concerns::ExtendedWarranty
  extend ActiveSupport::Concern

  def find_associated_variant
    return unless associated_information?

    @associated_variant ||= ::Mkp::Variant.find(params['associatedProductVariantId'])
  end

  def find_warranty_variant
    return unless associated_information?

    @warranty_variant ||= ::Mkp::Variant.find(params['warrantyVariantId'])

    coef = params['warrantyCoef'].to_f
    @warranty_price ||= (@associated_variant.product.price * coef) / 100 if @associated_variant.present?
  end

  private

  def warranty_coef?
    params['warrantyCoef'].present?
  end

  def associated_variant?
    params['associatedProductVariantId'].present?
  end

  def associated_information?
    warranty_coef? && associated_variant?
  end

  def not_applicable?
    @cart.items.find_by(variant_id: @associated_variant.id).quantity > 1
  end
end
