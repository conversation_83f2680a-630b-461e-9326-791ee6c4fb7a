module Api::AngularApp::V1::Concerns::RenaperVerifiable
  extend ActiveSupport::Concern

  #dataEntryQuestions or multipleChoiceQuestions
  def render_questions(response, without_correct = false)
    response.deep_transform_keys!(&:underscore)
    questions(response, without_correct)
  end

  def data_entry_question(response, question_id = 1)
    response.deep_transform_keys!(&:underscore) # response.slice!('data_entry_questions')
    data_entry_questions = response['data_entry_questions']
    data_entry_questions[question_id - 1]
  end

  def questions(all_questions, without_correct = false)
    questions_types = {}
    all_questions.map do |question_type, questions|
      questions_types[question_type.to_s.snakecase] = send("#{question_type.snakecase}".to_sym, questions, without_correct)
    end
    questions_types
  end

  def multiple_choice_questions(question, without_correct = false)
    question.map do |question_data|
      {
        'question_id' => question_data['question_id'],
        'question' => question_data['question'],
        'options' => question_data['options'].map { |option| without_correct ? option.except('correct_option') : option }
      }
    end
  end

  def remove_correct_data_entry_questions(questions)
    questions.map do |question_data|
      question_data.except(:correct_value)
    end
  end

  def adapt_questions_response(questions)
    questions.map do |question_data|
      {
        'id': question_data['question_id'],
        'question': question_data['question'],
        'correct_value': question_data['correct_value'],
        'answers': nil,
        'response': nil,
        'success': false
      }
    end
  end

  def pending_data_entry_question(attributes)
    RenaperAnswer.unanswered.data_entry.find_by(attributes.merge({store_id: @current_store.id}))
  end

  #Elige una random entre las 3 posibilidades y la crea como pendiente

  def select_and_create_data_entry_question(response, purchase_id, doc_number, gender)
    question = data_entry_question(response, rand(3)+1)
    RenaperResponse.create(
      store_id: @current_store.id,
      purchase_id: purchase_id,
      question_type: "DataEntry",
      question_id: question['question_id'],
      question: question['question'],
      correct_answer: question['correct_value'],
      doc_number: doc_number,
      gender: gender,
      answered: false
    )
    question
  end


  def add_renaper_exception(params)
    RenaperFailedService.create(params)
  end

  def check_renaper_exception(params)
    RenaperFailedService.exists?(params)
  end

  def gender_customer_to_renaper(gender)
    case gender
      when 0, 'male'
        'M'
      when 1, 'female'
        'F'
      when 2, 'non_binary'
        'X'
    end
  end
end
