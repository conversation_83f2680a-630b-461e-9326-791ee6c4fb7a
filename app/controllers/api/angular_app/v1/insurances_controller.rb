# frozen_string_literal: true

class Api::AngularApp::V1::InsurancesController < Api::AngularApp::V1::ApplicationController

  # before_filter :authenticate_user!
  before_filter :set_token_whitelist, only: [:new, :create]
  before_filter :order_insurance, only: [:new, :create]

  def new    
    if @order.present?
      data = Hash.new
      data[:insurance_data] = {'ARS' => @amount, 'installments' => @order.store.strategy_insurances.installments, 'order_id' => @order_id, 'item_title' => @extra_data }
      addresses = @order.customer.addresses&.first
      data[:customer] = @order.customer.slice('first_name', 'last_name', 'email')
      data[:customer][:address] = addresses&.address if data[:customer]
      data[:message] = @message
      render json: data, status: :ok
    else
      render json: { error: 'Verifique datos del seguro' }, status: 400
    end
  end

  def create
    # save
    @insurance_data = new_insurance_data(insurance_params)
    if @insurance_data.valid?
      @insurance_data.data.delete(:token)
      @nacion_tokens_service = NacionTokens.new(NACION_TOKENS_URL, NACION_TOKENS_API_KEY, @insurance_data.data)
      @nacion_tokens_service.create_insurance
      if !@nacion_tokens_service.valid?
        render json: { error: 'Error al guardar datos del seguro' }, status: 400
      else
        insurance_token = @nacion_tokens_service.response['token']
        InsuranceToken.create(token: insurance_token, 
                              order_id: @order.id, 
                              amount: @amount.to_f,
                              name: @insurance_data.data["name"],
                              email: @insurance_data.data["email"],
                              installments: @insurance_data.data["installments"],
                              gender:	@insurance_data.data["gender"],
                              birthday:	@insurance_data.data["birthday"],
                              birth_location:	@insurance_data.data["birth_location"],
                              nationality: @insurance_data.data["nationality"],
                              doc_type:	@insurance_data.data["doc_type"],
                              doc_number:	@insurance_data.data["doc_number"],
                              phone: @insurance_data.data["phone"],
                              address: @insurance_data.data["address"],
                              street_number: @insurance_data.data["street_number"],
                              dept:	@insurance_data.data["dept"],
                              country: @insurance_data.data["country"],
                              state: @insurance_data.data["state"],
                              city:	@insurance_data.data["city"],
                              postal_code: @insurance_data.data["postal_code"],
                              civil_status: @insurance_data.data["civil_status"],
                              profession: @insurance_data.data["profession"],
                              political_exposure:	@insurance_data.data["political_exposure"],
                              reference_id: @insurance_data.data["reference_id"],
                              cbu: @insurance_data.data["cbu"],
                              floor: @insurance_data.data["floor"],
                              extra_data: @insurance_data.data["extra_data"]) if insurance_token
        render json: true, status: :ok
      end
    else
      render json: { error: 'Verifique datos del seguro' }, status: 400
    end
  end

  private

  def order_insurance
      @nacion_tokens_service = NacionTokens.new(NACION_TOKENS_URL, NACION_TOKENS_API_KEY, {})
      @nacion_tokens_service.token_whitelist(@token_whitelist)
      if @nacion_tokens_service.valid?
        @message = @nacion_tokens_service.response[:message]
        @order_id = @nacion_tokens_service.response["order_id"]
        @amount = @nacion_tokens_service.response["insurance_amount"].to_f
        @extra_data = @nacion_tokens_service.response["item"]
        @order = Mkp::Order.find(@order_id)
      else
        head @nacion_tokens_service.status
      end
  end

  def set_token_whitelist
    @token_whitelist = params[:token]
  end

  def new_insurance_data(insurance_params)
      collected_data = insurance_params.dup
      collected_data.merge!({
          installments: @order.store.strategy_insurances.installments, 
          amount: @amount,
          extra_data: @extra_data }
      )
      InsuranceData.new(collected_data)
  end

  def insurance_params
      params.permit(:token, :card_number, :name, :email, :phone, :birthday, :birth_location, :nationality, :gender, :civil_status, :profession, :doc_type, :doc_number, :address, :street_number, :depth, :country, :state, :city, :postal_code, :political_exposure, :cbu, :floor)
  end
    
end
