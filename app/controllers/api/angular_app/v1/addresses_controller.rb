class Api::AngularApp::V1::AddressesController < Api::AngularApp::V1::ApplicationController
  before_filter :authenticate_user!, only: [:index, :show, :create, :update, :destroy]
  before_filter :find_address, only: [:show, :update, :destroy]

  def index
    @addresses = @current_user.addresses.verified
  end

  def show
  end

  def create
    @address = @current_user.addresses.create(address_params)

    if @address.valid?
      render :show
    else
      render json: {errors: @address.errors}, status: 401
    end
  end

  def update
    @address.update_attributes(address_params)

    if @address.valid?
      render :show
    else
      render json: {errors: @address.errors}, status: 401
    end
  end

  def destroy
    if @address.destroy
      render json: true, status: 200
    else
      render json: false, status: 404
    end
  end


  def provinces
    render json: Province.all, status: :ok
  end

  def cities
    province_id = params.require(:province_id)
    data = City.where(province: province_id).order(:name)

    raise ActiveRecord::RecordNotFound if data.empty?

    render json: data, status: :ok
  end

  def zip_codes
    city_id = params.require(:city_id)

    data = ZipCode.where(city_id: city_id).order(code: :asc)
    raise ActiveRecord::RecordNotFound if data.empty?

    render json: data, status: :ok
  end

  private
  def find_address
    @address = @current_user.addresses.find(params[:id])
  end

  def address_params
    params.require(:address).permit(:first_name, :last_name, :telephone, :address, :address_2, :city, :state, :zip, :country, :pickup, :open_hours, :retardment, :priority, :street_number, :doc_type, :doc_number, :email)
  end
end
