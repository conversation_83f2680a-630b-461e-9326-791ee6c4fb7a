class QrController < ApplicationController
  before_action :set_qr_service

  # Endpoint para generar el QR
  def generate_qr
    dni = params[:dni]
    sexo = params[:sexo] || 'M'
    external_tx_id = params[:external_tx_id]

    # Validar parámetros
    if dni.blank? || external_tx_id.blank?
      render json: { success: false, message: 'DNI y external_tx_id son obligatorios' }, status: :unprocessable_entity
      return
    end

    begin
      # Paso 1: Obtener el access_token
      access_token = @qr_service.fetch_access_token
      Rails.logger.info "Access Token obtenido: #{access_token}"

      # Paso 2: Validar el token y obtener el token_id
      token_id = @qr_service.validate_token(access_token)
      Rails.logger.info "Token ID obtenido: #{token_id}"

      # Paso 3: Obtener sessionIDKey
      session_id_auth = SecureRandom.uuid # Simular un session ID único
      callback_url = "https://api-bna.com.ar/qr/callback" # URL para recibir la respuesta
      session_id_key = @qr_service.get_session_id_key(access_token, session_id_auth, callback_url)
      Rails.logger.info "Session ID Key obtenido: #{session_id_key}"

      # Paso 4: Generar el QR usando el token_id como seckey
      qr_html = @qr_service.generate_qr(dni, sexo, external_tx_id, token_id)
      render html: qr_html.html_safe
    rescue StandardError => e
      Rails.logger.error "Error generando QR: #{e.message}"
      render json: { success: false, message: 'Error generando el QR', details: e.message }, status: :unprocessable_entity
    end
  end

  # Callback para manejar respuestas de biometría
  def biometric_callback
    decision = params[:decision]
    step = params[:step].to_i if params[:step].present?
    idtx = params[:idtx]
    external_id = params[:externalID]

    # Manejar decisiones (HIT / NO_HIT)
    if decision.present?
      case decision
      when 'HIT'
        render json: {
          success: true,
          message: 'Validación biométrica exitosa.',
          decision: decision,
          transaction_id: idtx,
          external_id: external_id
        }, status: :ok
      when 'NO_HIT'
        render json: {
          success: false,
          message: 'Validación biométrica fallida.',
          decision: decision,
          transaction_id: idtx,
          external_id: external_id
        }, status: :unprocessable_entity
      else
        render json: {
          success: false,
          message: 'Decisión desconocida.',
          decision: decision,
          transaction_id: idtx,
          external_id: external_id
        }, status: :unprocessable_entity
      end
      return
    end

    # Manejar steps
    if step.present?
      case step
      when 445
        render json: { success: false, message: 'Navegador no compatible. Intente con Chrome o Safari.' }, status: :bad_request
      when 402
        render json: { success: false, message: 'Número de DNI no proporcionado.' }, status: :unprocessable_entity
      when 450
        render json: { success: false, message: 'Error en la transacción. Intente nuevamente.' }, status: :unprocessable_entity
      when 700
        render json: { success: false, message: 'Sesión expirada por inactividad.' }, status: :request_timeout
      when 701
        render json: { success: false, message: 'Sesión expirada sin escanear el QR.' }, status: :request_timeout
      when 9000..9999
        minutes_remaining = step - 9000
        render json: {
          success: false,
          message: "Dispositivo bloqueado. Intente de nuevo en #{minutes_remaining} minutos.",
          transaction_id: external_id
        }, status: :too_many_requests
      else
        Rails.logger.warn "Step desconocido recibido: #{step}"
        render json: {
          success: false,
          message: "Paso desconocido: #{step}. Consulte soporte.",
          step: step,
          transaction_id: external_id
        }, status: :unprocessable_entity
      end
      return
    end
  end
end