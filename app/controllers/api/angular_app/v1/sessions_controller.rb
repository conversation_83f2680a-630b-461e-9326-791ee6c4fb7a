require 'json_web_token'

class Api::AngularApp::V1::SessionsController < Api::AngularApp::V1::ApplicationController
  before_filter :find_cart, only: [:create]

  def create
    @user = @current_store.customers.deleted.find_by_email(params[:email].to_s.downcase)
    if @user.present?
      return render json: {error: @user.nil? ? {key: :email, text: t('registration_required', scope:'models.mkp.customer')} : {key: :password, text: t('registration_required', scope:'models.mkp.customer')}}, status: 401
    end

    @user = @current_store.customers.not_deleted.find_by_email(params[:email].to_s.downcase)

    if @user && @user.valid_password?(params[:password])
      set_cart
      @user.update_last_sign_in
      render json: {success: true, token: JsonWebToken.encode({user_id: @user.id, store_id: @current_store.id})}, status: 200
    else
      render json: {error: @user.nil? ? {key: :email, text: t('invalid_username_password', scope:'models.mkp.customer')} : {key: :password, text: t('invalid_username_password', scope:'models.mkp.customer')}}, status: 401
    end
  end

  def macro_login
    service = Macro::ValidateUserMacro.new(macro_login_params, @current_store)
    if service.perform
      render json: {
        success: true,
        token: JsonWebToken.encode({user_id: service.user.id, store_id: @current_store.id}),
        user_info: {
          first_name: service.user.first_name,
          last_name: service.user.last_name,
          available_visa_points: service.user_info['available_points'],
          client_code: service.user_info['client_code'],
          email: service.user.email,
          temporary_email: service.user.temporary_email,
          is_select_user: service.user.is_select_user?
        },
        valid_until: service.user_info['token_valid_until']
      }, status: 200
    else
      render json: {success: false, description: 'Invalid macro token'}, status: 200
    end
  end

  private

  def find_cart
    @cart = Mkp::Cart.by_purchase_id(params[:purchase_id]).first
  end

  def set_cart
    @cart.update(customer_id: @user.id, customer_type: "Mkp::Customer", address_id: nil, delivery_option_id: nil) if @cart.present?
  end

  def macro_login_params
    params.permit(:macro_token)
  end

end
