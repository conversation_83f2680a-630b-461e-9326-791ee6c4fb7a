module Api
  module Mkp
    class WebhookController < ActionController::Base
      include PendingPaymentRefreshable
      # http_basic_authenticate_with name: "avenida", password: "secret", only: :payments

      protect_from_forgery with: :exception
      skip_before_action :verify_authenticity_token, only: [:payments]
      before_filter :check_and_set_gateway, only: :payments

      def notifications
        if payment_parameters_are_correct?
          if payment = ::Mkp::Payment.where(gateway: 'Mercadopago', gateway_object_id: params[:id]).first
            if payment.created_at < 30.seconds.ago
              payment.refresh!
            else
              render nothing: true, status: :service_unavailable and return
            end
          end
        end

        render layout: false, json: { status: :received }
      end

      def shopify
        integration = ::Mkp::Integration::Shopify.find(params[:integration_id])

        if valid_shopify_shop_domain?(integration) && valid_shopify_topic?(integration)
          notification = {
            payload: parsed_payload,
            topic: request.headers['HTTP_X_SHOPIFY_TOPIC']
          }

          integration.process_notification(notification) if notification.present?

          render nothing: true
        end
      rescue ActiveRecord::RecordInvalid => e
        ExceptionNotifier.notify_exception(e, data: { params: params.dup })
        render nothing: true
      end

      def mercadolibre
        user_id = params[:user_id].to_i

        key = key_for_webhook('mercadolibre', user_id)

        if $redis.setnx(key, true)
          message = { message: 'Got it! Working on this.' }
          if valid_mercadolibre_topic?
            resource_id = extract_mercadolibre_resource_id
            integration = load_mercadolibre_integration(resource_id, user_id)

            if integration && integration.need_to_process?(resource_id)
              integration.process_notification(resource_id)
            else
              message = { message: 'Got it! Nothing to do here.' }
            end
          end
        else
          message = { message: 'Already working on it.' }
        end

        render layout: false, json: message
      ensure
        $redis.del(key)
      end

      def shipnow
        if valid_shipnow_topic?
          notification = {
            resource_id: params[:resource_id],
            topic: params[:topic]
          }

          Gateways::Shipments::Shipnow.process_notification(notification)
        else
          Rails.logger.info("TRACE_BULLET Shipnow : @==================================================@")
          Rails.logger.info("TRACE_BULLET Shipnow : @ PARAMS: #{ params.as_json }")
          Rails.logger.info("TRACE_BULLET Shipnow : @==================================================@")
        end

        render nothing: true
      end

      def lion
        ids = params["id"].split('-')
        # We do this because first external id's corresponded with shipment, then we changed
        # it to match with suborders public_id
        if ids.size > 1
          shipment = ::Mkp::Suborder.find(ids[1]).shipment
        else
          shipment = ::Mkp::Shipment.find(ids[0])
        end
        if params["token"] == LION_KEY && shipment.present?
          status = params["status"].parameterize.underscore.to_sym
          is_pickup = shipment.is_pickup?
          if is_pickup && Gateways::Labels::Lion::STATUSES_MAP[status] == "delivered"
            Gateways::Shipments::Pickit.confirm_ready_to_pick(shipment)
          elsif !is_pickup
            Mkp::StatusChange::EntityStatusManage.status_change(shipment, Gateways::Labels::Lion::STATUSES_MAP[status])
          end
          render nothing: true, status: :accepted
        else
          render nothing: true, status: :unauthorized and return
        end
      end

      def pickit
        if params["token"] == PICKIT_TOKEN_ID
          message = Gateways::Shipments::Pickit.process_notification(params["IdTransaccion"], params["Estado"])
        else
          message = "Invalid token id"
        end
        render layout: false, json: {message: message}
      end

      def payments
        if @gateway.present?
          payment_data = payment_params[:data]
          payment = ::Mkp::Payment.find_by!(gateway: @gateway, gateway_object_id: payment_data[:payment_id])
          order = payment.sale_item
          unless ::PaymentManager::CollectPayment.merchant_match(payment_data[:merchant_order_id], order.purchase_id)
            render nothing: true, status: :unauthorized and return
          end

          # Si hay un pago con puntos asociado a este pago con Modo pero que ya esta cancelado o sea != collected no se puede cambiar
          if ::Mkp::Payment.where(gateway: 'LoyaltyBna', sale_item_id: payment.sale_item_id).where.not(status: 'collected').exists?
            raise StandardError.new('El pago de puntos asociado esta en estado diferente a collected. No se permite cambio de estado en Modo')
          end

          # modo_logger.info("Webhook - Payment data status: #{payment_data[:status]}")
          if payment_data[:status] != 'created' && payment_data[:status] != 'rejected'
            if refresh_payment!(payment)
              # modo_logger.info("Webhook - Confirm pending payment: #{payment.payment.gateway_object_id}")
              # si no hay puntos para canjear se confirma todo
              if payment_data[:status] == 'cancelled' || payment_data[:status] == 'rejected'
                Insurance::InsuranceProcessor.new(nil, order).delete_insurance_tokens
              elsif payment.collected?
                Insurance::InsuranceProcessor.new(nil, order).process_collected_payment_insurance
              end
              confirm_pending_payment(payment)
            end
          end
        end

        render json: { result: :ok }
      end

      private

      def check_and_set_gateway
        gateways_to_check = {
          Avenida::Payments::AvenidaModo.gateway_id.to_i => ['AvenidaModo', 'AvenidaModoDistributed']
        }
        @gateway = gateways_to_check[payment_params[:data][:gateway_id]]
      end

      def payment_params
        # {
        #   "message": "Payment id: 859 with amount: 9374 has been processed",
        #   "data": {
        #     "action": "processed",
        #     "payment_id": 859,
        #     "amount": 9374,
        #     "status": "created",
        #     "gateway_id": 3,
        #     "integrator_id": 92,
        #     "merchant_order_id": "TruAh7UmdGIKjgSfHsRBFw-220803113105_1"
        #   }
        # }
        params.permit(:message, { data:[:action, :payment_id, :amount, :status, :gateway_id, :integrator_id, :merchant_order_id] })
      end

      def payment_parameters_are_correct?
        params[:topic] == 'payment' && params[:id].present?
      end

      def parsed_payload
        @_payload ||= begin
          incoming_json = request.body.read

          if incoming_json && incoming_json.length >= 2
            JSON.parse(incoming_json)
          end
        end
      end

      def valid_shopify_shop_domain?(integration)
        shop_domain = request.headers['HTTP_X_SHOPIFY_SHOP_DOMAIN'].split('.').first
        valid_shop_domain = shop_domain == integration.account_name

        if valid_shop_domain
          true
        else
          render nothing: true, status: :unauthorized and return
        end
      end

      def valid_shopify_topic?(integration)
        valid_topic = integration.class::WEBHOOKS_TOPICS.any? do |topic|
          request.headers['HTTP_X_SHOPIFY_TOPIC'] == topic
        end

        if valid_topic
          true
        else
          render nothing: true and return
        end
      end

      def valid_shipnow_topic?
        Gateways::Shipments::Shipnow::WEBHOOKS_TOPICS.include?(params[:topic])
      end

      def load_mercadolibre_integration(resource_id, user_id)
        external_object = ::Mkp::Integration::Object.find_by_external_id(resource_id)

        if external_object.present?
          integration = external_object.integration
          integration if integration.data[:uid].to_i == user_id
        else
          integrations = ::Mkp::Integration::Base.where(
            type: 'Mkp::Integration::Mercadolibre',
            account_name: user_id
          )
          return unless integrations.present?

          # If we have multiple integrations we asume that the user_type = 'Brand'
          # and so the products should have an :official_store_id associated
          if integrations.length > 1
            mercadolibre_product = integrations.first.get_naked_item(resource_id)
            return unless mercadolibre_product.present?

            # Be carefull with this, because if we change the way it's in
            # MercadoLibreLogic we could break this.
            full_account_name = "#{user_id}-#{mercadolibre_product.official_store_id}"
            integrations.detect { |integration| integration.account_name == full_account_name }
          else
            integrations.first
          end
        end
      end

      def valid_mercadolibre_topic?
        ::Mkp::Integration::Mercadolibre::WEBHOOKS_TOPICS.include?(params[:topic])
      end

      def key_for_webhook(type, id)
        topic, resource = HashWithIndifferentAccess.new(parsed_payload).values_at(:topic, :resource)

        "webhooks:#{id}-#{type}-#{topic}-#{resource}"
      end

      def extract_mercadolibre_resource_id
        params[:resource].split('/').last
      end
    end
  end
end
