class AvenidaProductPictureService

  attr_reader :table, :shop_id

  def initialize(args = {})
    @table = args[:table]
    @shop_id = args[:shop_id]
  end

  def perform
    table.each do |row|
      variant = Mkp::Variant.find_by(gp_sku: row[:av_sku], shop_id: shop_id)

      next unless variant

      variant.product.pictures.destroy_all

      images(row).each.with_index(1) do |image, index|
        next if image.blank?

        AvenidaProductPictureWorker.new.perform(variant.product.id, image, index)
      end
    end
  end

  def images(row)
    [
      row[:imagen_1],
      row[:imagen_2],
      row[:imagen_3],
      row[:imagen_4],
      row[:imagen_5],
      row[:imagen_6],
      row[:imagen_7],
      row[:imagen_8]
    ]
  end
end
