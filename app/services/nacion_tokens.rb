class NacionTokens
    DateRange = Struct.new(:start_date, :end_date)

    attr_accessor :response, :status, :data, :api_key, :url, :valid, :errors

    def initialize(url, api_key, data)
        @api_key = api_key
        @data = data
        @url = url
        @valid = false
        @errors = ''
    end

    def create_insurance
      prepare_data
      begin
        #Rails.logger.info(data)
        server_response = RestClient.post("#{url}/insurances", data, headers)
        self.status = server_response.code
        self.response = JSON.parse(server_response.body)
        self.valid = true
        self.response
      rescue RestClient::ExceptionWithResponse => e
        self.response = {}
        self.status = e.response.code
        @errors = JSON.parse(e.response.body)
        self.valid = false
      rescue RestClient::Unauthorized, RestClient::Forbidden => err
        self.response = {}
        self.status = e.response.code
        @errors = "Unauthorized"
        self.valid = false
       end
    end
    
    def valid?
     self.valid
    end

    def token_whitelist(token_whitelist)
      server_response = RestClient.get("#{url}/tokens/#{token_whitelist}", headers)
      self.status = server_response.code
      self.response = JSON.parse(server_response.body)
      date_range = DateRange.new(response['start_date'], response['end_date'])
      response[:message] = "El link es invalido. La campaña es entre #{DateTime.iso8601(response['start_date']).strftime("%d/%m/%Y")} y #{DateTime.iso8601(response['end_date']).strftime("%d/%m/%Y")}" unless requested_between?(date_range)
      self.valid = status == 200
    rescue RestClient::ExceptionWithResponse => e
      self.response = {}
      self.status = e.response.code
    rescue JSON::ParserError => e
      self.response = {}
      self.status = nil
    end

    private

    def prepare_data
      self.data['dept'] = data['depth']
      self.data = { 'insurance' => self.data }
    end

    def headers
        {:Authorization => "Bearer #{api_key}"}
    end

    def requested_between?(date_range)
      requested_time = DateTime.now
      requested_time >= date_range.start_date && requested_time <= date_range.end_date
    end
end
