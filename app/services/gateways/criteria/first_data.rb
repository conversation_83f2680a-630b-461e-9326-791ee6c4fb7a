module Gateways
  module Criteria
    class FirstData
      def initialize(args = {})
        @store = args[:store]
        @doc_number = args[:document_number]
        @items = args[:items]
      end

      def authorized?
        Dni.exists?(number: @doc_number, store: @store) && @items.all? { |item| item.shop.first_data_credentials.present? }
      end

      def name
        'firstdata_distributed'
      end
    end
  end
end
