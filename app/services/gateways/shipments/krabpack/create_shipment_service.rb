# frozen_string_literal: true

require 'uri'
require 'net/http'
require 'openssl'

module Gateways
  module Shipments
    module Krabpack
      class CreateShipmentService # rubocop:disable Metrics/ClassLength
        attr_accessor :valid, :label, :error

        def initialize(shipment:)
          @shipment = shipment
          @valid = false
          @api_key = @shipment.order.store_id == 21 ? KRABPACK_MACRO_APIKEY : KRABPACK_APIKEY
          store = @shipment.suborders.map(&:store).uniq.first
        end

        def perform
          basic_perform(build_params)
        end

        def refund(shipment_date, shipment_kind)
          basic_perform(build_refund_params(shipment_date, shipment_kind))
        end

        def exchange
          basic_perform(build_exchange_params)
        end

        def basic_perform(body)
          url = URI("#{KRABPACK_URL}/shipments")
          http = Net::HTTP.new(url.host, url.port)
          http.use_ssl = true
          http.verify_mode = OpenSSL::SSL::VERIFY_NONE

          request = Net::HTTP::Post.new(url)
          request['apikey'] = @api_key
          request['content-type'] = 'application/json'

          request.body = body
          response = http.request(request)
          case response.code
          when '200', '201'
            result = JSON.parse(response.body)
            delivery_time = result['delivery']['sla'].present? ? DateTime.now + result['delivery']['sla'].to_i.hours : nil
            @label = @shipment.labels.new({
                                            tracking_number: result['label']['tracking_number'],
                                            courier: result['delivery']['carrier'],
                                            price: result['cost'].to_f,
                                            estimated_delivery_date: delivery_time,
                                            gateway: 'krabpack',
                                            gateway_object_id: result['id'],
                                            gateway_data: {
                                              'label_url' => result['label']['label_url'],
                                              'status' => result['status']
                                            }
                                          })

            if @label.save
              Mkp::StatusChange::EntityStatusManage.status_change(@shipment, 'in_process')
              @valid = true
            else
              @error = label.errors.first
            end
          when '500'
            Rails.logger.info 'Krabpakc requst error'
            Rails.logger.info url
            Rails.logger.info request.body
            Rails.logger.info response.body
            @error = "Fallo al crear los envios: #{response.body}"
          end
        end

        def build_params
          { quote_item_id: @shipment.gateway_object_id }.to_json
        end

        def build_refund_params(shipment_date, shipment_kind)
          {
            "id": @shipment.gateway_object_id,
            "shipment_date": shipment_date,
            "delivery": {
              "mode": 'return',
              "with_change": shipment_kind == 'exchange_refund'
            },
            reference_id: @shipment.suborders.first.id,
            "parcels": build_items_data('refund'),
            "declared_value": @shipment.suborders.map(&:total).first
          }.to_json
        end

        def build_exchange_params
          {
            "id": @shipment.gateway_object_id,
            "delivery": {
              "mode": 'change',
              "with_change": false
            },
            reference_id: @shipment.suborders.first.id,
            "parcels": build_items_data('exchange'),
            "declared_value": @shipment.suborders.map(&:total).first
          }.to_json
        end

        def build_items_data(kind)
          products_data = []
          @shipment.items.each do |item|
            description =
              if kind == 'exchange'
                variant_sku = item.variant.sku
                if %w[PyPBM PyPBM-G].include?(@shipment.suborders.first.shop.title)
                  (variant_sku.slice! 3..variant_sku.size).to_s
                else
                  variant_sku
                end
              else
                item.title
              end
            products_data << {
              height: item.product.height.to_f / 100,
              length: item.product.length.to_f / 100,
              width: item.product.width.to_f / 100,
              weight: item.product.weight.to_f,
              description: description
            }
          end
          products_data
        end
      end
    end
  end
end
