require 'uri'
require 'net/http'
require 'openssl'

module Gateways
  module Shipments
    module Krabpack
      class SuborderDeliveryOptionsService
        attr_accessor :valid, :response

        def initialize(suborder:, address:, checkout_cart:)
          @suborder = suborder
          @address = address
          @checkout_cart = checkout_cart
          @valid = false
          shop = @suborder.shop
          @api_key = (((Mkp::Store.find 21).shops.include? shop) ? KRABPACK_MACRO_APIKEY : KRABPACK_APIKEY)
          @response = []
        end

        def perform
          url = URI("#{KRABPACK_URL}/shippingquote")
          http = Net::HTTP.new(url.host, url.port)
          http.use_ssl = true
          http.verify_mode = OpenSSL::SSL::VERIFY_NONE

          request = Net::HTTP::Post.new(url)
          request["apikey"] = @api_key
          request["content-type"] = 'application/json'

          request.body = build_params
          response = http.request(request)
          result = JSON.parse(response.body)

          if result['address'].present?
            result['address'].each do |option|
              @response << {
                carrier: option['carrier'],
                price: option['cost'].to_f,
                service: option['service'],
                quote_item_id: option['quote_item_id']
              }.with_indifferent_access
            end
            @response = @response.sort_by { |hsh| hsh[:price] }
            @valid = true
          end
        rescue JSON::ParserError => e
        end

        def build_params
          {
            contact: {
              firstname: @address.first_name,
              lastname: @address.last_name,
              phone: @address.telephone,
              email: @checkout_cart.customer.email
            },
            created_by: "kp-avenida-001",
            declared_value: @suborder.subtotal.to_f,
            from: {
                warehouse_id: @suborder.shop.warehouse&.smartcarrier_id
            },
            mode: ["address"],
            parcels: build_parcels,
            reference_id: "KP-#{@suborder.shop.id}-#{SecureRandom.hex.first(5)}",
            to: {
                street_name: @address.address,
                street_number: @address.street_number,
                apartment: @address.address_2.to_s.gsub(/[^0-9A-Za-z.°-]/, ' ').strip,
                city: @address.city,
                province: @address.state,
                postal_code: @address.zip
            }
          }.to_json
        end

        def build_parcels
          #centimeters, kilograms
          packages = Mkp::Shipping::Packages.new(@suborder.items).get_package_values(length: :centimeters, mass: :kilograms)
          packages.map do |package|
            variant_sku = package["sku"]
            description = variant_sku
            if @shipment
              if @shipment.suborders.first.shop.title == 'PyPBM' || @suborder.shop.title == 'PyPBM-G'
                description = "#{variant_sku.slice 3..variant_sku.size}"
              end
            end
            if @suborder
              if @suborder.shop.title == 'PyPBM' || @suborder.shop.title == 'PyPBM-G'
                description = "#{variant_sku.slice 3..variant_sku.size}"
              end
            end
            [{
              weight: package["peso"],
              width: package["ancho"],
              height: package["alto"],
              length: package["largo"],
              description: description
            }] * package['cantidad'].to_i
          end.flatten
        end
      end
    end
  end
end
