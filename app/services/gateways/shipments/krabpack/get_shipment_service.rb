# frozen_string_literal: true

require 'uri'
require 'net/http'
require 'openssl'

module Gateways
  module Shipments
    module Krabpack
      class GetShipmentService
        STATUS_MAP = {
          'draft' => 'unfulfilled',
          'processed' => 'in_process',
          'in_transit' => 'shipped',
          'delivered' => 'delivered',
          'cancelled' => 'cancelled',
          'not_delivered' => 'not_delivered',
          'failed' => 'in_process'
        }.freeze

        attr_accessor :valid, :label, :error, :result

        def initialize(label:)
          @label = label
          @valid = false
          @api_key = if @label.shipment.order.store_id == 21
                       KRABPACK_MACRO_APIKEY
                     else
                       KRABPACK_APIKEY
                     end
          end

        def perform
          url = URI("#{KRABPACK_URL}/shipments/#{@label.gateway_object_id}")
          http = Net::HTTP.new(url.host, url.port)
          http.use_ssl = true
          http.verify_mode = OpenSSL::SSL::VERIFY_NONE

          request = Net::HTTP::Get.new(url.request_uri)
          request['apikey'] = @api_key
          request['content-type'] = 'application/json'
          Rails.logger.info("API KEY #{@api_key}")
          response = http.request(request)
          Rails.logger.info("Response: #{response.code.to_s}")

          @result = JSON.parse(response.body)
          case response.code
          when '200'
            Rails.logger.info("Order: #{@label.shipment.order.id}")
            Rails.logger.info("OLD STATUS SHIPMENT: #{@label.shipment.status}")
            Rails.logger.info("NEW STATUS SHIPMENT: #{STATUS_MAP[@result['status']]}")
            new_status = STATUS_MAP[@result['status']]
            Mkp::StatusChange::EntityStatusManage.status_change(@label.shipment, new_status)
            Rails.logger.info("Status updated to: #{@label.shipment.status}")
            @label.shipment.suborders.each(&:touch)
            @valid = true
          when '500', '503'
            @error = "Error al consultar datos del envío #{response.code}"
          else
            Rails.logger.info("RESPONSE CODE FORM KRABPACK:\n------- #{response.code} -------")
          end
        rescue StandardError => e
          "Error estandar error #{e}"
        end
      end
    end
  end
end
