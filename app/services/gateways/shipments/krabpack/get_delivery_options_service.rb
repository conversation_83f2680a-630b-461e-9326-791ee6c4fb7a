require 'uri'
require 'net/http'

module Gateways
  module Shipments
    module Krabpack
      class GetDeliveryOptionsService
        attr_accessor :valid, :response

        def initialize(zip_code:, product:)
          @zip_code = zip_code
          @product = product
          @valid = false
          shop = @product.shop
          @api_key = (((Mkp::Store.find 21).shops.include? shop) ? KRABPACK_MACRO_APIKEY : KRABPACK_APIKEY)
          @response = []
        end

        def perform
          url = URI("#{KRABPACK_URL}/shippingquote")
          http = Net::HTTP.new(url.host, url.port)
          http.use_ssl = true
          http.verify_mode = OpenSSL::SSL::VERIFY_NONE

          request = Net::HTTP::Post.new(url)
          request['apikey'] = @api_key
          request['content-type'] = 'application/json'

          parcels = []
          @product.packages.each do |package|
            parcels.push({
                           height: @product.height.to_f / 100,
                           length: @product.length.to_f / 100,
                           width: @product.width.to_f / 100,
                           weight: @product.weight.to_f
                         })
          end

          body = {
            contact: {
              email: '<EMAIL>',
              firstname: 'TEST',
              lastname: 'AVENIDA',
              mobile: '122213213',
              personal_id: '11123123',
              phone: '1112341234'
            },
            created_by: 'kp-avenida-001',
            declared_value: @product.price.to_f,
            from: {
              warehouse_id: @product.shop.warehouse.smartcarrier_id
            },
            mode: ['address'],
            parcels: parcels,
            reference_id: "AVENIDA-#{@product.id}-#{Time.now.to_i}",
            to: {
              postal_code: @zip_code
            }
          }

          request.body = body.to_json
          response = http.request(request)

          if response.code == '200'
            result = JSON.parse(response.body)

            if result['address'].present?
              result['address'].each do |option|
                @response << { carrier: option['carrier'], price: option['cost'].to_f, message: option['service'], quote_item_id: option['quote_item_id'] }
              end
              @response = @response.sort_by { |hsh| hsh[:price] }
              @valid = true
            end
          end
        rescue JSON::ParserError => e
        end
      end
    end
  end
end
