require 'uri'
require 'net/http'

module Gateways
  module Shipments
    module Krabpack
      class GetShipmentNoUpdateService
        STATUS_MAP = {
          'draft' => 'unfulfilled',
          'processed' => 'in_process',
          'in_transit' => 'shipped',
          'delivered' => 'delivered',
          'cancelled' => 'cancelled',
          'not_delivered' => 'not_delivered',
          'failed' => 'in_process'
        }.freeze

        attr_accessor :valid, :label, :error, :result

        def initialize(label:)
          @label = label
          @valid = false
          store = @label.shipment.suborder.store
          @api_key = (store.name == 'bancomacro' ? KRABPACK_MACRO_APIKEY : KRABPACK_APIKEY)
        end

        def perform
          begin
            url = URI("#{KRABPACK_URL}/shipments/#{@label.gateway_object_id}")
            http = Net::HTTP.new(url.host, url.port)
            http.use_ssl = true

            request = Net::HTTP::Get.new(url.request_uri)
            request["apikey"] = @api_key
            request["content-type"] = 'application/json'

            response = http.request(request)

            case response.code
            when "200"
              @result = JSON.parse(response.body)
              @valid = true
            when "500", "503"
              @error = 'Error al consultar datos del envío'
            end
          rescue StandardError => e
            @error = 'Error al consultar datos del envío'
          end
        end
      end
    end
  end
end
