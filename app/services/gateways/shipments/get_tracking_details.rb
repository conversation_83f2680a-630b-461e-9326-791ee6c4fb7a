require 'uri'
require 'net/http'

module Gateways
  module Shipments
    class GetTrackingDetails
      attr_accessor :valid, :label, :error, :result

      def initialize(label:)
        @label = label
        @valid = false
      end

      def perform
        if label.present? && label.gateway.present?
          if label.gateway == 'krabpack'
            service = Gateways::Shipments::Krabpack::GetShipmentNoUpdateService.new(label: @label)
            service.perform

            if service.valid
              @result = response_tracking
              if service.result['trackingEvents'].present?
                if service.result['trackingEvents'].any?
                  service.result['trackingEvents'].each do |event|
                    @result[:events] << {
                      status: event['status'],
                      description: event['external_description'],
                      date: DateTime.parse(event['status_changed_date']).in_time_zone("Buenos Aires").strftime('%d-%m-%Y %H:%M')
                    }
                  end
                end
              end

              @valid = true
            else
              @error = service.error
            end
          else
            order_status = label.shipment.status
            if order_status.present?
              @result = response_tracking
              @valid = true
            else
              @error = "Orden enviada por logística privada"
            end
          end
        elsif label.present? && label.gateway.nil?
          @error = "Orden enviada por logística privada"
        else
          @error = "Orden inválida"
        end
      end

      private

      def response_tracking
        {
          courier: @label.courier.capitalize,
          icon: @label.icon,
          status: @label.shipment.status,
          tracking: @label.tracking_number,
          estimated_delivery_date: @label.estimated_delivery_date.present? ? @label.estimated_delivery_date.strftime('%d-%m-%Y %H:%M') : nil,
          events: []
        }
      end
    end
  end
end
