class PaymentsCredentials < ApplicationService
  SECS = 300

  def initialize(store)
    @store = store
  end

  def call
    Struct.new(:public_key, :private_key).new(public_key, private_key)
  end

  private

  def public_key
    Rails.cache.fetch("#{name}_public_key_store_#{@store.id}") { keys_store[:public_key] }
  end

  def private_key
    Rails.cache.fetch("#{name}_private_key_store_#{@store.id}") { keys_store[:private_key] }
  end

  def keys_store
    if strategy_first_payments?
      keys = keys_from_payments_service
      # Leer de platform en caso de tener estrategia payments_platform
      keys = keys_from_platform if keys.compact.blank? && @store.gateway_alternative_strategies.find_by(gateway: name)&.use_platform_as_alternative?
    else
      keys = keys_from_platform
    end
    Rails.cache.write("#{name}_public_key_store_#{@store.id}", keys[:public_key], expires_in: SECS.seconds)
    Rails.cache.write("#{name}_private_key_store_#{@store.id}", keys[:private_key], expires_in: SECS.seconds)
    {
      public_key: keys[:public_key],
      private_key: keys[:private_key]
    }
  end

  def keys_from_payments_service
    # Esta anulado el endpoint que devuelve la public_key
    # se usa la key decidir-public-key de PaymentCredential hasta que vuelva a funcionar
    # config = @store.payments_service.credentials(self.class::PAYMENTS_GATEWAY_ID)
    decidir_public_key = @store.payment_credentials.find_by(name: key_field_public_key)&.value
    decidir_private_key = nil # @store.payment_credentials.find_by(name: 'decidir_private_key')
    { public_key: decidir_public_key, private_key: decidir_private_key }
  end

  def keys_from_platform
    { public_key: credentials_platform.public_key, private_key: credentials_platform.private_key }
  end

  def strategy_first_payments?
    @strategy_first_payments ||= @store.gateway_alternative_strategies.find_by(gateway: name)&.use_payments?
  end

  def credentials_platform
    @credentials_platform ||= {}
  end
end
