module Equifax
  class PendingQuestions < ApplicationService
    attr_reader :doc_number, :gender, :purchase_id, :pending_questions, :current_store, :equifax_token_api_endpoint, :equifax_client_api_endpoint

    def initialize(args = {})
      @doc_number = args[:doc_number]
      @gender = args[:gender]
      @purchase_id = args[:purchase_id]
      @current_store = args[:current_store]
      @pending_questions = nil
      @equifax_token_api_endpoint = Rails.env.production? ? EQUIFAX_TOKEN_API_ENDPOINT_PRD : EQUIFAX_TOKEN_API_ENDPOINT_UAT
      @equifax_client_api_endpoint = Rails.env.production? ? EQUIFAX_CLIENT_API_ENDPOINT_PRD : EQUIFAX_CLIENT_API_ENDPOINT_UAT
    end

    def call
      begin
        access_token = AvenidaWrappers::Equifax::Auth.new(
          username: current_store.equifax_id_validator_configuration.s2s_user,
          password: current_store.equifax_id_validator_configuration.s2s_password,
          equifax_token_api_endpoint: equifax_token_api_endpoint
        ).auth["access_token"]

        equifax_log.info("#{access_token}")

        questionnaire = AvenidaWrappers::Equifax::Client.new(
          auth_token: access_token,
          equifax_client_api_endpoint: equifax_client_api_endpoint
        ).full(doc_number, gender_customer_to_equifax(gender))

        questions_result = process_questions(questionnaire)

        return OpenStruct.new(type: :equifax, questions: questions_result[0], questions_data: questions_result[1], status: :ok) unless questions_result[1].key? :error

        OpenStruct.new(type: :equifax, message: questions_result[1][:error], status: :bad_request)
      rescue => e
        equifax_log.info("error: #{e}")
        OpenStruct.new(type: :equifax, message: e.message, status: :bad_request)
      end
    end

    private

    def equifax_log
      equifax_log ||= Logger.new("#{Rails.root}/log/equifax.log")
    end

    def process_questions(questionnaire_data)
      if questionnaire_data['message'] == "OK"
        questionnaire = questionnaire_data['payload']['questionnaire']
        questions = questionnaire['questionsOfGeneratedQuestionnaire']
        questions_result = questions.map do |question|
          IdentityValidation::Questions.new(id: question['id'], question: question['description'], answers: process_answers(question['options']))
        end
        [questions_result, {transaction_id: questionnaire_data['payload']['idTransaccion'], questionnaire_id: questionnaire['id']}]
      else
        [[],{ error: questionnaire_data['errors'].first['description']}]
      end
    end

    def process_answers(options)
      options.map do |answer|
        IdentityValidation::Answers.new(id: answer['id'], answer: answer['description'])
      end
    end

    def gender_customer_to_equifax(gender)
      case gender
        when 0, 'male'
          'M'
        when 1, 'female'
          'F'
        when 2, 'non_binary'
          'X'
      end
    end
  end
end
