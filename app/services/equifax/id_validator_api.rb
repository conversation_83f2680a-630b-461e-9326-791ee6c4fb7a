module Equifax
  class IdValidatorApi
    ID_VALIDATOR_URL = 'https://online.org.veraz.com.ar/WsIDValidator/services/idvalidator?wsdl'
    attr_reader :configuration, :store

    def initialize(store)
      @url = ID_VALIDATOR_URL
      @configuration = Equifax::IdValidatorConfiguration.find_by(store: store)
    end

    def mensaje_request(type, data)
      raise StandardError, "Faltan parametros documento o sexo" unless data[:documento].present? && data[:sexo].present?

      client.call(:mensaje_request, xml: xml_request(type, data))
    end
  
    def obtener_preguntas(data)
      raise StandardError, "Faltan parametros documento o sexo" unless data[:documento].present? && data[:sexo].present?
      
      client.call(:obtener_preguntas, xml: xml_request(:obtener_preguntas, data))
    end

    private
  
    def client(*args)
      @client ||= Savon.client({ wsdl: @url })
    end

    def xml_request(type, data)
      Equifax::IdValidator::XmlGenerator.generate(configuration, type, data)
    end

  end
end