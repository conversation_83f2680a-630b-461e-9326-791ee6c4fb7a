module Renaper
  class PendingQuestions < ApplicationService
    attr_reader :doc_number, :gender, :purchase_id, :pending_questions, :current_store

    include Api::AngularApp::V1::Concerns::RenaperVerifiable
    
    def initialize(args = {})
      @doc_number = args[:doc_number]
      @gender = args[:gender]
      @purchase_id = args[:purchase_id]
      @current_store = args[:current_store]
    end

    def call
      pending_question = pending_data_entry_question({:doc_number => doc_number, :gender => gender_customer_to_renaper(gender)})
      if pending_question
        pending_question.update(purchase_id: purchase_id) if purchase_id != pending_question.purchase_id
        return OpenStruct.new(type: :renaper, questions: process_questions([pending_question.as_renaper_hash]), status: :ok)
      end
      
      
      renaper_auth = Renaper::AuthToken.call
      return renaper_exception('No se pudo obtener access_token') if renaper_auth['access_token'].blank?

      status_response = Renaper::UserIdentityQuestions.call(renaper_auth['access_token'], {'doc_number' => doc_number, 'gender' => gender_customer_to_renaper(gender)})
      status, response = status_response[:status], status_response[:response]
      
      return renaper_exception("#{status} - #{response}") unless [200,400].include? status.to_i

      return OpenStruct.new(success: false, message: response['message'], status: :bad_request) if response['code'] == 'BAD_REQUEST'
      
      question = select_and_create_data_entry_question(response, purchase_id, doc_number, gender_customer_to_renaper(gender))
      return OpenStruct.new(type: :renaper, questions: process_questions([question]), status: :ok)
    end

    private 
    
    def renaper_exception(description)
      add_renaper_exception(store: current_store, purchase_id: purchase_id, doc_number: doc_number, description: description)
      OpenStruct.new(success: false, message: 'No se pudo obtener pregunta de identidad', status: :internal_server_error, data_entry_questions: nil)
    end

    def process_questions(all_questions)
      remove_correct_data_entry_questions(adapt_questions_response(all_questions))
    end
  end
end