require 'uri'
require 'net/http'
require 'json'

module Renaper
  class AuthToken < ApplicationService
    attr_accessor :response, :status

    def initialize
      @auth_url = URI(RENAPER_URL_AUTH)
    end

    def call
      https = Net::HTTP.new(@auth_url.host, @auth_url.port)
      https.use_ssl = true
      request = Net::HTTP::Post.new(@auth_url)
      request["Content-Type"] = "application/x-www-form-urlencoded"
      request.body = "grant_type=#{RENAPER_GRANT_TYPE}&client_id=#{RENAPER_CLIENT_ID}&username=#{RENAPER_USERNAME}&password=#{RENAPER_PASSWORD}"
      response_request = https.request(request)
      self.status = response_request.code
      self.response = JSON.parse response_request.body
    rescue => e
      Rails.logger.info e
      self.response = {}
    end

  end
end