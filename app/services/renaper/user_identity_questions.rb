require 'uri'
require 'net/http'
require 'json'

module Renaper
  class UserIdentityQuestions < ApplicationService
    attr_accessor :response, :status

    def initialize(access_token, identity)
      @access_token = access_token
      @doc_number = identity['doc_number'].strip
      @gender = identity['gender'].strip
      @url = URI("#{RENAPER_URL.chomp('/')}/v1/identityQuestions/getUserIdentityQuestions/#{@doc_number}/#{@gender}")
    end

    def call
      https = Net::HTTP.new(@url.host, @url.port)
      https.use_ssl = true
      request = Net::HTTP::Get.new(@url)
      request['Content-Type'] = 'application/json'
      request['Authorization'] = "Bearer #{@access_token}"
      response_request = https.request(request)
      self.status = response_request.code
      self.response = JSON.parse response_request.body
      { status: status, response: response }
    rescue JSON::ParserError => e
      Rails.logger.info e
      self.response = {}
      { status: status, response: response }
    end
  end
end
