module Encrypt
  class EncryptService
    #Proceso de desencriptación de datos cuando el frontend usa CrytoJS

    def self.decrypt(encrypted_data, password)
      new.decrypt(encrypted_data, password)
    end

    def decrypt(encrypted_data, password)
      data = Base64.decode64(encrypted_data)

      raise "Invalid format" unless data[0..7] == "Salted__"
      salt = data[8..15]
      ct = data[16..-1]

      md5 = OpenSSL::Digest::MD5.new
      d1 = md5.digest(password + salt)
      d2 = md5.digest(d1 + password + salt)

      key = d1 + d2
      iv = md5.digest(d2 + password + salt)[0, 16]

      decipher = OpenSSL::Cipher.new('aes-256-cbc')
      decipher.decrypt
      decipher.key = key[0, 32]
      decipher.iv = iv
      decipher.padding = 1


      plain = decipher.update(ct)
      plain << decipher.final

      plain.force_encoding('UTF-8')
    rescue OpenSSL::Cipher::CipherError => e
      Rails.logger.error("Error desencriptando: #{e.message}")
      nil
    rescue StandardError => e
      Rails.logger.error("Error inesperado: #{e.message}")
      nil
    end
  end
end
