module Ochenta
  class ProductWarranty
    attr_reader :warranties, :shop_id, :category_id, :manufacturer_id

    def initialize(args = {})
      @warranties = args[:warranties]
      @shop_id = args[:shop_id]
      @category_id = args[:category_id]
      @manufacturer_id = args[:manufacturer_id]
    end


    def create_or_update
      warranties.each do |warranty|
        product_attributes = build_product_attributes(warranty)
        sku = "Ochenta-#{warranty.provider_coverage_id}"
        variant_attributes = build_variant_attributes(warranty, sku)

        variants = Mkp::Variant.where('properties LIKE ?', '%external%').detect{|v|  v.properties[:external] == warranty.provider_coverage_id }

        next unless variants.nil?

        ActiveRecord::Base.transaction do
          create_warranty(product_attributes, variant_attributes)
        end
      end
    end

    def build_product_attributes(warranty)
      transaction_type = :warranties

      {
        title: warranty.name,
        description: warranty.description,
        category_id: @category_id,
        brand: "Grupo Hawk",
        manufacturer_id: manufacturer_id,
        regular_price: warranty.coefficient,
        iva: 0,
        available_on: Time.zone.now - 1.day,
        sale_on: warranty.start_date,
        sale_until: warranty.end_date,
        sale_price: warranty.coefficient,
        currency_id: 1,
        transaction_type: transaction_type,
        origin_of_product: 0,
        handle_stock: true,
        shop_id: shop_id,
        packages_attributes: [
          {
            width: 0,
            height: 0,
            length: 0,
            weight: 0,
            length_unit: Network[Network.default].length_unit,
            mass_unit: Network[Network.default].mass_unit
          }
        ],
        available_properties: [
          {
            period: warranty.terms_in_months,
            external: warranty.provider_coverage_id,
            articulo: warranty.andromeda_code_category_article
          }
        ]
      }
    end

    def build_variant_attributes(warranty, sku)
      {
        sku: sku,
        quantity: 1000000,
        properties: {
          period: warranty.terms_in_months,
          external: warranty.provider_coverage_id,
          articulo: warranty.andromeda_code_category_article
        }
      }
    end

    def create_warranty(product_attributes, variant_attributes)
      description = product_attributes[:description] || product_attributes[:title]
      product = Mkp::Product.create(product_attributes.merge(description: description))
      variant = Mkp::Variant.new(variant_attributes.merge(product_id: product.id))
      url_picture = 'https://statics.avenida.com/logos/garantia_extendida.jpg'
      AvenidaProductPictureWorker.perform_async(product.id, url_picture, 1)

      variant.send(:build_gp_sku, [])
      variant.save!
    end
  end
end
