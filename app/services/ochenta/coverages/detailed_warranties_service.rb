module Ochenta
  module Coverages
    class DetailedWarrantiesService < ApplicationService

      def call
        auth_token = AvenidaWrappers::Ochenta::Auth.new(
          username: <PERSON>CHENTA_USERNAME,
          password: OCHENTA_PASSWORD,
          client_id: OCHENTA_CLIENT_ID,
          ochenta_api_endpoint: OCHENTA_API_ENDPOINT
        ).auth['authToken']

        client = AvenidaWrappers::Ochenta::Client.new(auth_token: auth_token, ochenta_api_endpoint: OCHENTA_API_ENDPOINT)
        response = client.detailed_extended_warranties
      rescue StandardError => e
        OpenStruct.new({ success?: false, error: e })
      else
        OpenStruct.new({ success?: true, payload: response })
      end
    end
  end
end
