module Ochenta
  module Coverages
    class InsurancesService < ApplicationService
      def initialize(args = {})
        @body_request = args[:body_request]
      end

      def call
        auth_token = ::AvenidaWrappers::Ochenta::Auth.new(
          username: <PERSON>CHEN<PERSON>_USERNAME,
          password: <PERSON>CH<PERSON><PERSON>_PASSWORD,
          client_id: OCHENTA_CLIENT_ID,
          ochenta_api_endpoint: OCHENTA_API_ENDPOINT
        ).auth['authToken']

        client = ::AvenidaWrappers::Ochenta::Client.new(auth_token: auth_token, ochenta_api_endpoint: OCHENTA_API_ENDPOINT)
        response = client.insurances(@body_request)
      rescue StandardError => e
        OpenStruct.new({ success?: false, error: e })
      else
        OpenStruct.new({ success?: true, payload: response })
      end
    end
  end
end
