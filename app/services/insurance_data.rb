class InsuranceData
  attr_accessor :data, :errors

  def initialize(data)
    @data = data
    @errors = ''
  end

  def valid?
    @errors = blanks(data)
    @errors.empty?
  end

  def blanks(hash)
    keys = form_keys - optional_keys
    keys.select do |key|
      hash[key].blank?
    end
  end

  private
  
  def form_keys
    ["name", "email", "phone", "birthday", "birth_location", "nationality", "gender", "civil_status", "profession", "doc_type", "doc_number", "address", "street_number", "depth", "country", "state", "city", "postal_code", "political_exposure"]
  end

  def optional_keys
    ['depth', 'political_exposure']
  end
  
  def extra_keys
    ['installments',
    'amount',
    'card_number',
    'card_code',
    'card_month',
    'card_year', 
    'reference_id']
    #:extra_data]
  end

  
end
