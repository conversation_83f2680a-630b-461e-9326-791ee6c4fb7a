class AllBillingsService
  attr_reader :shipment, :customer

  def initialize(args = {})
    @shipment = args[:shipment]
    @customer = shipment.order.customer
  end

  def perform
    email = customer.email
    instance = Mkp::BillingBuilder::BuildBillingAddress.new(shipment: shipment, email: email)
    Mkp::BillingBuilder::FillBillingAddress.new(shipment: shipment, instance: instance).fill
    
    address = customer.addresses.first
    instance = Mkp::BillingBuilder::BuildCustomerBillingAddress.new(address: address, customer: customer).build
    address.update(billing_address: instance)
  end
end
