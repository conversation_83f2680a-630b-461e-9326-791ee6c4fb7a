module Checkout
  class ValidateProductAvailability
    attr_accessor :valid, :error

    def initialize(items, store)
      @items = items
      @store = store
      @valid = true
    end

    def perform # rubocop:disable Metrics/MethodLength
      return if @items.blank?

      @items.each do |item|
        variant = Mkp::Variant.find item[:variant_id]
        if variant.present?
          unless valid_variant?(variant)
            @valid = false
            @error = "Producto sin stock: #{variant.product.title}"
          end
        else
          @valid = false
          @error = 'Error verificando stock'
        end
      end
    end

    def valid_variant?(variant)
      valid = true
      if variant.product.reservable? && variant.product.third_party_code_required && @store.id != 43
        valid = third_party_code_validation?(variant)
      end

      variant.product.available? && variant.quantity.positive? && valid
    end

    def third_party_code_validation?(variant)
      ThirdPartyCode.where(store: @store,
                           manufacturer: variant.product.manufacturer,
                           available: true).count.positive?
    end
  end
end
