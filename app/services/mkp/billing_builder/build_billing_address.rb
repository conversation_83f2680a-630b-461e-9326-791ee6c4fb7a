module Mkp
  module BillingBuilder
    class BuildBillingAddress

      attr_reader :shipment, :email, :destination_address, :address

      def initialize(args)
        @shipment = args[:shipment]
        @email = args[:email]
        @destination_address = @shipment.destination_address
        @address = @shipment.address
      end

      def build
        OpenStruct.new(attributes)
      end

      private

      def attributes
        {
          name: address.full_name,
          doc_type: destination_address['doc_type'],
          doc_number: destination_address['doc_number'],
          email: email,
          phone: destination_address['telephone'],
          address: destination_address['address'],
          street_number: destination_address['street_number'],
          city: destination_address['city'],
          postal_code: destination_address['zip'],
          state: destination_address['state'],
          country: destination_address['country']
        }
      end
    end
  end
end