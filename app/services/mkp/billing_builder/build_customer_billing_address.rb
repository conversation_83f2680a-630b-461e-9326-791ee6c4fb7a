module Mkp
  module BillingBuilder
    class BuildCustomerBillingAddress

      attr_reader :address, :customer

      def initialize(args)
        @customer = args[:customer]
        @address = args[:address]
      end

      def build
        OpenStruct.new(attributes)
      end

      private

      def attributes
        {
          email: customer.email,
          name: customer.full_name,
          phone: customer.telephone,
          doc_number: customer.doc_number,
          doc_type: address.doc_type,
          address: address.address,
          street_number: address.street_number,
          city: address.city,
          postal_code: address.zip,
          state: address.state,
          country: address.country
        }
      end
    end
  end
end