module Mkp
  module BillingBuilder
    class FillBillingAddress

      attr_reader :shipment, :instance, :new_address

      def initialize(args)
        @shipment = args[:shipment]
        @instance = args[:instance]
        @new_address = @shipment.address
      end

      def fill
        new_address.billing_address = instance.build
        shipment.update(destination_address: new_address)
      end
    end
  end
end