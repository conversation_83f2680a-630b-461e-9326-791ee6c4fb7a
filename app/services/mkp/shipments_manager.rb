# frozen_string_literal: true

module Mkp
  # Class to manage shipment creation
  class ShipmentsManager
    attr_accessor :default_shipment_status

    STRATEGIES = %w[suborder product].freeze

    def self.options_for_select
      STRATEGIES
    end

    def initialize(order, suborder, items,
                   choosen_delivery_option,
                   destination_address, shipment = nil)
      @order = order
      @suborder = suborder
      @shop = suborder.shop
      @delivery_option = choosen_delivery_option
      @origin_address = build_address(@shop.warehouses.first)
      @destination_address = build_address(destination_address)
      @base_shipment = shipment
      @items = items
    end

    def create_shipment(shipment_kind)
      send("create_shipment_by_#{@shop.shipment_strategy}",
           shipment_kind)
    end

    def create_shipment_by_suborder(shipment_kind)
      basic_create_shipment(shipment_kind, @suborder.items)
    end

    def create_shipment_by_product(shipment_kind)
      @items.each do |item|
        basic_create_shipment(shipment_kind, [item])
      end
    end

    def basic_create_shipment(shipment_kind, items)
      pickup_origin_address = nil

      if shipment_kind == 'pickup' && @delivery_option && @delivery_option.pickup && @delivery_option.pickup_address.present?
        @shop.warehouses.each do |warehouse|
          if warehouse.pickup &&
             (warehouse.address.include?(@delivery_option.carrier) ||
              @delivery_option.pickup_address.include?(warehouse.address))
            pickup_origin_address = build_address(warehouse)
            break
          end
        end
      end

      shipment = Mkp::Shipment.create! do |shipment_object|
        shipment_object.origin_address = pickup_origin_address || @origin_address
        shipment_object.destination_address = @destination_address

        send("complete_#{shipment_kind}_shipment", shipment_object)

        shipment_object.sale_item = @order
        shipment_object.suborder = @suborder
        shipment_object.items = items
        shipment_object.status = default_shipment_status if default_shipment_status.present?
      end

      # Por defecto los shipments se crean con el estado 'unfullfilled' por lo que directamente genero un registro
      # de entity_status_change
      entity_status_create_class.create(entity: shipment, status: shipment.status)

      entity_status_manage_class.status_change(shipment, 'delivered') if shipment.virtual?

      # Notifico a los items asociados al shipment que deben cambiar su estado al mismo que el envio por que por defecto
      # se crean con estado _Available_.
      shipment.items.each do |item|
        item.product.transaction_type == 'warranties' ? entity_status_manage_class.status_change(item, 'delivered') :
        entity_status_manage_class.status_change(item, shipment.status)
      end
      shipment.save
    end

    private

    def build_address(from)
      return from if from.is_a? OpenStruct

      OpenStruct.new(
        from.attributes.merge(
          full_name: from.full_name
        )
      ).freeze
    end

    def complete_delivery_shipment(shipment_object)
      shipment_object.gateway = @shop.delivery_by_matrix ? @delivery_option.carrier : 'Krabpack'
      shipment_object.gateway_object_id = @delivery_option.external_shipment_id
      shipment_object.gateway_data = {
        title: @delivery_option.title,
        service_level: @delivery_option.service_level,
        carrier: @delivery_option.carrier,
        external_shipment_id: @delivery_option.external_shipment_id
      }
      shipment_object.charged_amount = @delivery_option.charge
      shipment_object.extra_info.tap do |hash|
        hash[:service_name] = @delivery_option.title
        hash[:courier] = @delivery_option.carrier
      end
      shipment_object.shipment_kind = 'delivery'
    end

    def complete_refund_shipment(shipment_object)
      complete_from_base_shipment(shipment_object)
      shipment_object.shipment_kind = 'refund'
    end

    def complete_virtual_shipment(shipment_object)
      shipment_object.shipment_kind = 'virtual'
    end

    def complete_pickup_shipment(shipment_object)
      shipment_object.shipment_kind = 'pickup'
    end

    def complete_exchange_change_shipment(shipment_object)
      complete_from_base_shipment(shipment_object)
      shipment_object.shipment_kind = 'exchange_change'
    end

    def complete_exchange_refund_shipment(shipment_object)
      complete_from_base_shipment(shipment_object)
      shipment_object.shipment_kind = 'exchange_refund'
    end

    def complete_from_base_shipment(shipment_object)
      shipment_object.gateway = @base_shipment.gateway_name
      shipment_object.gateway_object_id = (@base_shipment.label ? @base_shipment.label.gateway_object_id : @base_shipment.gateway_object_id)
      shipment_object.gateway_data = {}
      shipment_object.charged_amount = 0.0
      shipment_object.extra_info = @base_shipment.extra_info
    end

    def entity_status_manage_class
      Mkp::StatusChange::EntityStatusManage
    end

    def entity_status_create_class
      Mkp::StatusChange::EntityStatusChange
    end
  end
end
