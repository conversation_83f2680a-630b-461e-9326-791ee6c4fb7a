module Mkp
  module Catalog
    module Finder
      module_function

      PER_PAGE = 48.freeze
      PARAMS_ORDER_ATTRS = %w(price available_on).freeze
      PARAMS_ORDER_ORDERS = %w(asc desc).freeze
      PARAMS_ORDER_REGEX = Regexp.new("^(#{PARAMS_ORDER_ATTRS.join('|')})\-(#{PARAMS_ORDER_ORDERS.join('|')})$").freeze

      def find(params, options = {})
        options[:per_page] ||= PER_PAGE
        debug = options[:debug]

        network          = obtain_full_network params[:network]
        is_thin_network  = Network[params[:network]].thin?
        thin_network     = params[:network].upcase if is_thin_network
        store = Mkp::Store.find params[:store_id] if params[:store_id].present?

        store_shops = store&.active_shops&.pluck(:shop_id)
        store_shops = nil unless store_shops.present? # Passing an empty array is equivalent to a no-op

        Mkp::Variant.search(include: variant_includes) do
          if params[:query].present?
            fulltext params[:query] do
              boost_fields title: 90.0, category_name: 100.0, manufacturer_name: 49.0, description: 20.0, shop_name: 48.0, data: 15.0
              phrase_fields title: 90.0, category_name: 100.0, manufacturer_name: 49.0, description: 20.0, shop_name: 48.0, data: 15.0
              phrase_slop 1
              query_phrase_slop 1
              minimum_match 1
              highlight :title, :category_name, :manufacturer_name, :description, :shop_name, :data
            end
          end

          without(:categories_friendly_ids, ['gear']) if params[:g].present? && params[:c].nil?
          with_mfr = with :manufacturer_id, params[:b] if params[:b].present?

          with :custom_properties, "cilindrada-#{params[:cil].downcase}" if params[:cil].present?
          with :custom_properties, "tipo de vehiculo-#{params[:vt].downcase}" if params[:vt].present?
          with :custom_properties, "motorizacion-#{params[:mt].downcase}" if params[:mt].present?

          if params[:c].present?
            with_category = any_of do
              with :categories_ids, params[:c]
              with :categories_friendly_ids, params[:c]
            end
          end

          if store.present?
            with :store_id, store.id
            with :approved_for_store_ids, store.id if store.product_approval
          end
          
          with :shop_id, params[:shops].present? ? params[:shops] : store_shops

          with_size = with :available_sizes, params[:sz] if params[:sz].present?
          with_color = with :color_hex, params[:clr] if params[:clr].present?
          with :shop_friendly_id, params[:sh] if params[:sh].present?
          with(:price).less_than_or_equal_to params[:price].to_i if params[:price].present?

          if params.key?(:d)
            any_of do
              all_of do
                with :sale_fields_are_present, true
                with(:sale_on).less_than_or_equal_to Time.zone.now
                with(:sale_until).greater_than_or_equal_to Time.zone.now
              end
              all_of do
                with(:shop_massive_discount_on).less_than_or_equal_to Date.today
                with(:shop_massive_discount_until).greater_than_or_equal_to Date.today
              end
            end
          end

          if options[:exclude_products].present?
            without :product_id, options[:exclude_products]
          end

          #with :purchasable, true
          with :deleted, false
          with :shop_visible, true
          with :shop_deleted, false
          with :display_variant, true
          with :network, network
          with(:quantity).greater_than_or_equal_to 1
          with(:available_on).less_than Time.zone.now

          if params[:payment_type].present?
            if params[:payment_type] == 'points'
              any_of do
                with :transaction_type, 'points'
                with :transaction_type, 'voucher'
              end
            end
          end

          if is_thin_network
            any_of do
              with :available_countries, thin_network
              with :available_countries, 'all'
            end
          end

          ordering_params = if params[:o].present?
            Finder.get_order(params)
          elsif params[:query].present?
            if store.present?
              # store.preferred_sorting.presence ? store.preferred_sorting : [:score, :desc]
              store.preferred_sorting.presence ? store.preferred_sorting : store.strategy_finder.solr_sort_criteria_query
            end
          else
            if store.present?
              store.preferred_sorting.presence ? store.preferred_sorting : store.strategy_finder.solr_sort_criteria
            end
          end
          
          if ordering_params.present?
            order_by *ordering_params
          end

          if store.present? 
            if store.strategy_finder.order_visibility_enabled?
              # mover columna mkp_shops.order_visibility a mkp_shop_stores y obtener ese atributo por store
              order_by :product_order_visibility, :desc
              order_by :shop_order_visibility, :desc
            end
          end

          # TODO: @horacio
          # adjust_solr_params do |solr_params|
          #   solr_params[:sort] = "field(variant_ranking) desc" unless ordering_params.present?
          #   solr_params[:boost] = "variant_ranking"
          #   solr_params[:fl] = "*, variant_ranking:field(variant_ranking)" if debug
          # end

          paginate page: (params[:p] || 1), per_page: options[:per_page]

          unless options[:without_facets]
            # facet :genders_ids, exclude: with_gender
            # facet :sports_ids, exclude: with_sportupdate_attribute(:processing, true)update_attribute(:processing, true)
            facet :manufacturer_id, exclude: with_mfr, limit: -1, sort: :count
            facet :shop_id, limit: -1, sort: :count
            facet :categories_ids, exclude: with_category, limit: -1, sort: :count
            facet :available_sizes, exclude: with_size, sort: :count
            facet :color_hex, exclude: with_color
            facet :on_sale do
              row(:on_sale) do
                any_of do
                  all_of do
                    with :sale_fields_are_present, true
                    with(:sale_on).less_than_or_equal_to Time.zone.now
                    with(:sale_until).greater_than_or_equal_to Time.zone.now
                  end
                  all_of do
                    with(:shop_massive_discount_on).less_than_or_equal_to Date.today
                    with(:shop_massive_discount_until).greater_than_or_equal_to Date.today
                  end
                end
              end
            end

            stats :price

            if params[:pr].present?
              minimum, maximum = params[:pr].split('..').map(&:to_i)
              with(:price).greater_than_or_equal_to(minimum) if minimum.present? && minimum >= 0
              with(:price).less_than_or_equal_to(maximum) if maximum.present? && maximum >= 0
            end
          end

        end
      end

      def obtain_full_network(network)
        Network[network].thin? ? Network.default : network.upcase
      end

      def get_range_interval_by_network(network)
        # TODO: return custom range intervals by network
        150
      end

      def variant_includes
        [:picture, product: [:shop, :order_items, :currency, :category, :manufacturer, :pictures, :sports]]
      end

      def get_order(params)
        return if params[:o].blank? || params[:o] == "popular-desc"
        if _order = PARAMS_ORDER_REGEX.match(params[:o]).try(:captures)
          _order.map!(&:to_sym)
        end
        _order
      end

    end
  end
end
