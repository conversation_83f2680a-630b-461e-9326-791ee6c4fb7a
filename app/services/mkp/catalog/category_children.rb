module Mkp
  module Catalog
    class CategoryChildren
      attr_reader :children

      def initialize(args={})
        @children = []
      end

      def perform(category_ids)
        first, *rest = category_ids

        child_ids = Mkp::Category.find(first).child_ids

        children << [first] + child_ids

        if child_ids.empty?
          return
        else
          perform(rest + child_ids)
        end
      end
    end
  end
end
