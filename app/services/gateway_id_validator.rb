class GatewayIdValidator < ApplicationService
  attr_reader :quiz, :questions, :gender, :doc_number, :current_store, :purchase_id
  
  def initialize(args = {})
    @current_store = args[:current_store]
    @quiz = args[:quiz]
    @purchase_id = args[:purchase_id]
    @questions = args[:questions]
    @gender = args[:gender]
    @doc_number =  args[:doc_number]
  end

  def call
    quiz_instance = IdentityValidation::Quiz.new(type: quiz[:type],
                      questions: quiz[:questions],
                      transaction_id: quiz[:transaction_id],
                      questionnaire_id: quiz[:questionnaire_id])
    request = IdentityValidation::Request.new(doc_number: doc_number, gender: gender, quiz: quiz_instance, purchase_id: purchase_id)
    
    response = nil
    case quiz_instance.type
    when "equifax"
      if quiz_instance.pending_questions?
        begin
          response = Equifax::AttemptAnswer.call(
            doc_number: doc_number,
            gender: gender,
            current_store: current_store,
            quiz: quiz_instance)
        rescue AvenidaWrappers::ApiExceptions::BadRequestError
          return OpenStruct.new(valid?: false, error: 'No se pudo responder completamente', status: 404)
        rescue 
          return OpenStruct.new(valid?: false, error: 'No se pudo completar la validación de identidad', status: 403)
        end
      else
        begin
          response = Equifax::PendingQuestions.call(doc_number: doc_number, gender: gender,
            purchase_id: purchase_id, current_store: current_store)
        rescue
          return OpenStruct.new(valid?: false, error: 'Servicio de Identidad no disponible.', status: 404)
        end
      end
    when "renaper"
      begin
        if quiz_instance.pending_questions?
          response = Renaper::AttemptAnswer.call(
            doc_number: doc_number,
            gender: gender,
            current_store: current_store,
            questions: quiz_instance.questions)
        else
          response = Renaper::PendingQuestions.call(doc_number: doc_number, gender: gender,
            purchase_id: purchase_id, current_store: current_store)
        end
      rescue
        return OpenStruct.new(valid?: false, error: 'No se pudo completar la validación de identidad', status: 403)
      end
    end

    if response.questions.present?
      if response.questions_data
        quiz_instance.transaction_id = response.questions_data[:transaction_id]
        quiz_instance.questionnaire_id = response.questions_data[:questionnaire_id]
      end
      quiz_instance.questions = response.questions
      request.token = response.token if response.token
      OpenStruct.new(valid?: true, payload: request, status: response.status)
    else
      OpenStruct.new(valid?: false, error: response.message, status: response.status || :bad_request)
    end
  end
end
