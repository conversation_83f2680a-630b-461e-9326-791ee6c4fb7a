module Loyalty::ApiExceptions
  # APIExceptionError class inherits from StandardError class.
  ApiExceptionError = Class.new(StandardError)
  # BadRequestError class inherits from APIExceptionError class.
  BadRequestError = Class.new(ApiExceptionError)
  # UnauthorizedError class inherits from APIExceptionError class.
  UnauthorizedError = Class.new(ApiExceptionError)
  # ForbiddenError class inherits from APIExceptionError class.
  ForbiddenError = Class.new(ApiExceptionError)
  # ApiRequestsQuotaReachedError class inherits from APIExceptionError class.
  ApiRequestsQuotaReachedError = Class.new(ApiExceptionError)
  # NotFoundError class inherits from APIExceptionError class.
  NotFoundError = Class.new(ApiExceptionError)
  # UnprocessableEntityError class inherits from APIExceptionError class.
  UnprocessableEntityError = Class.new(ApiExceptionError)
  # ApiError class inherits from APIExceptionError class.
  ApiError = Class.new(ApiExceptionError)
end
