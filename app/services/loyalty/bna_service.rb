class Loyalty::BnaService
  include Loyalty::ApiExceptions
  include Loyalty::HttpClient

  BASE_URL = BNA_LOYALTY_BASE_URL
  SSO_URL = BNA_LOYALTY_SSO_URL
  SSO_CLIENT_ID = BNA_LOYALTY_SSO_CLIENT_ID
  SSO_CLIENT_SECRET = BNA_LOYALTY_SSO_CLIENT_SECRET
  SSO_SCOPE = BNA_LOYALTY_SSO_SCOPE
  SSO_TOKEN_ENDPOINT = BNA_LOYALTY_SSO_TOKEN_ENDPOINT
  SSO_USERNAME = BNA_LOYALTY_SSO_USERNAME
  SSO_PASSWORD = BNA_LOYALTY_SSO_PASSWORD

  @@connection = nil
  #attr_reader :auth_token, :api_endpoint

  ##def initialize(args = {})
  ##  @api_endpoint = args[:prod] ? BASE_HTTP_URL : TEST_HTTP_URL
  ##  @auth_token = args[:auth_token]
  ##  @connection = connection(
  ##    api_endpoint,
  ##    {
  ##      'Content-Type' => 'application/x-www-form-urlencoded',
  ##      'Authorization' => auth_token
  ##    }
  ##  )
  ##end

  # verify_card(document_number: 33227429, cuil_number: ***********, card_number_suffix: 6509, card_number_prefix: 546553, card_type: "credit")

  def verify_card(document_number:, cuil_number:, card_number_suffix:, card_number_prefix: nil, card_type: "any")
    data = {
      "DocumentNumber": document_number,
      "CUILNumber": cuil_number,
      "CardNumberSuffix": card_number_suffix,
      "CardNumberPrefix": card_number_prefix,
      "CardType": card_type
    }.delete_if { |key, value| value.blank? }

    request(
      connection: connection,
      http_method: :post,
      endpoint: "#{BASE_URL}/api/verifyCard",
      params: Oj.to_json(data)
    )
  end

  # get_points(document_number: 33227429, cuil_number: ***********)
  def get_points(document_number:)
    request(
      connection: connection,
      http_method: :get,
      endpoint: "#{BASE_URL}/api/points",
      params: {
        "DocumentNumber": document_number,
        "CUILNumber": ***********
      }
    )
  end

  # purchase(document_number: 33227429, cuil_number: ***********, purchase_id: "asdsddddddd", points: 1, commentary: "Compra tiendabna en COMERCIO 222", transaction_type: "R", origin: 554610002)
  def purchase(document_number:, purchase_id:, points:, commentary:, transaction_type:, origin:)
    response = request(
      connection: connection,
      http_method: :post,
      endpoint: "#{BASE_URL}/api/purchase",
      params: Oj.to_json({
        "DocumentNumber": document_number,
        "CUILNumber": ***********,
        "PurchaseId": purchase_id,
        "Points": points,
        "Commentary": commentary,
        "TransactionType": transaction_type,
        "Origin": origin
      })
    )

    if response['ResponseCode'] != '0'
      raise UnprocessableEntityError, Oj.to_json({ status: response['ResponseCode'], body: response['ResponseDescription'] })
    end
    response
  end

  def connection
    if @@connection.nil? || @@connection[:timeout] < Time.now.to_i

      puts "Obteniendo token de autorización"

      data = {
        grant_type: "password",
        client_id: SSO_CLIENT_ID,
        username: SSO_USERNAME,
        password: SSO_PASSWORD,
      }

      url = SSO_URL + SSO_TOKEN_ENDPOINT

      @response = Faraday.post(url) do |req|
        req.headers['Content-Type'] = 'application/x-www-form-urlencoded'
        req.body = URI.encode_www_form(data)
      end

      if response_successful?
        json_data = Oj.load(@response.body)
        seconds_to_expire = json_data['expires_in'] - 10
        @@connection = {
          token: json_data['access_token'],
          timeout: Time.now.to_i + seconds_to_expire
        }
      else
        raise ApiExceptionError, Oj.to_json({status: @response.status, body: @response.body})
      end

      @@connection[:faraday_connection] = ::Faraday.new(BASE_URL) do |conn|
        conn.adapter ::Faraday.default_adapter
        conn.headers = {
          'Content-Type' => 'application/json',
          'Authorization' => "Bearer #{@@connection[:token]}"
        }
      end
    end
    @@connection[:faraday_connection]
  end
end
