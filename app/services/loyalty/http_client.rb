require 'uri'
module Loyalty::HttpClient
  include Loyalty::ApiExceptions
  include Loyalty::HttpStatusCodes

  API_REQUESTS_QUOTA_REACHED_MESSAGE = 'API rate limit exceeded'.freeze

  def connection(api_endpoint, headers)
    @connection ||= ::Faraday.new(api_endpoint) do |conn|
      conn.adapter ::Faraday.default_adapter
      conn.headers = headers
    end
  end

  def request(connection:, http_method:, endpoint:, params_type: :query, params: {})
    @response = abstract_request(connection, http_method, endpoint, params, params_type)

    parsed_response = Oj.load(@response.body)

    return parsed_response if response_successful?

    raise ApiExceptionError, Oj.to_json({status: @response.status, body: @response.body})
  end

  def error_class
    case @response.status
    when HTTP_BAD_REQUEST_CODE then BadRequestError
    when HTTP_UNAUTHORIZED_CODE then UnauthorizedError
    when HTTP_FORBIDDEN_CODE then forbiden_error(response)
    when HTTP_NOT_FOUND_CODE then NotFoundError
    when HTTP_UNPROCESSABLE_ENTITY_CODE then UnprocessableEntityError
    else
      ApiError
    end
  end

  private

  def response_successful?
    @response.status == HTTP_OK_CODE
  end

  def api_requests_quota_reached?
    @response.body.match?(API_REQUESTS_QUOTA_REACHED_MESSAGE)
  end

  def forbiden_error
    return ApiRequestsQuotaReachedError if api_requests_quota_reached?(@response)

    ForbiddenError
  end

  def abstract_request(connection, http_method, endpoint, params, params_type)
    case params_type
    when :query
      connection.public_send(http_method, endpoint, params)
    when :body
      connection.get endpoint do |req|
        req.headers[:content_type] = 'application/json'
        req.body = params
      end
    else
      raise "Unknown params type: #{params_type}"
    end
  end
end
