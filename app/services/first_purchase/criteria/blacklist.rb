module FirstPurchase
  module Criteria
    class Blacklist

      def initialize(args = {})
        @store = args[:store]
        @doc_number = args[:document_number]
        @doc_type = args[:document_type]
      end

      def authorized?
        filter = {}
        filter.merge!(doc_type: @doc_type) unless @doc_type.nil?
        !Pioneer::Blacklist.exists?({store: @store, doc_number: @doc_number}.merge(filter))
      end
    end
  end
end