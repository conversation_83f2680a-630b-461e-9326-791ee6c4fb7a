module FirstPurchase
  module Criteria
    class Orders

      def initialize(args = {})
        @store = args[:store]
        @doc_number = args[:document_number]
        @doc_type = args[:document_type]
      end

      def authorized?
        filter = {}
        filter.merge!(document_type: @doc_type) unless @doc_type.nil?
        orders = Mkp::Order.joins(:payments).where(store: @store, mkp_payments: { document_number: @doc_number}.merge(filter))
        orders.empty?
      end
    end
  end
end