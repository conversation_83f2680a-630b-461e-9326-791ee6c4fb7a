module FirstPurchase
  class Validator < ApplicationService
    RETURNS = [
      SUCCESS = :success,
      FAILURE_CONFIGURATION = :failure_configuration,
      FAILURE_CONFIGURATION_ACTIVE = :failure_configuration_active,
      FAILURE = :failure,
      WHITELIST_FAILURE = :whitelist_failure,
      FIRST_PURCHASE_FAILURE = :first_purchase_failure
    ]

    def initialize(args = {})
      @store = args[:store]
      @document_number = args[:document_number]
      @configuration = @store&.whitelist_configuration
      @first_purchase_authorization = args[:first_purchase_authorization]
    end

    def call
      return FAILURE_CONFIGURATION unless @configuration.present?
      return FAILURE_CONFIGURATION_ACTIVE unless @configuration.active
      return FAILURE unless @document_number.present?
      return WHITELIST_FAILURE unless whitelist_valid?
      return FIRST_PURCHASE_FAILURE unless first_purchase_authorized?
      
      SUCCESS
    end

    private

    def whitelist_valid?
      @configuration&.user_strategy_class&.exists?(@document_number, @store)
    end

    def first_purchase_authorized?
      @first_purchase_authorization.authorized?
    end

  end
end