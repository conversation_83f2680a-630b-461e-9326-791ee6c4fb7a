module PaymentManager
  class CoefCalculator < ApplicationService

      def initialize(args = {})
        @payment = args[:payment]
        @shop_store = args[:shop_store]
        @gateway_data_coef = @payment.gateway_data.dig(:installment_data, :coef)
      end

      def call
        return @gateway_data_coef.to_f unless @gateway_data_coef.nil?
        Installment.where(number: @payment.installments, payment_program_id: @shop_store.payment_program_id).first.coef
      end
    end
end