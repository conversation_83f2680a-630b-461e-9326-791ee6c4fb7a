module Avenida
  module Boston
    class SendWarrantyMail
      include Interactor

      def call
        send_email_to_customer
      end

      private

      def send_email_to_customer
        pdf_data = context.response['Payload']['ListaDetalle'][0]['CertificadoBase64']
        warranty_pdf = Base64.decode64(pdf_data)

        ::Mkp::Bna::BostonMailer.send_policy_to_customer(warranty_pdf, context.order).deliver_now
      end
    end
  end
end
