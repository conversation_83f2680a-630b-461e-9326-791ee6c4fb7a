module Avenida
  module Boston
    class WarrantyRequest
      include Interactor

      before do
        address_ids = Province.get_ids(context.shipment.address.state, context.shipment.address.city)
        context.province_id = address_ids[:province_id]
        context.city_id = address_ids[:city_id]

        context.body_request = body_request
      end

      def call
        boston_response
        warranty_response
      end

      private

      def response
        context.response
      end

      def body_request
        @body_request ||= {
          "Factura": context.order.id.to_s,
          "Apellido": context.order.customer.last_name,
          "Nombres": context.order.customer.first_name,
          "DocumentoTipo": doc_type,
          "DocumentoNumero": context.order.customer.doc_number,
          "DomicilioCalle": context.shipment.address.address,
          "DomicilioNumero": context.shipment.address.street_number,
          "DomicilioPiso": context.shipment.address&.depth || "",
          "DomicilioDepartamento": "",
          "CodigoPostal": context.shipment.address.zip,
          "Localidad": context.city_id,
          "CodigoProvincia": context.province_id,
          "Telefono": context.shipment.address.telephone,
          "Email": context.order.customer.email,
          "SituacionIVA": 'CF',
          "FechaNacimiento": context.order.customer&.birthday_at&.strftime('%Y-%m-%d') || "",
          "ListaCobertura": [
            {
              "SolicitaGarantiaExtendida": true,
              "Articulo": context.associated_product.title,
              "Marca": context.associated_product.manufacturer.name,
              "IdCategoriaArticulo": context.warranty_product.variant.properties[:articulo],
              "MesesGarantiaFabrica": 0,
              "MesesGarantiaExtendida": context.warranty_product.variant[:properties][:period],
              "SumaAsegurada": context.associated_product.price.to_s,
              "ValorFinalVenta": total_amount,
              "ValorVenta": context.warranty_product.price.to_s,
              "PaymentId": context.warranty_suborder.payment.get_external_id
            }
          ]
        }
      end

      def doc_type
        customer = context.order.customer
        return 'DNI' if customer.instance_of?(Mkp::Guest)

        customer.doc_type
      end

      def boston_response
        context.response = AvenidaWrappers::Boston::Client.new(auth_token: context.token['token'], boston_api_endpoint: BOSTON_API_ENDPOINT).sales_extended_warranties(context.body_request)
      end

      def warranty_response
        WarrantyResponse.create(
          suborder_id: context.warranty_suborder.id,
          transaction_id: response['Payload']['IdVenta'].to_s,
          operation_id: response['Payload']['ListaDetalle'][0]['IdDetalle'].to_s,
          product_title: response['Payload']['ListaDetalle'][0]['Articulo'].to_s
          )
      end

      def total_amount
        amount_with_two_cards = []
        if context.warranty_suborder.all_payments.count == 1
          context.warranty_suborder.payment.sub_payment_amount(context.warranty_suborder).to_s
        else
          context.warranty_suborder.all_payments.each{ |pay| amount_with_two_cards << pay.sub_payment_amount(context.warranty_suborder) }
          amount_with_two_cards.sum.round(2).to_s
        end
      end
    end
  end
end
