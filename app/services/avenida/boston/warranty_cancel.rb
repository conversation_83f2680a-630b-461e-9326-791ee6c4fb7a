module Avenida
  module Boston
    class WarrantyCancel
      include Interactor

      def call
        AvenidaWrappers::Boston::Client.new(auth_token: context.token['token'],
                                            boston_api_endpoint: BOSTON_API_ENDPOINT)
                                       .cancel_sales(cancel_body_request)
      end

      private

      def cancel_body_request
        {
          IdVenta: context.policy_to_cancel.transaction_id,
          NotaCredito: context.policy_to_cancel.suborder_id.to_s,
          ListaDetalle: [{
            IdDetalle: context.policy_to_cancel.operation_id
          }]
        }
      end
    end
  end
end
