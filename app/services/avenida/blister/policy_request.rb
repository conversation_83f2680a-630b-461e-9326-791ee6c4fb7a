module Avenida
  module Blister
    class PolicyRequest
      include Interactor

      attr_accessor :response

      before do
        context.token = token
        shipment_state = context.shipment.destination_state.titleize
        context.cod_province = get_province_code(shipment_state, context.token)
        context.body_request = body_request
      end

      after do
        warranty_response
        send_email_to_customer
        rocket_notify
      end

      def call
        blister_response
      end

      private

      def token
        AvenidaWrappers::Blister::Auth.new(username: BLISTER_USERNAME, password: BLISTER_PASSWORD, blister_api_endpoint: BLISTER_API_ENDPOINT).auth['token']
      end

      def body_request
        @body_request ||= {
          "coberturas": [
            {
              "cobertura_id": context.warranty_product.variant.properties[:external],
              "articulo": context.associated_product.title,
              "marca": context.associated_product.manufacturer.name,
              "categ_articulo_id": context.warranty_product.variant.properties[:articulo],
              "garantia_fabrica": '0',
              "suma_asegurada": context.associated_product.price.to_s,
              "vfv": total_amount
            }
          ],
          "factura": context.order.id.to_s,
          "apellido": context.order.customer.last_name,
          "nombres": context.order.customer.first_name,
          "doc_tipo": doc_type,
          "doc_numero": context.order.customer.doc_number,
          "domicilio": context.shipment.address.address,
          "cp": context.shipment.address.zip,
          "localidad": context.shipment.address.state,
          "codigo_provincia": context.cod_province,
          "telefono": context.shipment.address.telephone,
          "email": context.order.customer.email,
          "SitIVA": 'CF'
        }
      end

      def get_province_code(shipment_state, token)
        provinces = AvenidaWrappers::Blister::Client.new(auth_token: token, blister_api_endpoint: BLISTER_API_ENDPOINT).province
        provinces['data'].find{ |p| p['provincia'] == shipment_state }['codigo_provincia']
      end

      def doc_type
        customer = context.order.customer
        return 'DNI' if customer.instance_of?(Mkp::Guest)

        customer.doc_type
      end

      def blister_response
        @response = AvenidaWrappers::Blister::Client.new(auth_token: context.token, blister_api_endpoint: BLISTER_API_ENDPOINT).sales_extended_warranties(context.body_request)
      end

      def send_email_to_customer
        pdf = @response['data']['pdf']
        ::Mkp::Bna::BlisterMailer.send_policy_to_customer(pdf, context.order).deliver_now
      end

      def rocket_notify
        RocketChatNotifier.notify(context.body_request, webhook_dest: :blister)
      end

      def warranty_response
        WarrantyResponse.create(
          suborder_id: context.warranty_suborder.id,
          transaction_id: response['data']['transaccion'].to_s,
          operation_id: response['data']['operaciones'].first['operacion'].to_s,
          product_title: response['data']['operaciones'].first['articulo'].to_s
          )
      end

      def cancel_body_request(policy_to_cancel)
        {
          transaccion_id: policy_to_cancel.transaction_id,
          operaciones: [
            operacion_id: policy_to_cancel.operation_id
          ]
        }
      end

      def total_amount
        amount_with_two_cards = []
        if context.warranty_suborder.all_payments.count == 1
          context.warranty_suborder.payment.sub_payment_amount(context.warranty_suborder).to_s
        else
          context.warranty_suborder.all_payments.each{ |pay| amount_with_two_cards << pay.sub_payment_amount(context.warranty_suborder) }
          amount_with_two_cards.sum.round(2).to_s
        end
      end
    end
  end
end
