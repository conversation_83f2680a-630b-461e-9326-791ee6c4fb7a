module Insurance
  class InsuranceProcessor
    INSURANCE_FIELDS = %w[
      name email installments gender birthday birth_location nationality
      doc_type doc_number phone address street_number dept country state
      city postal_code civil_status profession political_exposure floor
      cbu reference_id extra_data amount
    ].freeze

    def initialize(checkout_cart = nil, order)
      @checkout_cart = checkout_cart
      @order = order
    end

    # Viene del checkout_pending_payment.rb - Paso 1
    def process_checkout_insurance(insurance_params)
      return unless insurance_enabled? && !insurance_params.empty?

      array_insurance_data = prepare_insurance_data(insurance_params)
      create_insurance_tokens(array_insurance_data)
    end

    # Viene del webhook_controller.rb - Paso 1
    def process_collected_payment_insurance
      insurance_tokens = InsuranceToken.where(order_id: @order.id)
      return unless insurance_tokens.present?

      insurance_tokens.each do |insurance_token|
        begin
          insurance_token.update(reference_id: @order.purchase_id) if @order.purchase_id.present?
          send_to_nacion_tokens(insurance_token)
        rescue StandardError => e
          Rails.logger.error("Error procesando InsuranceToken: #{e.message}")
        end
      end
    end

    # Usado en webhook_controller.rb cuando el pago es rechazado/cancelado
    def delete_insurance_tokens
      InsuranceToken.where(order_id: @order.id).destroy_all
    end

    private

    # Viene del checkout_pending_payment.rb - Paso 2
    # Acá tengo card_number, detailed_model, insurance_amount -> dentro de data
    def prepare_insurance_data(insurance_params)
      array_insurance_data = {}
      checkout_cart_strategy_insurance.candidates.each do |item|
        item.quantity.times do
          insurance_data = new_insurance_data(insurance_params, item)
          raise InsuranceError, "Revise los datos del seguro" unless insurance_data.valid?
          array_insurance_data[insurance_data] = item
        end
      end

      array_insurance_data
    end

    # Viene del checkout_pending_payment.rb - Paso 3
    # Acá tengo card_number, detailed_model, insurance_amount -> dentro de extra_data
    def create_insurance_tokens(array_insurance_data)
      array_insurance_data.each do |insurance_data, item|
        token_params = INSURANCE_FIELDS.each_with_object({}) do |field, hash|
          hash[field] = insurance_data.data[field]
        end

        InsuranceToken.create!(token_params.merge(
          token: "pending_#{SecureRandom.hex(8)}",
          order_id: @order.id,
          amount: insurance_data.data["amount"]
        ))
      end
    end

    # Viene del webhook_controller.rb - Paso 2
    def send_to_nacion_tokens(insurance_token)
      extra_data = JSON.parse(insurance_token.extra_data, symbolize_names: true)

      nacion_params = INSURANCE_FIELDS.each_with_object({}) do |field, hash|
        hash[field] = insurance_token.send(field)
      end

      begin
        formatted_card = nil

        if extra_data[:card_number].present?
          decrypted_card = Encrypt::EncryptService.decrypt(extra_data[:card_number], ENCRYPT_KEY)
          formatted_card = decrypted_card.to_s.gsub(/(.{4})(?=.)/, '\1 ')
        end

        nacion_params.merge!(
          "date_of_sale" => @order.created_at,
          "reference_id" => '',
          "card_number" => formatted_card.presence || '0000000000000000',
          "detailed_model" => extra_data[:detailed_model],
          "amount" => extra_data[:insurance_amount],
          "insurance_amount" => extra_data[:insurance_amount]
        )

        nacion_tokens_service = NacionTokens.new(NACION_TOKENS_URL, NACION_TOKENS_API_KEY, nacion_params)
        nacion_tokens_service.create_insurance

        # Eliminar el card_number del extra_data y actualizar
        extra_data.delete(:card_number)
        insurance_token.update(extra_data: extra_data.to_json)

        if nacion_tokens_service.valid?
          insurance_token.update(token: nacion_tokens_service.response['token'])
          Rails.logger.info("Token de seguro creado exitosamente para order_id: #{@order.id}, token: #{nacion_tokens_service.response['token']}")
        else
          Rails.logger.error("Error al guardar datos del seguro en el servicio de tokens de Nación: #{nacion_tokens_service.errors.presence || nacion_tokens_service.response}")
        end
      rescue Encrypt::EncryptError => e
        Rails.logger.error("Error al desencriptar card_number: #{e.message}")
      rescue StandardError => e
        Rails.logger.error("Error inesperado en send_to_nacion_tokens: #{e.message}")
      end
    end

    # Viene del checkout_pending_payment.rb
    # Añado card_number, detailed_model, insurance_amount en extra_data como un json
    def new_insurance_data(insurance_params, item)
      strategy_insurances = checkout_cart_strategy_insurance
      collected_data = insurance_params.dup
      insurance_amount = strategy_insurances.insurance_amount([item])

      extra_data = {
        card_number: insurance_params["card_number"],
        detailed_model: strategy_insurances.detailed_model([item]),
        insurance_amount: insurance_amount
      }

      collected_data = collected_data.merge({
        installments: strategy_insurances.installments,
        amount: insurance_amount,
        extra_data: extra_data.to_json
      })

      InsuranceData.new(collected_data)
    end

    def insurance_enabled?
      checkout_cart_strategy_insurance.enabled?
    end

    def checkout_cart_strategy_insurance
      @checkout_cart_strategy_insurance ||= @checkout_cart.store.strategy_insurances(@checkout_cart)
    end
  end
end
