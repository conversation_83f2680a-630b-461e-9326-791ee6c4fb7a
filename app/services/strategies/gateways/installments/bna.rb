module Strategies
  module Gateways
    module Installments
      class Bna < Strategies::Gateways::Installments::Base
        GATEWAYS = { 'firstdata_distributed' => 'First Data Distribuido' }.freeze

        def own_gateways_for(criteria)
          if criteria.authorized?
            [criteria.name]
          else
            []
          end
        end

        def installments_from_bin(bin)
          bin.installments.select { |installment| own_gateways.include? installment.gateway }
        end
      end
    end
  end
end
