module Strategies
  module Gateways
    module Installments
      class Base
        attr_accessor :store

        # store this data in db
        GATEWAYS = {}.freeze

        def initialize(store)
          @store = store
        end

        def own_gateways?
          installment_gateways.present?
        end

        def own_gateways_for(criteria)
          []
        end

        def own_gateways
          installment_gateways.keys
        end

        def installments_from_bin(_bin)
          []
        end

        def own_gateways_info
          installment_gateways.invert.to_a
        end

        private

        def installment_gateways
          self.class::GATEWAYS
        end
      end
    end
  end
end
