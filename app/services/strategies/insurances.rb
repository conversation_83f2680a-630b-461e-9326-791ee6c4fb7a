module Strategies
  module Insurances
    def self.strategy(store, cart = nil)
      get_strategy_from(store, cart)
    rescue NameError, LoadError
      self::Base.new(store, cart)
    end

    def self.get_strategy_from(store, cart)
      case store.insurance_source
      when 'ochenta_grados'
        Strategies::Insurances::ExternalInsurances::OchentaGrados.new(store, cart)
      when 'insurance_configuration_table'
        "#{self}::#{store.name.camelcase}".constantize.new(store, cart)
      when 'pioneer'
        # WIP
        self::Base.new(store, cart)
      else
        self::Base.new(store, cart)
      end
    end
  end
end
