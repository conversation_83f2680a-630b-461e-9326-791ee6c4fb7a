module Strategies
  module Insurances
    module Insuranceable
      extend ActiveSupport::Concern

      def enabled_for_items?
        self.enabled? && items.any? { |item| candidate?(item) }
      end

      def cost_by_currency(filtered_items = nil)
        items_to_calculate = filtered_items || items
        currencies_amount = []
        candidates(items_to_calculate).group_by { |item| item.currency.identifier }.map do |currency , items |
            #currencies_amount << [currency.to_sym, cost_by_variant(items)]
            currencies_amount << [currency.to_sym, calculate_cost(items)]
        end
        currencies_amount.to_h.merge(:installments => installments)
      end

      def detailed_model(items)
        data = candidates(items).map do |item|
          "#{item.variant.product.title}"
        end
        data.join(' -- ')
      end

      def extra_info(items)
        data = candidates(items).map do |item|
          "#{item.variant.product.title} - #{item.total / item.quantity} - #{item.variant.product.manufacturer&.name}"
        end
        data.join('. ')
      end

      def insurance_amount(items)
        amount = candidates(items).map(&:total).sum
        amount.to_f
      end

      def candidates(filtered_items = nil)
        items_to_calculate = filtered_items || items
        items_to_calculate.select { |item| candidate?(item) }
      end

      private

      def items
        @items ||= @cart.checkout_items || []
      end
    end
  end
end
