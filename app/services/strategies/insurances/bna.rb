module Strategies
  module Insurances
    class Bna < Base
      def cost_by_variant(items)
        # NOT CURRENTLY USED
        by_variant = []
        items.group_by{ |item| item.variant.id }.map do | variant_id, items|
          by_variant << [variant_id, calculate_cost(items).to_f]
        end
        by_variant.to_h.merge('installments' => installments)
      end

      def installments
        configuration.all_installments.first
      end

      def enabled?
        configuration&.active?
      end

      protected

      def percentage
        super || 0.67
      end

      def calculate_cost(items)
        candidates(items).inject(0) { |sum, item| sum + item.total } * percentage / 100.to_f
      end

      def candidate?(item)
        # return true
        categories.include? item.variant.product.category_id.to_s
      end
    end
  end
end
