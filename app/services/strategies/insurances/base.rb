module Strategies
  module Insurances
    class Base
      include Strategies::Insurances::Insuranceable

      def initialize(store, cart)
        @store = store
        @cart = cart
      end

      def enabled?
        false
      end

      def installments
        configuration&.installments
      end

      def configuration
        @configuration ||= InsuranceConfiguration.find_by(store: @store)
      end

      def categories
        configuration&.all_categories
      end

      protected

      def percentage
        configuration&.percentage
      end

      def calculate_cost(_items)
        raise 'Not implemented'
      end

      def candidate?(_item)
        false
      end
    end
  end
end
