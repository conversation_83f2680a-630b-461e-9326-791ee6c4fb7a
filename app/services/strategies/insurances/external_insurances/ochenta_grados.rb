module Strategies
  module Insurances
    module ExternalInsurances
      class OchentaGrados
        include Strategies::Insurances::Insuranceable

        def initialize(store, cart)
          @store = store
          @cart = cart
          if @cart
            @insurances = ::Ochenta::Coverages::CategoryInsurancesParser.new(response.payload)
                                                                      .category_insurances.select(&:any?)
            response_detector = Ochenta::Cart::Warranties.new(
              mapper_instance: cart_product_mapper,
              available_categories: categories,
              ).detector
            @enabled = response_detector&.success?
          else
            @enabled = false
          end
        end

        def enabled?
          true
        end

        def installments
          12
        end

        def enabled_for_items?
          @enabled && super
        end

        private

        def percentage(item)
          @insurances.find do |category_insurance|
             category_insurance.category_id == item.variant.product.category_id
          end.insurances.first.coefficient
        end

        def calculate_cost(items)
          candidates(items).inject(0) { |sum, item| sum + (item.total * (percentage(item) / 100.to_f)) }
        end

        def candidate?(item)
          categories.include? item.variant.product.category_id
        end

        def response
          @response ||= Ochenta::Coverages::InsurancesService.call(
            body_request:  Ochenta::Request.new(habilitadas: true,
                                                category_ids: cart_product_mapper.category_ids).build
          )
        end

        def cart_product_mapper
          @cart_product_mapper ||= ::Ochenta::Category::Mapper::CartProduct.new(cart: @cart)
        end

        def categories
          @categories ||= @insurances.map(&:category_id)
        end
      end
    end
  end
end
