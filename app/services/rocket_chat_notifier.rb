require 'socket'

module RocketChatNotifier
  class << self
    attr_accessor :webhook_url_platform, :webhook_url_payments, :webhook_url_blister, :webhook_url_matrix

    def configure
      yield self

      raise "\nrocket chat notifier - configuration error: missing `webhook_url_platform` in initializer" unless webhook_url_platform
      raise "\nrocket chat notifier - configuration error: missing `webhook_url_payments` in initializer" unless webhook_url_payments
      raise "\nrocket chat notifier - configuration error: missing `webhook_url_blister` in initializer"  unless webhook_url_blister
      raise "\nrocket chat notifier - configuration error: missing `webhook_url_matrix` in initializer" unless webhook_url_matrix
    end

    def notify(message, event: '', emoji: '', attachment: [{}], webhook_dest: nil)
      raise "\nrocket chat notifier - error: empty rocket chat message, message text is mandatory" unless message

      request_body = {
        text: "#{Socket.gethostname} - #{message}"
      }

      request_body[:event] = event if event.present?
      request_body[:emoji] = emoji if emoji.present?
      request_body[:attachment] = attachment if attachment.present?

      begin
        webhook = case webhook_dest
                  when :platform
                    webhook_url_platform
                  when :payments
                    webhook_url_payments
                  when :blister
                    webhook_url_blister
                  when :matrix
                    webhook_url_matrix
                  else
                    webhook_url_platform
                  end

        response = JSON.parse Faraday.post(webhook, request_body.to_json, { 'Content-Type' => 'application/json' }).body
        warn("\nrocket chat notifier - warning: rocket chat could not be notified. rocket chat response: `#{response['error']}`") unless response['success'] || response['success'] == true
      rescue => e
        warn(e.message)
        warn("\nrocket chat notifier - warning: could not connect to rocket chat webhook url or parse JSON result, check your webhook_url")
      end

      response
    end
  end
end
