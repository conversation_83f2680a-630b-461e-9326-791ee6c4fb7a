section.description
  a.tab_title.toggle-info data-target="product-description" href="#"= t('.tab_title')
  .content#product-description
    = simple_format(@product.description_html)
  - ["data", "data_shipment"].each do |field|
    - unless @product.data_to_simple_hash(field).blank?
      a.tab_specs.toggle-info data-target="product-specs-#{field}" href="#"= t(".tab_specifications#{"_shipment" if field == "data_shipment"}")
      .content id="product-specs-#{field}"
        table class="product-specs-#{field}"
          tr
            th = t('.attributes')
            th.padding-10-left = t('.attributes_values')
          - @product.data_to_simple_hash(field).each do |name, value|
            tr
              td.border-grey-bottom = name
              td.border-grey-bottom.padding-10-left = parse_hash_value(value)
          - if ([@current_store.id] & [41,43,47]).any?
            tr
              td.border-grey-bottom = "Origen del Producto"
              td.border-grey-bottom.padding-10-left = @product.origin_of_product.present? ? @product.translated_values_origin_of_product : " "
            tr
              td.border-grey-bottom = "Marca"
              td.border-grey-bottom.padding-10-left = @product.brand.present? ? @product.brand : " "
            tr
              td.border-grey-bottom = "Eficiencia Energética"
              td.border-grey-bottom.padding-10-left = @product.energy_efficiency.present? ? @product.energy_efficiency : " "

  = render partial: 'mkp/variants/v5/partials/extras/reviews'
