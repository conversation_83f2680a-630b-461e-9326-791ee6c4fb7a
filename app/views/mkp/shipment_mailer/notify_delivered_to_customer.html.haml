- I18n.locale = @locale
%div{ :style => "color:#525252; font-family: verdana, geneva, sans-serif; font-size:11px;"}
%div.body
  %h2
    =t('.hi',name: @customer.first_name, last:@customer.last_name)
  %p
    = t('.success')
  %table.order-item{ :style => "margin: auto; width: 100%;" }
    %tbody
      - @shipment.items.each do |item|
        = render partial: 'mailer/partials/order_item', locals: {item: item, hide_purchase_date: true}
  %p
    - if @order.points <= 0
      = t('.total_order') + ": "
      %strong
        = number_to_currency((@order.total_without_points + @order.total_with_coef.to_f), unit: current_currency_format)

    - if @order.points > 0
      = t('.total_order') + ": "
      %strong
        = @order.total_points
        = " puntos "
        = number_to_currency((@order.gross_total + @order.total_with_coef.to_f), unit: current_currency_format)
  %br
  %hr
  - shipment = @order.shipments.first
  - if shipment.present?
    %table
      %tbody
        %tr
          %td{ colspan: 2}
            - address = shipment.destination_address
            %p
              = t('.reciver')
              %strong #{address.full_name.try(:titleize)}
            %p
              = t('.address')
              - address_to_print = []
              - address_to_print << address.address
              - address_to_print << address.street_number if address.street_number.present?
              - address_to_print << address.address_2
              - address_to_print << address.city
              - address_to_print << address.state
              - address_to_print << address.zip.to_s
              - address_to_print << GeoConstants::Countries.name_for_code(address.country) if address.country.present?
              %strong
                = address_to_print.compact.reject(&:empty?).join(', ').titleize

            - if @current_store.name == 'bna'
              %br
              %h3{:style => "margin-bottom:5px;"}
                = t('.satisfaction_survey')
              %br
              %a{:href => "https://es.surveymonkey.com/r/eval_market", :style => "cursor:pointer; color: #ffffff; text-decoration: none; background-color: #007894; border-top: 15px solid #007894; border-bottom: 15px solid #007894; border-left: 25px solid #007894; border-right: 25px solid #007894; border-radius: 3px; -webkit-border-radius: 3px; -moz-border-radius: 3px; display: inline-block;" }
                Ir a la encuesta &rarr;
%br
= render partial: 'v5/partials/mailer/contact_us'
