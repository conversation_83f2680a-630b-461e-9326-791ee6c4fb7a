- if Couriers::ALL.key?(@shipment.courier_name.downcase.to_sym) && !Couriers::ALL[@shipment.courier_name.downcase.to_sym][:url].blank?
  - courier_tacking_link = Couriers::ALL[@shipment.courier_name.downcase.to_sym][:url]
  - if Couriers::ALL[@shipment.courier_name.downcase.to_sym][:direct_url]
    - courier_tacking_link = "#{courier_tacking_link}#{@shipment.label.try(:tracking_number)}"
- else
  - courier_tacking_link = nil

%h2=t('.hi',name:@customer.first_name)
%p= @shipment_title
%hr
%table.order-item{ :style => "width: 100%;" }
  %tbody
    %tr.border-bottom
      %td
        %p
          %strong= t('.orders_being_tracked')
          %span= @orders_being_tracked
        %p
          %strong= t('.courier')
          - courier = @shipment.label.try(:courier).eql?('other') ? 'Logística Privada' : @shipment.label.try(:courier)
          %strong{style:"text-decoration:none;text-transform: uppercase;"}=courier
        - unless courier.eql? 'Logística Privada'
          %p
            %strong= t('.tracking_number')
            %span=@shipment.label.try(:tracking_number)
          %br
        - unless courier_tacking_link.nil?
          %p
            Para conocer el estado de tu pedido, haz clic
            %a{href:"#{courier_tacking_link}" ,target:"_blank"}
              aquí
            o visita la página del Operador Logístico
        - if @shipment.order.suborders.size > 1
          .highlight
            .container-highlight
              %p= t('.legend')
%br
- if @current_store.name == 'bna'
  %h4{:style => "margin-bottom: 0px;"}
    = "Datos del vendedor"

  %small
    = "Ante cualquier consulta que tengas sobre la facturación, el producto adquirido o el envió, puedes ponerte en contacto con el Vendedor"
  %p{:style => "line-height: 17px;"}
    = "Vendedor #{@shipment.suborder.shop.public_name}"
    - if @shipment.suborder.shop.phone != ""
      %br
      = "Teléfono #{@shipment.suborder.shop.phone}"
    - if @shipment.suborder.shop.public_email != ""
      %br
      = "Email "
      %a{:href => "mailto:#{@shipment.suborder.shop.public_email}"}
        = "#{@shipment.suborder.shop.public_email}"
    - if @shipment.suborder.shop.web != ""
      %br
      = "Web #{@shipment.suborder.shop.web}"
%br
= render partial: 'v5/partials/mailer/contact_us'
