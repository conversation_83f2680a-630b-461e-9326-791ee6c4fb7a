%h2=t('.hi',name:@customer.first_name)
%p= @shipment_title_pick_up
%hr
%table.order-item{ :style => "width: 100%;" }
  %tbody
    %tr.border-bottom
      %td
        %p
          %strong= t('.orders_being_tracked')
          %span= @orders_being_tracked
        %p
          %strong= t('.courier')
          - courier = @shipment.pickable? ? 'Retiro por sucursal' : '-'
          %strong{style:"text-decoration:none;text-transform: uppercase;"}=courier
          %p{:style => "line-height: 19px;"}
          = t('mkp.shipment_mailer.shipping_ready_pickup')
          %p{:style => "line-height: 19px;"}
          %strong= "#{@shipment.origin_address[:address]}" + ", " + "#{@shipment.origin_address[:city]}" + ", " + "#{@shipment.origin_address[:state]}"
          %p{:style => "line-height: 19px;"}
          = "Horario: #{@shipment.origin_address[:open_hours]}" + " - " + "#{@shipment.origin_address[:closing_hours]}"
          %br
        - if @shipment.order.suborders.size > 1
          .highlight
            .container-highlight
              %p= t('.legend')
%br
- if @current_store.name == 'bna'
  %h4{:style => "margin-bottom: 0px;"}
    = "Datos del vendedor"

  %small
    = "Ante cualquier consulta que tengas sobre la facturación o el producto adquirido, puedes ponerte en contacto con el Vendedor"
  %p{:style => "line-height: 17px;"}
    = "Vendedor #{@shipment.suborder.shop.public_name}"
    - if @shipment.suborder.shop.phone != ""
      %br
      = "Teléfono #{@shipment.suborder.shop.phone}"
    - if @shipment.suborder.shop.public_email != ""
      %br
      = "Email "
      %a{:href => "mailto:#{@shipment.suborder.shop.public_email}"}
        = "#{@shipment.suborder.shop.public_email}"
    - if @shipment.suborder.shop.web != ""
      %br
      = "Web #{@shipment.suborder.shop.web}"
%br
= render partial: 'v5/partials/mailer/contact_us'
