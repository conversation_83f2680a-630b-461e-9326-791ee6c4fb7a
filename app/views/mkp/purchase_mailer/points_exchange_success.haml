- I18n.locale = @locale
%div{ :style => "color:#525252; font-family: verdana, geneva, sans-serif; font-size:11px;" }
	%div.body
		%h2 
			=t('.hi',name: @customer.first_name, last:@customer.last_name)
		%p
			=t('.success', purchase: @variant.title)
			- if @variant.properties[:percentage] && @variant.properties[:payment_method]
				%p
					=t('.discount')
		%table.order-item{ :style => "width: 100%;" }
			%tbody
				%tr.border-bottom
					%td
						%div{ :style => "margin-right: 150px" }
							%strong 
								= t('.details').upcase
							%p
								=t('.date')
								%strong
									= (@purchase.created_at-3.hours).getutc.strftime("%d/%m/%y - %H:%M")
							- if @variant.properties[:percentage]
								%p
									=t('.percentage')
									%strong
										="#{@variant.properties[:percentage]}%"
							- if @variant.properties[:payment_method]
								%p
									=t('.payment_method')
									%strong
										= "#{@variant.properties[:payment_method]}"
							- if @variant.properties[:percentage] && @variant.properties[:payment_method]
								%p 
									=t('.return')
									%strong
										=number_to_currency(@variant.discount_top, unit: current_currency_format)
							%p 
								=t('.points')
								%strong
									= number_with_delimiter(@variant.points_price, :delimiter => '.')
							%p
								=t('.transaction_number') 
								%strong
									=@purchase.id
					%td
						%p
						  - picture = @variant&.picture
							= image_tag picture.url(:st), size: '100x100' if picture.present?
		.highlight{ :style => "padding: 15px; background-color: #BFBFBF" }
			.container-highlight
				%p{ :style => "font-size: 1.5em; margin: 20px 0 0 0" }
					= t('.summary')
				%p{ :style => "margin-top: 20px" }
					%strong
						- if @variant.properties[:percentage] && @variant.properties[:payment_method]
							="#{@variant.properties[:percentage]}% de #{@purchase.title} por #{@variant.points_price} Puntos Macro Premia"
						- else
							="#{@purchase.title} por #{number_with_delimiter(@variant.points_price, :delimiter => '.')} Puntos Macro Premia"
						
		%p{ :style => "text-align: justify;" }
			
			- if @variant.category.ancestry == "1793"
				=t('.legal_experiencias')
			- elsif @variant.category.ancestry != "1793" && @variant.category.id == 1806
				=t('.legal_premios_instantaneos')
			- elsif @variant.category.ancestry != "1793" && @variant.category.id == 1810
				=t('.legal_donaciones')
			- elsif @variant.category.ancestry != "1793" && @variant.category.id == 1649
				=t('.legal_giftcards')
			- else
				=t('.legal')
		
		%br
		%p{:style => "text-align: justify;"}
			=t('.legal2')
		%br
		%p{:style => "text-align: justify;"}
			=t('.legal3')
		%br
		%p{:style => "text-align: justify;"}
			=t('.legal4')
		%br
		%p{:style => "text-align: justify;"}
			=t('.legal5')
%br
= render partial: 'v5/partials/mailer/contact_us'
