%h2= t('.hi', name: @customer.full_name)
- if @current_store.name == 'bna'
  %p
    %p
      = t('.msg9_html')
    %p
      = t('.msg10_html')
  %p 
    %p
      = t('.msg8_html', order_id: @order.id, suborder_id: @order.suborders.map(&:id).uniq.join, store_id: @order.store.id)
    %p
      = t('.msg11_html', order_date: @order.created_at.strftime("%d/%m/%Y"), order_time: @order.created_at.strftime("%H:%M"))
    %p
      = t('.msg12_html')
      - @order.suborders.each do |suborder|
        - suborder.items.each do |item|
          %li
            %strong
              = item.title
  %p
    = t('.msg13_html')
  %p
    = t('.msg14_html')
  %p
    = t('.msg15_html')
  %p
    = t('.msg16_html')
  %p
    = t('.msg17_html')
  %p
    = t('.msg18_html')
  %p
    = t('.msg19_html')
  %p
    = t('.msg20_html')
