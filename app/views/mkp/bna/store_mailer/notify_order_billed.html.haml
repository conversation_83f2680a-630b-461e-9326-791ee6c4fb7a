%body{ :style => "font-family:Verdana, Geneva, sans-serif;" }
    
    %table{ :align => "center", :bgcolor => "#FFFFFF", :cellspacing => "0", :cellpadding => "0", :border => "0", :style => "max-width: 600px; width:100%; table-layout: fixed; padding: 10px 0px;" }
        %tr 
            %td
                %h1{ :style => "font-family: Georgia, Times, 'Times New Roman', serif; margin-top:0px;" }
                    ="Hola, Sucursal #{@id_store}"
                %p{ :style => "color:#4e4e4e; font-size:12px; line-height: 20px;"  }
                    = "La reserva detallada  se encuentra en estado “Facturada” y la factura asociada está disponible en el marketplace para su descarga. La misma se encontrará en el Marketplace durante los próximos 10 días."

    %table{ :align => "center", :bgcolor => "#FFFFFF", :cellspacing => "0", :cellpadding => "0", :border => "0", :style => "max-width: 600px; width:100%; table-layout: fixed; padding: 0px 0px 30px 0px;" }
        
        %tr
            %td{ :width => "100", :height => "100", :align => "left", :valign => "top", :style => "border-collapse: collapse; border-spacing:0; padding-top:18px; padding-right:20px;"}
                - picture = @variant&.picture
                = image_tag picture.url(:st), width: '100' if picture.present?

            %td{ :align => "left", :valign => "top", :style => "border-collapse: collapse; border-spacing: 0; margin: 0; padding: 0; padding-top: 15px; color: #000000;"}
                
                %h3{ :style => "margin-bottom:0px;" }
                    ="Detalle de la reserva - Cod. #{@order.id}"
                
                %p{ :style => "color:#4e4e4e; font-size:10px; line-height: 15px; margin:3px 0px;"  }
                    = "Fecha y Hora: #{(@order.created_at-3.hours).getutc.strftime("%d/%m/%y - %H:%M")}"
                    %br
                    = "Modelo: #{@variant.title}"
                    %br
                    = "Código de reserva: #{@order.id}"
                    %br
                    = "Método de pago: Prestamo BNA"
                    %br
                    = "Método de envío: Retiro por concesionaria "
                    %br
                    = "Total del préstamo utilizado: #{number_to_currency(@order.total, unit: "$", separator: ",", delimiter: ".")}: "
    
    
    %table{ :align => "center", :bgcolor => "#FFFFFF", :cellspacing => "0", :cellpadding => "0", :border => "0", :style => "max-width: 600px; width:100%; table-layout: fixed;" }
        %tr 
            %td
                %h3{ :style => "margin-bottom:5px;" }
                    ="Datos del cliente"
                %p{ :style => "color:#4e4e4e; font-size:10px; line-height: 15px; margin:3px 0px;"  }
                    = "Nombre: #{@customer.first_name.capitalize()} #{@customer.last_name.capitalize()}"
                    %br
                    = "ID de solicitud: #{@order.id}"
                    %br
                    = "CUIL: #{@order.customer.doc_number}"
                    %br
                    = "Monto Preaprobado: #{number_to_currency(@bna_info.limit_amount, unit: "$", separator: ",", delimiter: ".")}" if @bna_info
                    %br
                    = "Sucursal: #{@id_store}"
                    %br
                    = "Email Solicitante: #{@customer.email}"
                    %br
                    = "Telefono: #{@customer.telephone}"
    

    %table{ :align => "center", :bgcolor => "#FFFFFF", :cellspacing => "0", :cellpadding => "0", :border => "0", :style => "max-width: 600px; width:100%; table-layout: fixed; padding: 10px 0px 30px 0px;" }
        %tr 
            %td
                %h3{ :style => "margin-bottom:5px;" }
                    ="Detalle de la concesionaria"
                %p{ :style => "color:#4e4e4e; font-size:10px; line-height: 15px; margin:3px 0px;"  }
                    = "Nombre: #{@shop.title}"
                    %br
                    = "Email: #{@shop.setting.notify_purchases}"
                    %br
                    = "Teléfono: #{@shop.warehouses.first.telephone}"
                    %br
                    = "Domicilio: #{@shop.warehouses.first.address} #{@shop.warehouses.first.address_2}"
                    %br
                    = "Provincia: #{@shop.warehouses.first.state}"
                    %br
                    = "Localidad: #{@shop.warehouses.first.city}"
                    %br
                    = "Código Postal: #{@shop.warehouses.first.zip}"



    
    %table#footer{ :align => 'center', :bgcolor => "#017894", :style => "padding:20px 30px; width: 100%; max-width: 600px;" }
        %tr
            %td
                %h3{ :color => "#ffffff", :align => 'left', :style => "color:#fff; margin:0px;"}
                    = "Si tiene consultas puede contactarse con:"
                %a{ :style => "color:#ffffff; font-size:12px; line-height: 20px; margin:0px;"  }
                    = "<EMAIL> "
