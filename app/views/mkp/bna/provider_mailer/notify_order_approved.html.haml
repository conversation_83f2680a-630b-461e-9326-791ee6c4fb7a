%body{ :style => "font-family:Verdana, Geneva, sans-serif;" }
    
    %table{ :align => "center", :bgcolor => "#FFFFFF", :cellspacing => "0", :cellpadding => "0", :border => "0", :style => "max-width: 600px; width:100%; table-layout: fixed; padding: 10px 0px;" }
        %tr 
            %td
                
                %h1{ :style => "font-family: Georgia, Times, 'Times New Roman', serif; margin-top:0px;" }
                    ="Hola, #{@shop.title}"
                
                %p{ :style => "color:#4e4e4e; font-size:12px; line-height: 20px;"  }
                    = "La reserva detallada ya se encuentra APROBADA por la sucursal. Por favor tomar contacto con el cliente para concluir el proceso de venta, dentro de las próximas 48 Horas. Recordá que la reserva tiene una vigencia de 15 (quince) días corridos a partir de su fecha de aprobación. Los datos del cliente se encuentran en el marketplace asociados a la reserva."

                %h3{ :style => "margin-bottom:0px;" }
                    ="Importante"

                %p{ :style => "color:#4e4e4e; font-size:12px; line-height: 20px;"  }
                    = "El importe de la factura realizada debe coincidir con el monto de la reserva."

    %table{ :align => "center", :bgcolor => "#FFFFFF", :cellspacing => "0", :cellpadding => "0", :border => "0", :style => "max-width: 600px; width:100%; table-layout: fixed; padding: 0px 0px 30px 0px;" }
        
        %tr
            %td{ :width => "100", :height => "100", :align => "left", :valign => "top", :style => "border-collapse: collapse; border-spacing:0; padding-top:18px; padding-right:20px;"}
                - picture = @variant&.picture
                = image_tag picture.url(:st), width: '100' if picture.present?

            %td{ :align => "left", :valign => "top", :style => "border-collapse: collapse; border-spacing: 0; margin: 0; padding: 0; padding-top: 15px; color: #000000;"}
                
                %h3{ :style => "margin-bottom:0px;" }
                    ="Detalle de la reserva - Cod. #{@order.id}"
                
                %p{ :style => "color:#4e4e4e; font-size:10px; line-height: 15px; margin:3px 0px;"  }
                    = "Fecha y Hora: #{(@order.created_at-3.hours).getutc.strftime("%d/%m/%y - %H:%M")}"
                    %br
                    = "Modelo: #{@variant.title}"
                    %br
                    = "Código de reserva: #{@order.id}"
                    %br
                    = "Método de pago: Prestamo BNA"
                    %br
                    = "Método de envío: Retiro por concesionaria "
                    %br
                    = "Total del préstamo utilizado: #{number_to_currency(@order.total, unit: "$", separator: ",", delimiter: ".")}: "
    
    
    %table#footer{ :align => 'center', :bgcolor => "#017894", :style => "padding:20px 30px; width: 100%; max-width: 600px;" }
        %tr
            %td
                %h3{ :color => "#ffffff", :align => 'left', :style => "color:#fff; margin:0px;"}
                    = "Por cualquier consulta puede contactarse con:"
                %a{ :style => "color:#ffffff; font-size:12px; line-height: 20px; margin:0px;"  }
                    = "<EMAIL> "
