- variant = item.variant
- product = variant.product
- variant_url = [item.suborder.store.hostname, variant.get_url].all?(&:present?) ? item.suborder.store.hostname + variant.get_url : '#'

%table
  %tbody
    %tr.border-bottom{:style => ""}
      %td{width: '40%', :style => "text-align: center;"}
        = link_to (image_tag variant.picture.url(:tm), size: '198x200'), variant_url if variant.picture
      %td
        %h4= t('.summary').upcase

        %p
          %strong= t('.description')
          %br
          %p{:style => "line-height: 19px;"}
            %strong
              %a{ href: variant_url }= product.title.upcase

        %p{:style => "line-height: 17px;"}
          = t('.order_id')
          %strong= item.suborder.public_id
          %br
          = t('.date')
          %strong= item.created_at.strftime("%d/%m/%y - %H:%M:%S")
          %br
          = t('.quantity')
          %strong= item.quantity
          %br
          = t('.model')
          %strong= product.title
          %br
          = t('.price')
          %strong.currency= number_to_currency(item.unit_price_charged, unit: current_currency_format)
          %br
          = t('.pickup_shipping')
          %strong= t('.coordinated_with_seller')
