- is_admin ||= false
- hide_purchase_date = hide_purchase_date.present? ? hide_purchase_date : false
- variant = item.variant
- product = variant.product
- variant_url = item.suborder.store.hostname + variant.get_url
- properties = Mkp::Product::AVAILABLE_PROPERTIES

%tr
  %td{width: '40%', :style => "border-bottom: 2px solid lightgrey; text-align: center;"}
    %a{ href: variant_url }
      = image_tag variant.picture.url(:tm), size: '198x200' if variant.picture
  %td{:style => "border-bottom: 2px solid lightgrey;"}
    %strong= t('.summary').upcase
    - unless hide_purchase_date
      %p
        = t('.date')
        %strong= item.created_at.strftime("%d/%m/%y - %H:%M:%S")
    %p
      = t('.description')
      %strong
        - if @current_store.name == 'bancomacro'
          %a{ href: variant_url }= variant.description.truncate(100)
        - else
          %a{ href: variant_url }= product.title
    - unless product.has_no_property?
      - variant.properties.keys.each do |property|
        %p
          =t('.' + property.to_s)
          - if property.to_sym == :color
            %strong= variant.color_name
          - else
            %strong= variant.properties[property]
    %p
      = t('.quantity')
      %strong= item.quantity
    - if item.points > 0
      %p
        = t('.points')
        %strong.currency= number_with_delimiter(item.points, :delimiter => '.')
    %p
      = t('.price')
      %strong.currency= number_to_currency(item.total_without_points, unit: current_currency_format)

    - payments = item.suborder.order.payments.where.not(gateway: ['VisaPuntos', 'SystemPoint'])
    - if payments.any?
      %p
      - installments = payments.first.installments
      - if installments.present?
        = t('.installments')
        %strong= installments
    %p
      = t('.order_id')
      %strong= item.suborder.public_id
%tr.border-top
  %td
    - shipment = item.suborder.shipment
    = render partial: 'mailer/partials/shipped', locals: {shipment: shipment} unless shipment.nil?