%table
  %tbody
    %tr
      %td{ colspan: 2}
        - address = shipment.destination_address
        %p{:style => "line-height: 19px;"}
          %p{ :style => "font-size: 1.5em; margin: 20px 0 0 0" }
            %strong= t('mkp.shipment_mailer.shipping_details').upcase
          %p
            = t('mkp.shipment_mailer.shipping_to')
            %strong #{address.full_name.try(:titleize)}
          %p
            = t('mkp.shipment_mailer.shipping_address')
            - address_to_print = []
            - address_to_print << address.address
            - address_to_print << address.street_number if address.street_number.present?
            - address_to_print << address.address_2
            - address_to_print << address.city
            - address_to_print << address.state
            - address_to_print << address.zip.to_s
            - address_to_print << GeoConstants::Countries.name_for_code(address.country) if address.country.present?
            %strong
              = address_to_print.compact.reject(&:empty?).join(', ').titleize
          -# =t('mkp.shipment_mailer.hot_sale_notice')
          -# =t('mkp.shipment_mailer.cyber')
- if @order.shipments.any? { |shipment| shipment.is_a?(Mkp::Shipment) } && @network == 'US'
  %p
    %strong= t('.shipping_speed_clarification')
- if @order.have_taxes?
  %p
    %strong #{t('.taxes')}:
    #{number_to_currency(@order.taxes, unit: current_currency_format)}
