json.extract! suborder, :public_id, :title, :created_at, :status, :payment_status
json.total suborder.order.payments.map {|p| p.sub_payment_amount(suborder) }.sum
json.coupon_discount suborder.coupon_discount

json.payments suborder.all_payments do |payment|
  json.status payment.status
  json.total payment.sub_payment_amount(suborder)
  json.points_used payment.points_used
  json.payment_date payment.collected_at
  json.payment_method payment.payment_method || 'Modo'
  json.unit payment.unit
  json.gateway payment.gateway
  json.installments payment.get_installments
  json.card_holder payment.card_holder_name
  begin
    json.document_type payment.gateway_data&.dig(:payer, :identification, :type) || 'DNI'
    json.external_reference_id payment.gateway_data['id']
    json.plan_tem number_to_percentage(payment.gateway_data[:installment_data][:coef].to_f, precision: 4) unless payment.gateway_data[:installment_data] == nil
  rescue
    json.document_type 'DNI'
    json.external_reference_id 'N/A'
  end
  json.document_number payment.payer_dni
  json.bin payment.get_cc_bin
  json.last_digit payment.card_last_four_digits
  json.coupon_number payment.sub_payment_ticket(suborder) || '-'
  json.auth_number payment.sub_payment_code_authorization(suborder) || '-'
  json.financial_cost_value payment.amount_coef(suborder)
  json.government_plan Mkp::Payment::INSTALLMENTS_MAP.key?(payment.get_installments)
  json.brand_name payment.get_cc_brand
  json.issuer payment.get_cc_bank
  json.installments_value payment.calculate_installment_amount(suborder)
end

json.customer do
  json.extract! suborder.customer, :first_name, :last_name, :doc_number, :email
  json.address (suborder.shipment ? suborder.shipment.destination_street_number : '')
  if suborder&.shipment&.billing_address.present?
    json.billing_address do
      json.extract! suborder&.shipment&.billing_address, :name, :email, :doc_type, :doc_number, :phone, :address, :street_number, :depth, :country, :state, :city, :postal_code || []
    end
  else
    json.billing_address nil
  end
end
json.products suborder.items do |item|
  json.extract! item, :quantity
  json.extract! item.product, :title
  json.extract! item.variant, :sku
  json.extract! item, :price
  json.extract! item, :unit_price_charged
end
json.shipments suborder.shipments do |shipment|
  json.extract! shipment, :id, :status
  json.extract! shipment, :charged_amount
  json.destination do
    json.name shipment.destination_full_name
    json.address shipment.destination_street_number
    json.city shipment.destination_city
    json.province shipment.destination_state
    json.floor_department shipment.destination_address.address_2
    json.extract! shipment.destination_address,  :telephone, :zip, :doc_number
  end
end
