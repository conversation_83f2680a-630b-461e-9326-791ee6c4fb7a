json.extract! @current_user, :email, :first_name, :last_name, :image, :doc_type, :doc_number, :gender, :birthday_at, :telephone, :temporary_email,:cuit
json.gender_name t("api.angular.gender.#{@current_user.gender}")
json.points @current_user.points_enabled? && defined?(points) ? points || 0 : 0
json.points_money @current_user.points_to_money(points: 1)
json.customer_points_enabled @current_user.points_enabled?
json.points_error defined?(points_error) && points_error ? JSON.parse(points_error) : nil
json.site_points_enabled LoyaltyConfig.active
