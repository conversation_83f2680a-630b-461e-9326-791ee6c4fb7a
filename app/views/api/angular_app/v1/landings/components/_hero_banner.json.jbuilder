json.slides component.slides do |slide|
  json.open_in (/avenida\.com\.ar/ =~ slide.link).nil? ? '_blank' : '_self'
  picture = ::Pages::Picture.find_by_id(slide.desktop_picture_id)
  json.image picture.try(:image).try(:url, :desktop)
  json.mobile_image picture.try(:image).try(:url, :mobile)
  json.title slide.title
  json.description slide.description
  json.link slide.link
end

json.banners component.banners do |banner|
  picture = ::Pages::Picture.find_by_id(banner.desktop_picture_id)
  json.image picture.try(:image).try(:url, :mobile)
  json.title banner.title
  json.link banner.link
end
