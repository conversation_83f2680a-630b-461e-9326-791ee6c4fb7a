json.extract! user, :id, :email, :first_name, :last_name
json.points user.points_enabled? && defined?(points) ? points : 0
json.is_select_user user.try(:is_select_user?) || false
json.temporary_email user.try(:temporary_email) || false
json.points_money user.points_to_money(points: 1)
json.customer_points_enabled user.points_enabled?
json.points_error defined?(points_error) && points_error ? JSON.parse(points_error) : nil
json.site_points_enabled LoyaltyConfig.active
