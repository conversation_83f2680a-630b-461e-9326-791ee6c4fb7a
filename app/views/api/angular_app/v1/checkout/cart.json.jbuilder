json.extract! @checkout_cart, :max_installments, :address_id, :coupon, :customer, :data, :ip, :network,
  :payment, :purchase_id, :promotion, :landing_id, :items_per_shop, :title,
  :total_points, :taxes_cost, :coupon_items_discount,
  :coupon_total_discount, :promotion_discount, :items, :shipping_cost_points, :sp_subtotal_points, :sp_subtotal_price,
  :zones, :countries, :shipments, :get_promotion, :delivery_option_id, :total, :subtotal
json.multi_program @checkout_cart.multi_program?
json.shops do
  json.partial! 'api/angular_app/v1/shops/shop', collection: @checkout_cart.shops, as: :shop
end
json.balance_due @checkout_cart.balance_due?
# json.is_pickeable @checkout_cart.is_pickeable?
json.has_shipment_bonification @checkout_cart.has_shipment_bonification?
json.has_shipment_promotion @checkout_cart.has_shipment_promotion?
json.discount @discount.to_f
json.delivery_options @cart.get_delivery_options
json.choosen_delivery_options @cart.get_selected_delivery_options
json.shipping_cost @cart.get_shipping_cost

json.user_points (@current_user.try(:points) rescue 0) || 0
if @cart.address.present?
  json.address do
    json.extract! @cart.address, :first_name, :last_name, :telephone, :address, :address_2, :city, :state, :country, :zip, :street_number, :id, :doc_type, :doc_number, :email
    json.email @cart.address.addressable.email
  end
else
  json.address nil
end

json.checkout_items @checkout_cart.checkout_items do |item|
  json.extract! item, :quantity, :title, :name, :currency, :points
  json.shop do
    json.partial! 'api/angular_app/v1/shops/shop', shop: item.shop
  end
  json.variant { json.partial! 'api/angular_app/v1/variants/variant', variant: item.variant }
  json.product { json.partial! 'api/angular_app/v1/products/product', product: item.product }
  json.on_sale item.on_sale?
  if @warranty_variant&.id == item.variant.id
    json.price @warranty_price
    json.sale_price @warranty_price
    json.total @warranty_price
  else
    json.price item.price
    json.sale_price item.sale_price
    json.total item.total_with_points_discount(@checkout_cart.store)
  end
end

json.partial! 'api/angular_app/v1/gateways/gateways'

json.insurance @checkout_cart.insurance_option_for_items? ? @checkout_cart.insurance_cost_by_currency : nil
json.identity_questions @checkout_cart.store.strategy_renaper.enabled?
