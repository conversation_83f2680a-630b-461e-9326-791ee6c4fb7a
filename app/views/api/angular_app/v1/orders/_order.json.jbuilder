def payment_present_attibutes(payment)
  @payment_attributes.select{|attr|  payment.send(attr).present?}
end

json.id order.listable_id
json.order_type order.listable_type.gsub('::', '-').underscore
json.extract! order, :title, :coupon_discount, :total, :total_points, :total_without_points, :target_item, :created_at, :gateway, :miles, :total_spended_points
if (payment = order.payment).present?
  json.extract! order.payment, *payment_present_attibutes(payment)
end

json.suborders order.suborders do |suborder|
  json.shipments suborder.shipments do |shipment|
    json.status shipment.status || 'delivered'
    json.updated_at shipment.status_last_updated_at
    json.tracking_number shipment.label.try(:tracking_number) || nil
    json.courier shipment.label.try(:courier) || nil
    json.shipment_kind shipment.shipment_kind_label
  end
  json.status suborder.status
  json.total_points suborder.total_points
  json.total_without_points suborder.total_without_points
  json.total suborder.total
  json.public_id suborder.public_id
  json.created_at suborder.created_at
  json.items suborder.items do |item|
    json.quantity item.quantity
    json.title item.title
    json.sku item.variant.gp_sku
    json.price item.total
    json.url item.variant.get_url
    json.transaction_type item.product.transaction_type
    json.status "#{item.prefix_status}#{t("pioneer.orders.#{item.status}")}"
    json.picture do
      json.id item.variant.picture.present? ? item.variant.picture.id : nil
      json.thumb item.variant.picture.present? ? item.variant.picture.url(:t) : nil
      json.large item.variant.picture.present? ? item.variant.picture.url(:l) : nil
      json.st item.variant.picture.present? ? item.variant.picture.url(:st) : nil
    end
  end
end
