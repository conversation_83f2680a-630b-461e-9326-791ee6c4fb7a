<?xml version="1.0" encoding="utf-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ipg="http://ipg-online.com/ipgapi/schemas/ipgapi" xmlns:v1="http://ipg-online.com/ipgapi/schemas/v1">
  <soapenv:Header/>
  <soapenv:Body>
     <ipg:IPGApiOrderRequest>
        <v1:Transaction>
           <v1:CreditCardTxType>
              <v1:StoreId><%= @firstdata_store %></v1:StoreId>
              <v1:Type>sale</v1:Type>
           </v1:CreditCardTxType>
           <v1:CreditCardData>
              <v1:CardNumber><%= @payment_data[:card_number] %></v1:CardNumber>
              <v1:ExpMonth><%= @payment_data[:card_expiration_month] %></v1:ExpMonth>
              <v1:ExpYear><%= @payment_data[:card_expiration_year] %></v1:ExpYear>
              <v1:CardCodeValue><%= @payment_data[:security_code] %></v1:CardCodeValue>
           </v1:CreditCardData>
           <v1:Payment>
              <v1:ChargeTotal><%= @amount %></v1:ChargeTotal>
              <v1:Currency>ARS</v1:Currency>
              <v1:numberOfInstallments><%= @payment_data[:installment_number] %></v1:numberOfInstallments>
           </v1:Payment>
           <v1:Billing>
              <v1:Name><%= @payment_data[:cardholder_name] %></v1:Name>
           </v1:Billing>
        </v1:Transaction>
     </ipg:IPGApiOrderRequest>
  </soapenv:Body>
</soapenv:Envelope>