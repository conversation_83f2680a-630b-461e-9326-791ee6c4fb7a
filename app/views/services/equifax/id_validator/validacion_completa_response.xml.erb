<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
xmlns:web="http://webservices.idvalidator.veraz.com"
xmlns:xsd="http://request.model.idvalidator.veraz.com/xsd">
  <soapenv:Header/>
     <soapenv:Body>
      <web:mensajeRequest>
        <web:identificador>
          <xsd:autorizacion></xsd:autorizacion>
          <xsd:lote>xxxxxxxxxxxxxxxx</xsd:lote>
          <xsd:producto>ETAPA2</xsd:producto>
          <xsd:userLogon>
            <xsd:cliente></xsd:cliente>
            <xsd:matriz><%= equifax_configuration.matrix %></xsd:matriz>
            <xsd:password><%= equifax_configuration.password %></xsd:password>
            <xsd:sector><%= equifax_configuration.sector %></xsd:sector>
            <xsd:sucursal><%= equifax_configuration.office %></xsd:sucursal>
            <xsd:usuario><%= equifax_configuration.user %></xsd:usuario>
          </xsd:userLogon>
        </web:identificador>
        <web:respuestas>
          <xsd:integrante>
            <xsd:valor>1</xsd:valor>
            <xsd:variables>
              <xsd:cuestionario><%= equifax_configuration.questionnaire %></xsd:cuestionario>
              <xsd:respuesta>
                <% questions.each { |question|%>
                  <xsd:id_pregunta><%= question[:id] %></xsd:id_pregunta>
                  <xsd:id_respuesta><%= question[:response[:id]%></xsd:id_respuesta>
                <% end %>
              </xsd:respuesta>
            </xsd:variables>
          </xsd:integrante>
        </web:respuestas>
      </web:mensajeRequest>
    </soapenv:Body>
</soapenv:Envelope>