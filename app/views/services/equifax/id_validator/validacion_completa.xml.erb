<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:web="http://webservices.idvalidator.veraz.com" xmlns:xsd="http://request.model.idvalidator.veraz.com/xsd">
<soapenv:Header/>
   <soapenv:Body>
      <web:mensajeRequest>
         <web:identificador>
            <xsd:autorizacion>0</xsd:autorizacion>
            <xsd:lote></xsd:lote>
            <xsd:producto>ETAPA1</xsd:producto>
            <xsd:userLogon>
               <xsd:cliente></xsd:cliente>
               <xsd:matriz><%= equifax_configuration.matrix %></xsd:matriz>
               <xsd:password><%= equifax_configuration.password %></xsd:password>
               <xsd:sector><%= equifax_configuration.sector %></xsd:sector>
               <xsd:sucursal><%= equifax_configuration.office %></xsd:sucursal>
               <xsd:usuario><%= equifax_configuration.user %></xsd:usuario>
            </xsd:userLogon>
         </web:identificador>
         <web:consulta>
            <xsd:integrante>
               <xsd:cuestionario></xsd:cuestionario>
               <xsd:documento><%= @data[:documento] %></xsd:documento>
               <xsd:nombre><%= @data[:nombre] %></xsd:nombre>
               <xsd:referencia></xsd:referencia>
               <xsd:sexo><%= @data[:sexo] %></xsd:sexo>
               <referencia>0</referencia>
            </xsd:integrante>
            <xsd:integrantes>1</xsd:integrantes>
         </web:consulta>
      </web:mensajeRequest>
   </soapenv:Body>
</soapenv:Envelope>