- network = @network || 'AR'
- language = @language || 'es'
doctype html
html.desktop.no-js lang=I18n.locale.to_s
  head
  body
    table#background-table bgcolor="#f2f2f2" align="center" cellpadding="0" cellspacing="0" border="0" style="width: 100%; padding:40px 0px;"
        tr
          td align='center'
            table border="0" bgcolor="#ffffff" cellpadding="0" cellspacing="0" style="max-width: 600px; width: 100%; background-color: #fff;"
              tbody style="font-family: 'Verdana', sans-serif;"
                tr#header height="90" background-color='#FFF'
                  td bgcolor="#ffffff" style="padding: 0px 20px;"
                    img align='left' src= "#{@current_store.decorate.try(:logo_delivered_to_customer)}" background-color= "FFFFFF"

                - if content_for?(:subheader)
                  tr
                    = yield :subheader
                tr
                  td
                    table#body cellpadding="0" cellspacing= "0" border="0" style="width: 100%;"
                      tbody style="font-family: 'Verdana', sans-serif; font-size:11px;"
                        tr
                          td#content style="padding: 0px 20px 20px 20px"
                            = yield
                tr
                  td.
                    = render partial: 'v5/partials/mailer/footer_user'