class PaymentProgram < ActiveRecord::Base
  has_many :shops, class_name: 'Mkp::Shop'
  has_many :installments, dependent: :delete_all

  accepts_nested_attributes_for :installments, reject_if: :all_blank, allow_destroy: true

  scope :by_store, lambda { |store_id|
    where(id: Mkp::ShopStore.select(:payment_program_id).where(store_id: store_id))
  }

  def display_name(installment)
    # todo sacar el ahora a una variable prefijo y el 12 a un check que indique incluir cantidad de cuotas en el display name
    " (Ahora #{installment.number})"
  end
end
