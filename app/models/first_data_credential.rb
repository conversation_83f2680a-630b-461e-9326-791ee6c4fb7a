class FirstDataCredential < ActiveRecord::Base
  belongs_to :store, class_name: "Mkp::Store"
  belongs_to :shop, class_name: "Mkp::Shop", required: false

  has_attached_file :ssl_cert, path: 'avenida/:attachment/certs/:token_:style_:filename'
  has_attached_file :ssl_cert_key, path: 'avenida/:attachment/certs/:token_:style_:filename'
  do_not_validate_attachment_file_type :ssl_cert
  do_not_validate_attachment_file_type :ssl_cert_key

  def categories_ids
    category ? category.split(',').map(&:to_i) : []
  end

  def token
    store.present? ? store.token : shop.stores.first.token
  end
end
