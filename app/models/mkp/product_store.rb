module Mkp
  class ProductStore < ActiveRecord::Base
    belongs_to :product
    belongs_to :store

    #validates :points, presence: true, numericality: true
    validates :visa_puntos_equivalence, numericality: { greater_than: 0 }, allow_nil: true
    delegate :name, to: :store, allow_nil: true, prefix: true
    delegate :title, to: :product, allow_nil: true, prefix: true
    validates :order_visibility, numericality: { greater_than_or_equal_to: 0, less_than_or_equal_to: 5 }
    enum status: [:pending, :approved, :rejected]

    scope :not_approved, -> { where("status <> ?", Mkp::ProductStore.statuses[:approved]) }

    after_commit :index_variants!

    private

    def index_variants!
      product.save
      product.variants.each(&:save)
    end
  end
end
