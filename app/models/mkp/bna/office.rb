module Mkp
  module Bna
    class Office < ActiveRecord::Base
      validates :token, uniqueness: true

      delegate :login, :password, to: :user, allow_nil: true

      has_many :suborders, class_name: 'Mkp::Suborder', dependent: :destroy
      has_many :shops, class_name: 'Mkp::Shop'
      has_one :role, class_name: 'Pioneer::Role', dependent: :destroy
      belongs_to :user


      def self.to_csv
        CSV.generate(headers: true) do |csv|
          csv << column_names
          all.each do |office|
            csv << office.attributes.values_at(*column_names)
          end
        end
      end
    end
  end
end
