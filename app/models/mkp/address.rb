module Mkp
  class Address < BaseAddress
    attr_reader :email
    attr_accessor :network, :full_name
    serialize :billing_address, OpenStruct

    after_create :new_address_verify

    belongs_to :addressable, polymorphic: true

    scope :verified, -> { where(new_address: true) }

    def self.build_from(customer)
      case customer.class.to_s
      when 'SocialUser', 'Mkp::Guest'
        new(first_name: customer.first_name, last_name: customer.last_name)
      when 'Brand'
        new(first_name: customer.name)
      end
    end

    def full_name
      "#{first_name} #{last_name}".strip
    end

    def full_name=(names)
      name_parts = names.split(' ')
      self.first_name = name_parts.shift
      self.last_name = name_parts.join(' ')
    end

    def to_s
      return "#{full_name}-#{address}-#{city}-#{zip}"
    end

    def full_address
      address = [self.address]
      address << ((self.street_number.present?) ? self.street_number : self.address_2)
      address.select{|v| v.present?}.join(', ')
    end

    def email
      return addressable.email if addressable.present? && addressable.respond_to?(:email)
    end

  end
end
