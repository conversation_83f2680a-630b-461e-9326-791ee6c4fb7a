# frozen_string_literal: true

module Mkp
  module StatusChange
    # Class to manage status changes for order items
    class OrderItemStatusManage
      class << self
        def status_change(entity, status)
          if respond_to?("#{status}!", :include_private)
            send("#{status}!", entity)
          end
          post_status_change_actions(entity, status)
        end

        private

        def post_status_change_actions(entity, status); end

        def unfulfilled!(entity)
          entity.update!(status: 'unfulfilled') unless entity.unfulfilled?

          entity
        end

        def in_process!(entity)
          unless entity.in_process?
            entity.update!(status: 'in_process')
          end

          entity
        end

        def shipped!(entity)
          entity.update!(status: 'shipped') unless entity.shipped?

          entity
        end

        def ready_to_pick_up!(entity)
          entity.each { |e| e.update!(status: 'ready_to_pick_up') }

          entity
        end

        def delivered!(entity)
          entity.update!(status: 'delivered') unless entity.delivered?

          entity
        end

        def cancelled!(entity)
          if entity.unfulfilled? || entity.in_process? || entity.suborder.warranty?
            unless entity.cancelled?
              entity.update!(status: 'cancelled')
              operation = entity.order.is_paid? ? :increase_quantity : :decrease_reserved_quantity
              entity.variant.send(operation, entity.quantity)
            end
          end

          entity
        end

        def not_delivered!(entity)
          if entity.unfulfilled? || entity.in_process? || entity.shipped?
            entity.update!(status: 'not_delivered') unless entity.not_delivered?
          end

          entity
        end

        def returned!(entity)
          unless entity.returned?
            entity.update_attributes!(status: 'returned')
            operation = entity.order.is_paid? ? :increase_quantity : :decrease_reserved_quantity
            entity.variant.send(operation, quantity)
          end

          entity
        end
      end
    end
  end
end
