# frozen_string_literal: true

module Mkp
  class OrderItem < ActiveRecord::Base
    belongs_to :currency, class_name: 'C<PERSON><PERSON>cy'
    belongs_to :product, unscoped: true
    belongs_to :suborder, class_name: 'Mkp::Suborder'
    belongs_to :variant, unscoped: true
    has_many :payments
    has_many :entity_status_changes, class_name: 'Mkp::StatusChange::EntityStatusChange', as: :entity, dependent: :destroy
    has_and_belongs_to_many :shipments,
                            class_name: 'Mkp::Shipment',
                            join_table: :mkp_shipment_items,
                            foreign_key: :order_item_id,
                            association_foreign_key: :shipment_id
    has_one :gift_card
    has_one :voucher, through: :product
    has_one :coupon, through: :gift_card
    has_one :external_coupon

    alias_attribute :status_changes, :entity_status_changes

    delegate :title, :name, to: :variant
    delegate :order, to: :suborder

    after_save do
      order.update_attribute(:updated_at, Time.zone.now)
      suborder.update_attribute(:updated_at, Time.zone.now)
    end

    after_create :build_giftcard_coupon

    STATUS = %w[unfulfilled in_process shipped delivered cancelled returned not_delivered ready_to_pick_up pending].freeze
    BNA_STATUSES = %w[booked approved declined billed posted cancelled pending_cancellation overdue].freeze
    CANCELLABLE_STATUS = %w[unfulfilled in_process pending].freeze

    STATUS.each do |status|
      scope status, -> { where(status: status) }

      define_method("#{status}?".to_sym) do
        attributes['status'] == status
      end
    end

    def hs_tariff_number
      ''
    end

    def on_sale?
      sale_price.present? && sale_price > 0
    end

    delegate :iva, to: :product

    def total_iva
      (product.iva * total) / 100
    end

    def total
      quantity * unit_price_charged
    end

    def total_without_points
      price = sale_price.presence || self.price
      total = (price * quantity)

      # Descuento por puntos
      if points.present? && total > 0
        points_value = points * point_equivalent_with_iva
        total -= points_value.round
      end

      # Costo de envío
      total += suborder.order.shipments_cost if suborder.order.shipments_cost.present?

      # Descuento por cupón
      if suborder.order.has_coupon? && suborder.order.bonified_amount == 0
        total -= suborder.order.coupons_discount
      end

      total
    end

    def total_weight
      quantity * (product.weight || 0)
    end

    def price_in_cents
      (price * 100).round
    end

    def unit_price_charged
      on_sale? ? sale_price : price
    end

    def point_equivalent
      return self[:point_equivalent] if self[:point_equivalent].present? && self[:point_equivalent] > 0
      points_without_iva = 0
      if suborder.order.payments.where(gateway: 'LoyaltyBna').exists?
        points_with_iva = (self.suborder.order.points_money / self.suborder.order.points)
        points_without_iva = points_with_iva / iva_coefficient
      end
      points_without_iva
    end

    def points
      return self[:points] if self[:points].present? && self[:points] > 0
      return 0 unless self.suborder.present?
      return 0 unless suborder.order.payments.where(gateway: 'LoyaltyBna').exists?
      total_amount_for_suborder = self.suborder.items.sum(:price)
      this_item_proportion = self.price / total_amount_for_suborder
      self.suborder.total_points * this_item_proportion
    end

    def point_equivalent_with_iva
      point_equivalent ? (point_equivalent * iva_coefficient).round(5) : 0
    end

    def points_money
      point_equivalent_with_iva * points
    end

    def point_price
      points && point_equivalent ? (points * point_equivalent * iva_coefficient).round(4).ceil : 0
    end

    def point_price_without_iva
      points && point_equivalent ? (points * point_equivalent).round(2) : 0
    end

    def commission
      return 0 if product.shop.sale_commission.blank?

      unit_price_charged * (product.shop.sale_commission.to_f / 100)
    end

    def sale_discount
      return 0 unless on_sale?

      (price - sale_price).to_f
    end

    def coupon_discount
      return 0.to_f unless order.has_coupon?
      return coupon_discount_amount(order.coupon) if order.coupon.present?
      return coupon_discount_amount(suborder.coupon) if suborder.coupon.present?
    end

    def shipment_cost
      shipping_cost = suborder.shipment_cost || 0.0
      return shipping_cost if shipping_cost.zero? || suborder.items.count.eql?(1)

      total = suborder.get_suborder_weight
      weight = product.final_weight * (total_weight.eql?(0) ? 1 : quantity)
      ((shipping_cost * weight) / total).round(2)
    end

    def iva_coefficient
      1 + (iva / 100)
    end

    def has_external_objects?
      product.external_objects.present?
    end

    def prefix_status
      return '' unless current_shipment.present? && current_shipment.shipment_kind.present?
      return 'Cambio - ' if  is_exchange?
      return 'Devolución - ' if is_refund?

      ''
    end

    def current_shipment
      @current_shipment ||= shipments.order('created_at').last
    end

    def is_exchange?
      %w[exchange_refund exchange_change].include? current_shipment.shipment_kind
    end

    def is_refund?
      current_shipment.shipment_kind == 'refund'
    end

    def is_refundable?
      return false if current_shipment.blank?

      last_shipments = is_exchange? ? shipments.order(:created_at).last(2) : [current_shipment]
      return false unless last_shipments.map(&:status).uniq.count == 1

      last_shipments_close = last_shipments.all? { |last_shipment| last_shipment.delivered? || last_shipment.not_delivered? }
      delivered? && payments.all?(&:collected?) && last_shipments_close
    end

    def self.options_for_select
      STATUS.map { |state| [state.titleize, state] }
    end

    def self.bna_options_for_select
      BNA_STATUSES.map { |state| [state.titleize, state] }
    end

    protected

    def validate
      errors.add(:quantity, 'must be a positive value') unless quantity && quantity >= 0
    end

    def build_giftcard_coupon
      product.voucher.create_gift_card_to_item!(self) if product.is_voucher?
    end

    def coupon_discount_amount(coupon)
      return 0.to_f if coupon.value_policy? || (on_sale? && !coupon.apply_on_sale)

      (unit_price_charged * coupon.percent.to_f / 100.0).round
    end
  end
end
