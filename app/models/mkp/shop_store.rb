class Mkp::ShopStore < ActiveRecord::Base
  has_paper_trail
  belongs_to :shop, class_name: "Mkp::Shop"
  belongs_to :store, class_name: "Mkp::Store"
  belongs_to :payment_program
  belongs_to :bin

  scope :active, -> { where(active: true) }
  scope :at_date_start, -> (date) { where('start_date < ? OR start_date is null', date) }
  scope :at_date_end, -> (date) { where('end_date > ? OR end_date is null', date) }

  scope :active_now, -> { active.at_date_start(DateTime.current).at_date_end(DateTime.current) }
  # after_save :set_products_approved_for_stores
	# after_destroy :set_products_approved_for_stores

	def set_products_approved_for_stores
		shop.products.each do |product|
      product.set_approved_for_stores
    end
	end
end
