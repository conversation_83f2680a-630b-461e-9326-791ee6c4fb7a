module Mkp
  class Customer < ActiveRecord::Base
    include UserMethods
    include ::Concerns::SoftDestroy
    # Include default devise modules.
    devise :database_authenticatable, :registerable,
           :recoverable, :rememberable, :trackable

    belongs_to :store
    has_many :addresses, class_name: 'Address', as: :addressable
    has_many :coupons
    has_one :wishlist
    has_one :member, class_name: 'Member', dependent: :destroy
    has_one :subscription, class_name: 'Subscription', dependent: :destroy
    has_many :carts, class_name: 'Mkp::Cart', as: :customer, dependent: :destroy
    has_many :identities, foreign_key: :user_id, dependent: :destroy
    has_many :sube_cards, foreign_key: :mkp_customer_id, dependent: :destroy
    has_many :orders
    enum gender: [:male, :female, :non_binary]
    enum doc_type: [:DNI, :DU, :LC, :LE, :PAS, :CUIT, :CUIL]
    enum macro_category: [:regular, :selecta]

    validates :email, uniqueness: { scope: :store_id }, allow_nil: true
    validates :doc_number, uniqueness: { scope: :store_id }, allow_nil: true
    validates :cuit, uniqueness: {scope: :store, conditions: -> { where(deleted_at: nil) }}, :allow_nil => true, length: { is: 11 }, if: -> {cuit_changed?}
    validates :password, :password_confirmation, presence: true, on: :create
    validates :password, confirmation: true
    validate :cuit_must_include_doc_number, if: -> { cuit.present? }
    validates_presence_of :store
    validates_presence_of :subscription, :if => :is_tdd?

    before_create :build_default_wishlist

    accepts_nested_attributes_for :addresses, allow_destroy: true, reject_if: :all_blank
    accepts_nested_attributes_for :member, allow_destroy: true, reject_if: :all_blank
    accepts_nested_attributes_for :subscription, allow_destroy: true

    scope :not_deleted, -> { where(['mkp_customers.deleted_at IS ?', nil]) }
    scope :deleted, -> { where(['mkp_customers.deleted_at IS NOT ?', nil]) }
    
    scope :with_store_id, ->(store_ids) { where(store_id: [*store_ids]) }
    scope :created_at_gte, ->(reference_time) { where('mkp_customers.created_at >= ?', reference_time) }
    scope :created_at_lt, ->(reference_time) { where('mkp_customers.created_at < ?', reference_time) }
    # scope :search_query, ->(query) do
    #   query = query.to_s
    #   return if query.blank?

    #   num_or_conds = 4
    #   terms = query.downcase.split(/\s+/)
    #   terms = terms.map{ |e| ('%' + e.gsub('*', '%') + '%').gsub(/%+/, '%')}
    #   where('mkp_customers.doc_number LIKE ? OR mkp_customers.email LIKE ? OR mkp_customers.first_name LIKE ? OR mkp_customers.last_name LIKE ?', terms[0], terms[0], terms[0], terms[0])
    #   debugger
    # end

    scope :search_query, ->(query) do
      query = query.to_s
      return if query.blank?

      num_or_conds = 4
      terms = query.downcase.split(/\s+/).map do |e|
        (e.gsub('*', '%') + '%').gsub(/%+/, '%')
      end
      sql = terms.map do
        '(LOWER(mkp_customers.first_name) LIKE ? OR LOWER(mkp_customers.last_name) LIKE ? OR LOWER(mkp_customers.email) LIKE ? OR mkp_customers.doc_number LIKE ?)'
      end.join(' AND ')
      where(sql, *terms.map { |e| [e] * num_or_conds }.flatten)
    end

    filterrific available_filters: %i[
      created_at_gte
      created_at_lt
      search_query
      with_store_id
    ]

    searchable do
      text :email
      text :doc_number
      text :first_name
      text :last_name
      boolean :deleted do
        deleted_at?
      end
      integer :store_id, references: Store
    end

    def providers
      identities.map(&:provider)
    end

    # def points
    #   return 0 unless user_identify.presence
    #   SystemPoints::User.new(self).points
    # end

    def is_select_user?
      selecta?
    end

    def self.bna_registration_validation(current_store_id, user_errors)
      if Mkp::Store::BNA_STORE_IDS.include?(current_store_id) && (user_errors[:doc_number].present? || user_errors[:email].present?)
        I18n.t('models.mkp.customer.bna_registration_error')
      else
        user_errors.full_messages
      end
    end

    def active_for_authentication?
      super && !deleted_at
    end

    def update_last_sign_in(date: DateTime.now)
      update(last_sign_in_at: date)
    end

    def last_validation
      RenaperAnswer.where(doc_number: doc_number).last
    end

    def set_validator
      update(validator: validator_name(validation: last_validation))
    end

    def points_enabled?
      validator == 'Equifax' || validator == 'Renaper' || validator == 'Equifax CC'
    end

    def points(with_cache: true)
      return 0 unless self.id && self.doc_number
      raise StandardError.new({message_type: :text, message: "En este momento no podemos acceder a tus puntos."}.to_json) unless CHECK_POINTS
      cache_key = "/users/#{self.id}/points"
      Rails.cache.delete(cache_key) unless with_cache
      Rails.cache.fetch(cache_key, expires_in: 5.seconds) do
        ret = 0
        response = nil
        begin
          timeout_in_seconds = 6
          Timeout.timeout(timeout_in_seconds) do
            response = Loyalty::BnaService.new.get_points(document_number: self.doc_number)
          end
        rescue Timeout::Error => e
          Rails.logger.error "Error de timeout al obtener los puntos del usuario #{self.id}: #{e.message}"
          raise StandardError.new({message_type: :text, message: "La solicitud tardó demasiado. Mostramos tus puntos más tarde."}.to_json)
        rescue StandardError => e
          Rails.logger.error "Error al obtener los puntos del usuario #{self.id}: #{e.message}"
          raise Loyalty::ApiExceptions::ApiExceptionError.new({message_type: :text, message: "En este momento, no se pueden visualizar tus puntos. Te invitamos a ingresar en unos minutos."}.to_json)
        end
        if response.nil?
          raise StandardError.new({message_type: :text, message: "En este momento no podemos acceder a tus puntos."}.to_json)
        elsif response['ResponseCode'].to_s.in?(['0', '1', '2'])
          ret = response['ActivePoints'].to_i
        elsif response['ResponseCode'].to_s.in?(['3', '4', '5'])
          raise StandardError.new({message_type: :text, message: 'En este momento, tus puntos no pueden ser informados. Te invitamos a comunicarte al 0810-666-1515.'}.to_json)
        elsif response['ResponseCode'].to_s.in?(['6'])
          raise StandardError.new({message_type: :text_with_link, message: 'Cuando sos cliente de Banco Nación con tus compras sumas puntos del Programa de Beneficios. Te invitamos a solicitar tu tarjeta ingresando', link: "https://solicitudes.bna.com.ar/formulario/datospersonales/TC?wc=botoncredito&utm_source=botoncredito&utm_medium=bnacomar"}.to_json)
        else
          raise StandardError.new({message_type: :text, message: 'Error desconocido al obtener los puntos del usuario'}.to_json)
        end
        return ret
      end
    end

    def points_to_money(with_cache: true, points: nil)
      (points || self.points(with_cache: with_cache)) * LoyaltyConfig.conversion
    end

    def money_to_points(money)
      (money / LoyaltyConfig.conversion).ceil
    end

    def spend_points_from_amount(amount, purchase_id, sample_item)
      return false unless self.id && self.doc_number
      return unless amount > 0
      point_to_spend = money_to_points(amount)
      spend_points(point_to_spend, purchase_id, sample_item)
    end

    def spend_points(points, purchase_id, sample_item)
      service=Loyalty::BnaService.new
      service.purchase(
        document_number: self.doc_number,
        purchase_id: purchase_id.to_s.gsub('-', '')[0..19],
        points: points.to_i,
        commentary: "Compra Tienda BNA #{sample_item}"[0..179],
        transaction_type: "R",
        origin: 554610002)
      true
    end

    def rollback_points_transaction(purchase_id)
      return false unless self.id && self.doc_number
      service=Loyalty::BnaService.new
      service.purchase(
        document_number: self.doc_number,
        purchase_id: purchase_id.to_s.gsub('-', '')[0..19],
        points: 0, # es lo mismo, rollbackea toda la trx
        commentary: "Compra Tienda BNA ID #{purchase_id}",
        transaction_type: "A",
        origin: 554610002)
      true
    end

    private

    def validator_name(validation: nil)
      validation&.type&.split(/(?=[A-Z])/)&.first
    end

    def build_default_wishlist
      build_wishlist
      true
    end

    def sp_create
      SystemPoints::Worker::GenerateUserWorker.perform_async(self.id) if store.system_point_availability?
    end

    def is_tdd?
      store.id.eql?(14)
    end

    def is_member?
      return unless store.id.eql?(14)
      return unless doc_number.present? && gender.present?
      response = Boca::Client.post_member_validation(self)
      response.try(:[], "es_socio")
    end
    def cuit_must_include_doc_number
      return if doc_number.blank?
      return if !cuit_changed?
      unless cuit.include?(doc_number.to_s)
        errors.add(:cuit, "El CUIT debe contener el numero de documento")
      end
    end

  end
end
