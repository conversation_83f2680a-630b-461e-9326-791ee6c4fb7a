module Mkp
  class Product < ActiveRecord::Base
    has_paper_trail
    include ::Concerns::Favoritable
    include ::Concerns::HasPublishablePictures
    include ::Concerns::SoftDestroy
    include ::Concerns::HasReviews
    include ::Concerns::DataHashable
    include ::Concerns::Filterable
    include ::Concerns::<PERSON>ucardas

    APPLY_FOR_CHANGE_STATUS = %w[category_id regular_price sale_price
                                 second_category_id origin_of_product available_on].freeze

    AVAILABLE_PROPERTIES = %i[
      color
      dimensions
      hardness
      length
      material
      size
      percentage
      payment_method
      noproperty
      period
      external
      articulo
      coef
      date
    ].freeze

    PERCENTAGE = [
      { value: 10 },
      { value: 15 },
      { value: 20 },
      { value: 25 },
      { value: 30 },
      { value: 40 },
      { value: 50 }
    ].freeze

    PERIOD = [
      { value: 6 },
      { value: 12 },
      { value: 18 },
      { value: 24 }
    ].freeze

    PAYMENT_METHODS = [
      { key: 1, value: 'Visa' },
      { key: 2, value: 'Visa debito' },
      { key: 3, value: 'Mastercard' },
      { key: 4, value: 'Amex' }
    ].freeze

    PROPERTIES_LIMIT = 3

    COMPATIBILITY_PROPERTIES = { hardness: 'Propiedad uno', length: 'Propiedad dos', material: 'Propiedad tres' }.freeze

    ENERGY_EFFICIENCY_OPTIONS = ['A+++', 'A++', 'A+', 'A', 'B', 'C', 'D', 'E', 'F', 'G'].freeze

    attr_accessor :published_pictures

    belongs_to :currency
    belongs_to :manufacturer, counter_cache: true
    belongs_to :shop
    belongs_to :category
    belongs_to :second_category, class_name: 'Mkp::Category'
    belongs_to :cucarda

    has_one  :voucher, dependent: :destroy
    has_many :order_items
    has_many :questions
    has_many :suborders, through: :order_items
    has_many :variants, dependent: :destroy
    has_many :packages, dependent: :destroy
    has_many :product_stores, dependent: :destroy
    has_many :installments
    has_many :customer_reservation_purchases, class_name: 'CustomerReservationPurchases', dependent: :destroy

    has_many :external_objects, class_name: 'Integration::Object', as: :integrable, dependent: :destroy

    has_and_belongs_to_many :sports,
      join_table: 'sports_on_products'

    has_and_belongs_to_many :genders, join_table: 'genders_on_products', uniq: true

    has_and_belongs_to_many :pro_athletes,
      join_table: 'products_users',
      association_foreign_key: 'user_id'

    has_many_publishable_pictures :pictures,
      class_name: Mkp::Attachment::ProductPicture,
      dependent: :destroy

    # This Concern is included EXACTLY HERE because we need our associations to be fully loaded
    # so we can set the callbacks on them accordingly. Bear in mind. Do not change unless you know
    # what you're doing AND if what you're doing affects URL generation. Thanks! - horacio.
    include ::Concerns::UrlMapperProductCallbacks

    as_enum :pickup, {not_applicable: 0, not_pickeable: 1, pickeable: 2 }, { column: :pickeable_status}
    enum transaction_type: {purchasable: 0, reservable: 1, other: 2, voucher: 3, points: 4, warranties: 5}
    enum origin_of_product: {national: 0, imported: 1, assembled_in_argentina: 2}
    after_save :update_status_from_change
    # after_save :rebuild_menu
    validates :length, :width, :height, :weight,
      numericality: true,
      on: :create,
      if: Proc.new { |p| Network[p.shop.network].requires_product_dimensions? }

    validates :title,
      :description,
      :regular_price,
      :iva,
      presence: true

    validates :category,
      presence: true,
      on: :create

    validate :sale_price_between_0_and_regular_price
    validate :check_sale_price_without_taxes
    validate :check_regular_price_without_taxes

    validate :sale_price_no_zero, if: ->(product) { product.shop.stores.ids.exclude?(18) }

    validate :regular_price_no_zero, if: ->(product) { product.shop.stores.ids.exclude?(18) }

    validate :variants_similarity
    validate :at_most_four_variants_properties
    validate :must_have_one_package
    # validate :setting_coupon?

    validate :validate_origin_of_product_brand_energy_efficiency, on: %i[create update]
    validate :sale_on_must_be_before_sale_until

    extend FriendlyId
    friendly_id :title, use: %i[slugged history]

    before_create :set_default_transaction_type, if: -> { shop&.stores&.ids&.include?(43) }
    after_commit :update_published_pictures
    before_save :clean_decimals
    before_validation :curate_origin_of_product
    before_destroy { |_product| remove_habtm }
    before_save :check_cucarda_new_product
    after_update :check_cucarda_update_product

    scope :on_sale, -> { where('mkp_products.sale_price > 0 AND ? BETWEEN mkp_products.sale_on AND mkp_products.sale_until', Time.zone.now) }
    scope :with_stock, -> { joins(:variants).merge(Variant.with_stock) }
    scope :without_stock, -> { where(id: Variant.group('product_id').having('sum_quantity <= 0').sum('quantity').keys) }
    scope :available, -> { where(['mkp_products.available_on <= ?', Time.zone.now]) }
    scope :unavailable, -> { where(['mkp_products.available_on > ?', Time.zone.now]) }
    scope :not_deleted, -> { where(['mkp_products.deleted_at IS ?', nil]) }
    scope :visible, -> { joins(:shop).merge(Shop.visible) }
    scope :active, -> { available.not_deleted.visible }
    scope :random, ->(limit) { order('RAND()').limit(limit) }
    scope :by_network, ->(network) { joins(:shop).merge(Shop.by_network(network)) }
    scope :with_sports, ->(sport_ids) { joins(:sports).where('sports.id IN (?)', sport_ids) }
    scope :visibles, -> { available.visible.with_stock }
    scope :by_sku, ->(query) {eager_load(:variants)
      .where('mkp_variants.sku LIKE ? OR mkp_variants.gp_sku LIKE ?', "#{query}%", "#{query}%") }
    scope :by_shop, ->(shop_id) { where(shop_id: shop_id) }
    scope :by_category, ->(category_id) { where(category_id: category_id) }
    scope :by_properties, -> (external) { eager_load(:variants).where('mkp_variants.properties LIKE ? ', "%#{external}%") }
    scope :by_transaction_type, -> (tt)  { where(transaction_type: tt) }
    default_scope { not_deleted }

    accepts_nested_attributes_for :packages, allow_destroy: true
    accepts_nested_attributes_for :variants, allow_destroy: true
    accepts_nested_attributes_for :voucher, allow_destroy: true

    serialize :data, Hash
    serialize :data_shipment, Hash
    serialize :available_properties, Array
    serialize :available_properties_names, Hash

    searchable do
      text :title

      text :description

      text :manufacturer_name do |product|
        product.manufacturer.name if product.manufacturer.present?
      end

      integer :store_id, multiple: true, references: Store do
        (category.store_ids + shop.store_ids).uniq
      end

      integer :id

      string  :title

      integer :shop_id, references: Shop

      string :shop_id_str do
        shop_id.to_s
      end

      integer :manufacturer_id, references: Manufacturer
      string :manufacturer_id_str do
        manufacturer_id.to_s
      end

      integer :category_id, references: Category do
        category&.id
      end

      time    :available_on

      boolean :deleted, using: :deleted_at?
      boolean :purchasable
      boolean :reservable

      time    :deleted_at, trie: true
      time    :created_at, trie: true

      string :network do
        shop.network
      end

      boolean :visible_shop do
        shop.visible
      end

      boolean :is_vaucher do
        is_voucher?
      end

      boolean :shop_deleted do
        shop.deleted_at.present?
      end

      boolean :with_stock do
        total_stock > 0
      end

      boolean :on_sale do
        on_sale?
      end

      integer :root_category_id do
        category_root_id
      end

      string :root_category_id_str do
        category_root_id
      end

      time :last_sold_at, trie: true do
        order_items.order('created_at DESC').limit(1).pluck(:created_at).first
      end

      integer :sold_count, trie: true do
        sold_count
      end

      integer :rejected_for_store_ids, multiple: true, references: Store do
        product_stores.rejected.map(&:store_id)
      end

      integer :pending_for_store_ids, multiple: true, references: Store do
        product_stores.pending.map(&:store_id)
      end

      integer :approved_for_store_ids, multiple: true, references: Store do
        product_stores.approved.map(&:store_id)
      end

      string :product_status do
        product_stores.map(&:status).first
      end

      text :variants_skus do
        variants.map(&:sku).join(',')
      end

      boolean :available do
        visible?
      end

      # Índice para el stock total de las variantes
      integer :variants_quantity do
        variants.sum(:quantity)
      end

      # Índice para verificar si la tienda del producto está visible
      boolean :shop_visible do
        shop.visible
      end

      # Aumentar el peso del título para búsquedas más relevantes
      #text :title

    end

    auto_html_for :description do
      html_escape
      link target: '_blank', rel: 'nofollow'
      simple_format
    end

    delegate :network, to: :shop

    def update_status_from_change
      if changes.keys.any? { |k| APPLY_FOR_CHANGE_STATUS.include?(k) }
        product_stores.each do |ps|
          ps.update(status: 0)
          Sunspot.index! ps.product
        end
      end
    end

    def available_values_for(property_name)
      size_variant = variants.any? { |variant| variant.properties.with_indifferent_access.key?(:size) }

      size_variant ? check_properties(property_name) : check_properties_with_stock(property_name)
    end

    def all_integers?(values)
      values.all? { |value| Integer(value) rescue false }
    end

    def check_properties(property_name)
      variants.each_with_object(Set.new) do |variant, set|

        property_name = property_name[:slug].to_sym if property_name.is_a?(Hash)

        next if (value = variant.properties[:"#{property_name}"]).blank?

        if property_name == :color && variant.properties[:color].is_a?(Hash)
          value[:slug_name] = variant.color_slug_name
        end

        set << value
      end.to_a
    end

    def check_properties_with_stock(property_name)
      variants.with_stock.each_with_object(Set.new) do |variant, set|

        property_name = property_name[:slug].to_sym if property_name.is_a?(Hash)

        next if (value = variant.properties[:"#{property_name}"]).blank?

        if property_name == :color && variant.properties[:color].is_a?(Hash)
          value[:slug_name] = variant.color_slug_name
        end

        set << value
      end.to_a
    end

    def categories
      Array.wrap(category)
    end

    def category_root_id
      category.present? ? category.root.id : 0
    end

    def recommended_products(quantity = 4, ordered = false, store = nil)
      operation = ordered ? :shift : :sample
      ids = recommended_variants_ids(store).send(operation, quantity)
      Mkp::Variant.where(id: ids)
    end

    def currency_symbol
      currency&.symbol
    end

    def currency_code
      currency&.identifier
    end

    def get_url(options = {})
      if @_stored_get_url.blank?
        mfr = manufacturer.slug
        network_url = options[:network] || shop.present? ? shop.network.downcase : 'ar'
        @_stored_get_url = "/#{network_url}/#{mfr}/#{slug}"
      end

      options = { only_path: true, preview: false }.merge(options)
      host = options[:only_path] ? '' : 'http://' + HOSTNAME
      color_slug = options[:color].present? ? '-' + options[:color].parameterize('') : ''
      preview = options[:preview] ? '?preview=true' : ''

      host + @_stored_get_url + color_slug + preview
    end

    def has_property?(property_name)
      available_properties.include?(property_name)
    end

    def has_no_property?
      available_properties.include?(:noproperty)
    end

    def custom_properties
      available_properties.each_with_object([]) do |property, array|
        array.push(property) if property.is_a?(Hash)
      end
    end

    def keywords
      keywords = [slug]

      keywords << categories.map(&:slug)
      keywords << sports.map(&:slug)
      keywords << genders.map(&:slug)
      keywords << [manufacturer.name, manufacturer.slug] if manufacturer.present?

      keywords.flatten.join(',')
    end

    def manufacturer_name
      @manufacturer_name ||= manufacturer.name
    end

    def sale_fields_not_null?
      fields_not_null?('sale_on', 'sale_until', 'sale_price')
    end

    def on_sale_period?
      return false if sale_on.nil? || sale_until.nil?

      now = Time.zone.now
      sale_on < now && now < sale_until
    end

    def has_future_sale_period?
      sale_on > Time.zone.now
    end

    def on_sale?
      sale_fields_not_null? && on_sale_period?
    end

    def is_voucher?
      voucher.present?
    end

    def has_future_sale?
      sale_fields_not_null? && has_future_sale_period?
    end

    def has_sale?
      on_sale? || has_future_sale?
    end

    def pickeable?
      return false if shop.fulfilled_by_gp?

      pickable_category = category.present? ? category.flat_pickup : :not_applicable
      return false if pickup == :not_applicable && pickable_category == :not_applicable
      return false if (pickup == :not_pickeable) || (pickable_category == :not_pickeable)

      true
    end

    def picture(color = nil)
      if color.present?
        variant = variants.with_property(:color, color).first
        if variant&.picture_id
          variant.picture
        else
          pictures.first
        end
      else
        pictures.first
      end
    end

    # Be cautious using #sale_price and #price because a wide discount
    # can be applied and this will not reflect those changes.
    def price
      on_sale? ? sale_price : regular_price
    end

    def product_questions_pending
      product_questions.reject(&:has_children?)
    end

    def remove_habtm
      sports.clear
      genders.clear
    end

    def sale_price_between_0_and_regular_price
      if sale_price.present? && regular_price.present?
        if regular_price > 0 && sale_price > regular_price
          errors.add(:sale_price, I18n.t('lux.products.partials.sale_price.errors.higher_than_regular_price'))
        elsif sale_price < 0
          errors.add(:sale_price, I18n.t('lux.products.partials.sale_price.errors.lower_than_zero'))
        end
      end
    end

    def sale_price_no_zero
      return if sale_price.nil?

      if sale_price.to_f <= 0 && has_sale?
        errors.add(:sale_price, I18n.t('lux.products.partials.sale_price.errors.dont_allow_sale_price_on_zero'))
      end
    end

    def regular_price_no_zero
      if regular_price <= 0 && !is_voucher?
        errors.add(:regular_price, I18n.t('lux.products.partials.price.errors.equal_zero'))
      end
    end

    def set_third_relations
      sports    = self.sports
      genders   = self.genders
      category  = self.category

      unless category.nil?
        sports.each do |sport|
          category.sports << sport unless category.sports.include?(sport)
        end
        genders.each do |gender|
          category.genders << gender unless category.genders.include?(gender)
        end
        category.save
      end

      Menu.set(network)
    end

    def points_by_store(store_id)
      (product_stores.find_by(store_id: store_id).try(:points) || price * Mkp::Store.find(store_id).equivalent_points || 0).to_i
    end

    def points_value_by_store(store, points)
      (points * (get_points_equivalence(store) * (1 + (iva / 100)))).round
    end

    def get_points_price(store)
      if points?
        total = points_price
      else
        equivalence = get_points_equivalence(store)
        if equivalence > 0
          price = on_sale? ? sale_price : regular_price
          total = (price / (equivalence * (1 + (iva / 100)))).round
        else
          total = 0
        end
      end

      total
    end

    def get_points_equivalence_with_iva(store)
      (get_points_equivalence(store) * (1 + (iva / 100))).round(5)
    end

    def get_points_equivalence(store)
      if store.has_visa_puntos?
        equivalence = store.visa_puntos_equivalence
        equivalence = shop.visa_puntos_equivalence if shop.visa_puntos_equivalence.present?
        equivalence = category.visa_puntos_equivalence if category.visa_puntos_equivalence.present?

        if category.ancestors.any? { |parent| parent.visa_puntos_equivalence.present? }
          category.ancestors.each do |parent|
            equivalence = parent.visa_puntos_equivalence if parent.visa_puntos_equivalence.present?
          end
        end

        product_store = product_stores.find_by(store: store)
        if product_store.present? && product_store.visa_puntos_equivalence.present?
          equivalence = product_store.visa_puntos_equivalence
        end

        equivalence
      elsif store.system_point_availability?
        store.equivalent_points
      else
        0
      end
    end

    def sold_count
      order_items.sum(:quantity)
    end

    def total_stock
      variants.sum(:quantity)
    end

    def total_stock_cached
      @total_stock ||= total_stock
    end

    def variant
      variants.with_stock.first
    end

    %i[length height width weight].each do |attr|
      define_method(attr.to_s) do
        total = packages.sum(attr)
        total.zero? ? 2.0 : total
      end
    end

    %i[length_unit mass_unit].each do |unit|
      define_method(unit) do
        packages.first.send(unit) if packages.any?
      end
    end

    def weight_with_unit
      return measure(weight, mass_unit) if mass_unit.present? && weight.present?
      if category.blank?
        return measure(1, 'kilograms')
      end # default para algunos productos que por alguna razón tienen category nil

      measure, unit = category.get_dimension_values(:weight)
      unit = (mass_unit || 'kilograms') if unit.nil?
      measure((weight ||  measure), unit)
    end

    %i[length height width].each do |dimension|
      define_method("#{dimension}_with_unit") do
        return measure(send(dimension.to_s), length_unit) if all_dimensions_present?

        measure, unit = category.get_dimension_values(dimension)
        unit = (length_unit || 'millimeters') if unit.nil?
        measure((send(dimension.to_s) ||  measure), unit)
      end
    end

    def recreate_variants_visibility
      variants.update_all(visible: false)

      return if (variants_with_stock = variants.with_stock).blank?

      if display_variants && has_property?(:color)
        color_groups = variants_with_stock.group_by { |v| v[:properties][:color] }

        color_groups.each do |_color, variants|
          variant = variants.first
          variant.show
        end
      else
        variant = variants_with_stock.first
        variant.show
      end
    end

    def show_available_sibling_for!(stale_variant)
      return if (siblings = available_siblings_for_variant(stale_variant)).blank?

      # Choose other available variant as the replacement and visibilize it.
      successor = siblings.first
      successor.show
    end

    def available_siblings_for_variant(variant)
      if has_property?(:color)
        own_color = variant.properties[:color]

        # Fallback to an empty group if you were the last variant
        # in your color group with stock left (i.e. you now have
        # no group, thus nil is returned for your own color key).
        candidates = variants.with_stock.group_by do |v|
          v[:properties][:color]
        end[own_color] || []

        candidates - [variant]
      else
        [] # This is a noproperty variant.
      end
    end

    def category_group_title
      return [] if category.nil?

      category.ancestors.map(&:name)
    end

    def has_any_shop_integration?
      network_shop_integrations = Network[network].shop_integrations
      network_shop_integrations.map!(&:downcase) if network_shop_integrations.present?

      external_objects.where(integration_name: network_shop_integrations).any?
    end

    def has_custom_properties?
      !available_properties.all? { |a| a.is_a?(Symbol) }
    end

    def has_any_network_integration?
      network_integrations = Network[network].pioneer_integrations
      network_integrations.map!(&:downcase) if network_integrations.present?

      external_objects.where(integration_name: network_integrations).any?
    end

    def external_object
      external_objects.first
    end

    def available?
      available_on.present? && available_on <= Time.zone.now && at_least_one_picture?
    end

    def unavailable?
      !available? || deleted? || !shop.visible || variants.all? { |each| !each.visible? }
    end

    def visible?
      available? && shop.visible && variants.present? && variants.any?(&:visible?)
    end

    def get_gp_sku
      "#{manufacturer.sku_id}-#{id.to_s.rjust(5, '0')}"
    end

    def final_weight
      weight = weight_with_unit.to.kilograms.value
      weight > 0.0 ? weight : 2
    end

    # This replaces the `max_length` option on FriendlyId v.3.
    def normalize_friendly_id(string)
      string = string.parameterize.mb_chars.limit(50).to_s unless string.nil?
      super(string)
    end

    def set_approved_for_stores(set_pending = false)
      shop.stores.each do |store|
        if store.product_approval
          product_store = Mkp::ProductStore.find_or_create_by(product_id: id, store_id: store.id)
          product_store.pending! if set_pending && (!product_store.approved? && !product_store.rejected?)
        end
      end
    end

    def is_approved_for_store?(store)
      return true unless store.product_approval

      product_store = product_stores.find_by(store: store)
      return false if product_store.blank?

      product_store.approved?
    end

    def reservable?
      transaction_type == 'reservable'
    end

    def points?
      transaction_type == 'points'
    end

    def warranty?
      transaction_type == 'warranties'
    end

    def min_points_price_for_variants
      return nil unless voucher? || points?
      return nil unless variants.any?
      variants.map(&:points_price).flatten.reject(&:nil?).min
    end

    def available_installments(store)
      return nil unless store.bines.any?
      installments = list_installments(store)
      unless installments.nil?
        installments.map(&:number).uniq.compact.sort
      end
    end

    def max_installments_info(store, product)
      installments_amount = product.category.max_installments(store, product)
      installments = product.list_installments(store)&.find {|x| x.number == installments_amount}
      amount = (!store.percentage_fee.zero? && installments_amount == 1) ? store.discount(product.price) : (installments.try(:total_amount, product.price) || product.price).to_f
      {
        installments: installments_amount,
        amount: (amount/installments_amount).round(2).to_f,
        coef: installments.try(:coef)&.to_f || 0.0,
        cft: installments.try(:cft)&.to_f || 0.0
      }
    end

    def list_installments(store)
      installments = store.bines.first&.installments
      if shop.payment_program_for(store).present?
        installments = shop.payment_program_for(store).installments
        installments += Array(shop.modo_payment_program_for(store)&.installments) # in case it's nil
      end
    end

    def is_refundable?
      non_refundable_transaction_types = %w[voucher points]
      non_refundable_transaction_types.exclude?(transaction_type) && !title.match(/recarga /i)
    end

    def order_visibility_by_store(store_id)
      product_stores.find_by(store_id: store_id).try(:order_visibility)
    end

    def set_variant_visibility
      self.variants.each do |variant|
        if variant.quantity > 0
          variant.update!(visible: true)
          break
        end
      end
    end

    def translated_values_origin_of_product
      case self.origin_of_product
      when 'national'
        "Nacional"
      when 'imported'
        "Importado"
      else
        "Ensamblado en Argentina"
      end
    end

    def interest_free_installments(installments)
      installments.select{|installment| installment.coef == 0}.any?
    end

    def bna?(store)
      store.id == 41
    end

    def set_changes_to_show
      versions&.last&.changeset&.reject {|k,v| k unless APPLY_FOR_CHANGE_STATUS.include?(k)}
    end

    def is_new?
      updated_at == created_at
    end

    def sale_price_no_blank
      return 0 if sale_price.blank?

      sale_price.to_f
    end

    def is_warranty_category?
      self.category_id == 3603
    end

    def recalculate_available_properties
      variants.pluck(:properties).map { |properties| properties.keys }.flatten.map(&:to_sym).uniq
    end

    def at_least_one_picture?
      pictures.any?
    end

    def discount_percentage
      return 0 if sale_price.blank?

      ((regular_price - sale_price)*100/regular_price).to_f.round
    end

    def price_without_taxes
      return sale_price_without_taxes if on_sale? && sale_price_without_taxes.present?
      return regular_price_without_taxes if !on_sale? && regular_price_without_taxes.present?
    end

    def sale_price_without_taxes
      return  (sale_price / (1 + (iva / 100.0))).round(2) if (self[:sale_price_without_taxes].blank? || self[:sale_price_without_taxes] == 0) && PRICES_WITHOUT_TAXES_ENABLED && PRICES_WITHOUT_TAXES_AUTO_CALCULATION
      super
    end

    def regular_price_without_taxes
      return  (regular_price / (1 + (iva / 100.0))).round(2) if (self[:regular_price_without_taxes].blank? || self[:regular_price_without_taxes] == 0) && PRICES_WITHOUT_TAXES_ENABLED && PRICES_WITHOUT_TAXES_AUTO_CALCULATION
      super
    end

    private

    def clean_decimals
      %i[regular_price sale_price].each do |v|
        self[v] = self[v].ceil unless self[v].nil?
      end
    end

    def curate_origin_of_product
       self.origin_of_product = origin_of_product || origin_of_product_was || 1
    end

    def all_dimensions_present?
      length_unit.present? && length.present? && height.present? && width.present?
    end

    def at_most_four_variants_properties
      if available_properties.size > 4
        errors.add(:available_properties, I18n.t('javascript.lux.products.properties.at_least_three'))
      end
    end
    
    def recommended_variants_ids(store = nil)
      return @recommended_variants_ids unless @recommended_variants_ids.nil?
    
      # Verificar que el producto tiene una categoría asociada
      last_category_id = category ? category.path_ids.last : nil
      return [] unless last_category_id
    
      @recommended_variants_ids = [] # Inicializamos el array de IDs de variantes recomendadas
    
      # Establecer un rango de precios inicial amplio
      initial_price_interval = price * product_range_interval / 100
      minimum_price = price - initial_price_interval
      maximum_price = price + initial_price_interval
    
      # Realizar una sola búsqueda con un rango de precios amplio
      results = []
      until results.count >= 4 || minimum_price <= 0
        search = Mkp::Variant.search do
          without :product_id, id
          with :categories_ids, last_category_id
          with :deleted, false
          with :shop_visible, true
          with :shop_deleted, false
          with :display_variant, true
          with :network, network
          with(:quantity).greater_than 0
          with(:available_on).less_than Time.zone.now
          with :price, minimum_price..maximum_price unless store&.id == 47 # Filtramos por rango de precio
          if store.present?
            with :approved_for_store_ids, store.id if store.product_approval
          end
          order_by :price, :asc # Ordenar por precio ascendente
          paginate page: 1, per_page: 50 # Obtenemos más resultados para filtrar en memoria
        end

        results = search.results
        minimum_price -= product_range_interval * price / 100
        maximum_price += product_range_interval * price / 100
      end
    
      # Ordenar los resultados en memoria por la diferencia absoluta de precio
      found_variants = results.sort_by { |variant| (variant.price - price).abs }
    
      # Tomar los primeros 4 productos únicos
      @recommended_variants_ids = found_variants.take(4).map(&:id)
    end    
       

    def product_range_interval
      Network.for(network).price_range_for_recommended_products
    end

    def variants_similarity
      #Si se actualiza por API los keys no se guardan como simbolos
      # se pisa la variante solo si el producto se crea con noproperty

      return if variants.flat_map{|v| v.properties.keys}.uniq.include? 'noproperty'

      prop_sets = variants.map(&:properties).map(&:keys).uniq
      variants_prop = prop_sets.map { | properties | properties.map(&:to_sym) }.uniq
      if variants_prop.size > 1
        errors.add(:available_properties, 'All variants of a product should be defined for the same properties')
      end
    end

    def fields_not_null?(*fields)
      !fields.map { |e| self[e] }.include? nil
    end

    def measure(value, unit)
      value = 0 if value.blank?
      value.send(unit.to_sym)
    end

    def rebuild_menu
      MenuTreeUpdaterWorker.perform_async(id)
    end

    # def soft_destroy?
    #  order_items.present?
    # end

    def must_have_one_package
      if packages.empty? || packages.all?(&:marked_for_destruction?)
        errors.add(:packages, 'must have at least one package')
      end
    end

    def update_published_pictures
      return if published_pictures.blank?

      published_pictures.each do |attr|
        idy = attr.delete(:id)
        Mkp::Attachment::Picture.find(idy).update_attribute(:product_id, id)
      end
    end

    def setting_coupon?
      return unless is_voucher?

      if voucher.setting_coupon.nil? && visible?
        errors.add(:voucher, I18n.t('lux.ajax.products.voucher.missing_setting_config'))
      end
    end

    def validate_origin_of_product_brand_energy_efficiency
      if energy_efficiency.present? && !ENERGY_EFFICIENCY_OPTIONS.include?(energy_efficiency)
        errors.add(:base, 'Eficiencia energética no es una opcion válida')
      end

      # Solo valida tiendas de BNA
      bna_stores = [41, 47]
      return unless Mkp::ShopStore.where(store_id: bna_stores).where(shop_id: shop_id).any?

      # errors.add(:base, 'Origen del producto no puede estar en blanco') if origin_of_product.blank?
      # errors.add(:base, 'Marca no puede estar en blanco') if brand.blank?
    end

    def sale_on_must_be_before_sale_until
      return unless sale_on.present? && sale_until.present?

      errors.add(:base, I18n.t('lux.products.partials.sale_until.sale_until_before_sale_on')) if sale_until <= sale_on
    end

    def set_default_transaction_type
      self.transaction_type = :reservable
    end

    def check_sale_price_without_taxes
      if sale_price.present? && sale_price_without_taxes.present? && sale_price_without_taxes > sale_price
        errors.add(:sale_price_without_taxes, I18n.t('lux.products.partials.sale_price.errors.sale_price_without_taxes_greater_than_sale_price'))
      end

      check_changes = (sale_price_changed? || sale_price_without_taxes_changed?)
      without_changed_mandatory = (sale_until.present? && sale_until > Time.now && PRICES_WITHOUT_TAXES_MANDATORY)

      if (check_changes || without_changed_mandatory) && PRICES_WITHOUT_TAXES_ENABLED
        return if PRICES_WITHOUT_TAXES_AUTO_CALCULATION && (sale_price_without_taxes == 0 || sale_price_without_taxes.nil?)
        return if sale_price_without_taxes.present? && sale_price_without_taxes > 0
        errors.add(:sale_price_without_taxes, I18n.t('lux.products.partials.sale_price.errors.sale_price_without_taxes'))
      end
    end

    def check_regular_price_without_taxes
      if regular_price.present? && regular_price_without_taxes.present? && regular_price_without_taxes > regular_price
        errors.add(:regular_price_without_taxes, I18n.t('lux.products.partials.price.errors.regular_price_without_taxes_greater_than_regular_price'))
      end

      # No se porque si ambos regular_price_change son iguales me da como changed? true asi que reverifico
      check_changes = (regular_price_changed? && regular_price_change[0] != regular_price_change[1]) || regular_price_without_taxes_changed?

      if ((check_changes) || PRICES_WITHOUT_TAXES_MANDATORY) && PRICES_WITHOUT_TAXES_ENABLED
        return if PRICES_WITHOUT_TAXES_AUTO_CALCULATION && (regular_price_without_taxes == 0 || regular_price_without_taxes.nil?)
        return if regular_price_without_taxes.present? && regular_price_without_taxes > 0
        errors.add(:regular_price_without_taxes, I18n.t('lux.products.partials.price.errors.without_taxes'))
      end
    end
  end
end
