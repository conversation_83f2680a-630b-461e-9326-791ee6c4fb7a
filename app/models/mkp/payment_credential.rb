module Mkp
  class PaymentCredential < ActiveRecord::Base
    belongs_to :store
    belongs_to :shop

    validates :name, uniqueness: { scope: [:shop_id, :store_id] }

    VALID_CONFIGURATIONS = %w[api-key integrator-id decidir-public-key]

    def self.api_key(store, shop = nil)
      api_key = find_by(name: 'api-key', store: store, shop: shop)&.value

      return api_key if api_key.present?
      find_by(name: 'api-key', store: store, shop: nil)&.value
    end

    def self.decidir_public_key(store, shop = nil)
      find_by(name: 'decidir-public-key', store: store, shop: shop)&.value
    end

    def self.integrator_id(shop, store)
      shop.payment_credentials.find_by(name: 'integrator-id', store: store)&.value
    end

  # Mkp::PaymentCredential.mercado_pago_public_key(Mkp::Store.find(1))
  # Se podria tomar directamente de la configuracion de Payments Genosha
  def self.mercadopago_public_key(store)
    store.payment_credentials.find_by(name: 'mercadopago-public-key')&.value
  end

  end
end
