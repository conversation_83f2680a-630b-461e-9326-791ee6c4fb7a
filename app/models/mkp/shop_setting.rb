module Mkp
  class ShopSetting < ActiveRecord::Base
    serialize :tax_rates, Hash
    belongs_to :shop, class_name: 'Mkp::Shop', foreign_key: :mkp_shop_id

    store :commercial_agreement,
          accessors: [:monthly_fee, :sale_commission]

    store :wide_discount,
          accessors: [:discount_percent,
                      :discount_ends_at,
                      :discount_is_over_product_sale,
                      :discount_starts_at]

    store :notification_policy,
          accessors: [:daily_digest, :webhooks]

    store :delivery_channel_policy,
          accessors: [:own_labels, :on_demand_labels_config]

    store :business_number,
          accessors: [:visa, :cabal, :mastercard, :naranja, :american]
    
    store :contact_email,
          accessors: [:comercial, :logistica, :liquidaciones]
    
    store :contact_number,
          accessors: [:comercial, :logistica, :liquidaciones]

    validates :sale_commission, numericality: { allow_blank: true }
    validates :monthly_fee, numericality: { allow_blank: true }
    validate :notify_purchases_should_be_emails
    validates :company_name, length: { maximum: 50 }, allow_nil: true
    validates :account_executive, length: { maximum: 50 }, allow_nil: true
    validate :contact_email_should_be_emails
    validate :contact_number_should_be_numbers

    def notify_purchases=(value)
      if value.present? && /\s*(\S+@\S+){1}(\s*,\s*\S+@\S+)*\s*/ !~ value
        value.strip!.gsub!(/\s*,\s*/,',')
      end
      super(value)
    end

    def notify_purchases_emails
      n = notify_purchases
      return nil if n.blank?
      (n.split(', ') - shop.merchants.map(&:email)).join(', ')
    end

    def set_default_taxes
      TaxesManager::DEFAULT_TAXES.each do |state, value|
        self.tax_rates[state] = { rate: value, apply_to_shipping: false }
      end
      save
    end

    def tax_rates
      super || {}
    end

    def discount_percent
      wide_discount[:discount_percent].to_i
    end

    def discount_ends_at
      wide_discount[:discount_ends_at] && Date.parse(wide_discount[:discount_ends_at])
    end

    def discount_starts_at
      wide_discount[:discount_starts_at] && Date.parse(wide_discount[:discount_starts_at])
    end

    def discount_applying_now?
      discount_ends_at && discount_starts_at && Date.today.between?(discount_starts_at, discount_ends_at)
    end

    def label_gateways
      return [] unless on_demand_labels_config.present?

      on_demand_labels_config.map do |gateway_name, gateway_config|
        "Gateways::Labels::#{gateway_name.to_s.camelize}".constantize.new(gateway_config)
      end
    end

    def destroy_oca_epak
      update_attributes(
          delivery_channel_policy: {
              on_demand_labels_config: {}
          }
      )
    end

    def oca_epak(operation, origin, contact_name)
      config = Gateways::Labels::OcaEpak.new.config_hash_for_shop(Gateways::Labels::OcaEpak::OPERATION_SERVICE[operation], origin, contact_name)
      if config.present?
        update_attributes(
            delivery_channel_policy: {
                on_demand_labels_config: {
                    oca_epak: config
                }
            }
        )
        on_demand_labels_config
      end
    end

    protected
    def notify_purchases_should_be_emails
      if notify_purchases.present? && /\s*(\S+@\S+){1}(\s*,\s*\S+@\S+)*\s*/ !~ notify_purchases
        errors.add(:notify_purchases, 'should be a comma separated list of emails.')
      end
    end

    def contact_email_should_be_emails
      contact_email.each do |k,v|
        if v.present? && v.match(/\A([^@\s]+)@((?:[-a-z0-9]+\.)+[ a-z]{2,})\z/i).nil?
          errors.add(:contact_email, 'should be an email.')
        end
      end
    end

    def contact_number_should_be_numbers
      contact_number.each do |k,v|
        if v.present? && v.match(/[1-9][0-9]+/).nil?
          errors.add(:contact_number, 'should be a valid number.')
        end
      end
    end
  end
end
