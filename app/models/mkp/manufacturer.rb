module Mkp
  class Manufacturer < ActiveRecord::Base
    extend FriendlyId
    include Concerns::HasSlugOnRedis
    include Concerns::GenerateManufacturerSku

    friendly_id :name, use: [:slugged, :history]

    has_many :brand_profiles, class_name: 'Social::Profile::Brand'
    has_many :brands, through: :brand_profiles, source: :user
    has_many :products, class_name: 'Product'

    validates :name, :slug, presence:true

    validates_uniqueness_of :name, :slug

    after_initialize :generate_token

    def self.seo_network_method_names
      Network.all_active.map do |network|
        ["description_#{network.downcase}", "product_description_#{network.downcase}"]
      end.flatten.map(&:to_sym)
    end

    store :description,
          accessors: seo_network_method_names

    has_attached_file :logo,
      path: 'manufacturers/:attachment/:id/:friendly_name_:style.:extension',
      s3_host_alias: 's3-us-west-2.amazonaws.com/gpcdn-mkp',
      url: ':s3_alias_url',
      bucket: 'gpcdn-mkp',
      styles: ->(m){ m.instance.class.styles },
      processors: [:thumbnail, :paperclip_optimizer],
      convert_options: { all: '-auto-orient -strip' },
      default_style: :lt,
      s3_protocol: 'https',
      s3_headers: { 'Cache-Control' => 'max-age=315576000', 'Expires' => 10.years.from_now.httpdate }

    validates_attachment :logo,
      # presence: true, # Cavi: I comment this line for the migration
      content_type: { content_type: [ 'image/bmp',
                                      'image/gif',
                                      'image/jpeg',
                                      'image/png' ] },
      size: { :in => 0..10.megabytes }

    default_scope { order('name ASC') }

    before_post_process :check_picture_file

    searchable do
      text :name
    end

    def self.styles
      {
        st: '100x100#', # small-thumb - Cropped
        lt: '200x200#', # large-thumb - Cropped
        xl: '960x960>', # xtra-large - Si la imagen excede alguno de los dos parametros la achica hasta que cumpla con esto
      }
    end

    def get_description(network)
      send("description_#{network.downcase}")
    end

    def get_product_description(network)
      send("product_description_#{network.downcase}")
    end

    def generate_token
      unless attribute_present?(:token)
        self.token = Digest::MD5.hexdigest( rand.to_s + Time.now.to_i.to_s )
      end
    end

    def check_picture_file
      valid?
      errors.blank?
    end

    def assign_logo_from_url(url)
      if url =~ /^#{URI::regexp}$/
        self.logo = URI.parse(url)
      end
    end

    def related_brand(network)
      Brand.joins(:profile)
           .where('brand_profiles.manufacturer_id = ?', id)
           .where('network = ?', network)
           .first
    end

    def self.alikes
      require 'text'

      names = Mkp::Manufacturer.pluck(:name)
      white = Text::WhiteSimilarity.new

      names.each do |n|
        alikes = names.dup
        alikes.slice!(names.index(n))
        alikes.map! do |_n|
          {
            name: _n,
            distance: (white.similarity(n, _n) * 10).ceil
          }
        end
        alikes.sort! { |x, y| y[:distance] <=> x[:distance] }
        alikes.map! { |a| a[:name] }
        $stdin.gets.chomp
      end
    end

  end
end
