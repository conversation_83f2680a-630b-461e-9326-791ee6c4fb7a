module Mkp
  class Coupon < ActiveRecord::Base
    include Concerns::SoftDestroy
    include Concerns::Avenidable

  # Coupons are straight forward. Picture a coupon you have in a grocery store. The only big difference in the grocery store you can have 30 coupon for different items you buy.
  # eFor Trumaker you can only have one couple for an entire order. This is pretty standard in the ecommerce world.

  # The method that is most important:
  #
  # qualified?
  #
  # This method does 4 things:
  #
  # 1) it determines if the coupon is not deleted
  # 2) it determines if the coupon has enough units
  # 3) it determines if the items in your cart cost enough to reach the minimum qualifing amount needed for teh coupon to work.
  # 4) it determines if the coupon is "eligible?" (eligible? is a method)
  #
  # The eligible? method changes functionality depending on what type of coupon is created.
  # => at the most basic level it determine if the date of the order is greater than starts_at and less than expires_at
  #
  # For first_purchase_xxxxx coupons eligible? also ensures the order that this is being applied to is the first item you have ever purchased.
  #
    COUPON_POLICIES = ['percent', 'value']

    validates_presence_of :amount, if: :value_policy?
    validates_format_of   :code, with: /\A[A-Za-z0-9_-]*\z/, on: :create
    validates_presence_of :expires_at
    validates_presence_of :minimum_value
    validates_presence_of :percent, if: :percent_policy?
    validates_presence_of :starts_at
    validates_presence_of :type
    validates :amount,  numericality: { greater_than: 0 }, if: :value_policy?
    validates :percent, numericality: { greater_than: 0, less_than: 101 }, if: :percent_policy?
    validate :validate_the_end_date_is_not_less_than_the_start_date
    validates :code, uniqueness: { case_sensitive: true }, presence: true

    has_many :gift_cards
    has_many :vouchers, through: :gift_cards
    has_many :order_items, through: :gift_cards

    attr_accessor :message

    serialize :restrictions, Hash

    delegate :applies_to?, :can_be_applied_by?, :what_does_it_apply_to?, to: :restrictions

    after_create :init_still_available
    after_create :init_amount_available

    def self.random_coupon
      loop do
        random_coupon = SecureRandom.urlsafe_base64(10, false)
        break random_coupon unless Mkp::Coupon.exists?(code: random_coupon)
      end
    end

    def self.non_deleted_coupons_with_code_in_network(code, network)
      coupons = []
      begin
        coupons = Coupon.where(code: code, network: network, deleted_at: nil)
        raise "More than one coupon on the same network with same code and non flagged as deleted" if coupons.length > 1
      rescue Exception => msg
        logger.error msg
      end
      coupons
    end

    def self.non_deleted_coupons_with_code(code)
      coupons = []
      begin
        coupons = Coupon.where(code: code, deleted_at: nil)
        raise "More than one coupon with same code and non flagged as deleted" if coupons.length > 1
      rescue Exception => msg
        logger.error msg
      end
      coupons
    end

    def self.get_elegible_by_code_and_network?(code, network, price_to_cover)
      coupons = Coupon.non_deleted_coupons_with_code_in_network(code, network)
      Coupon.which_coupon(coupons, price_to_cover)
    end

    def self.get_elegible_by_code?(code, price_to_cover)
      coupons = Coupon.non_deleted_coupons_with_code(code)
      Coupon.which_coupon(coupons, price_to_cover)
    end

    def self.get_elegible_by_id?(id, price_to_cover)
      if coupon = Coupon.find(id)
        coupon if coupon.qualified?(price_to_cover, nil, Time.now)
      end
    end

    def self.which_coupon(coupons, price_to_cover)
      return false unless coupons.present?
      coupon = coupons.last
      coupon if coupon.qualified?(price_to_cover, nil, Time.now)
    end

    def restrictions
      CouponRestrictions.new(super)
    end

    def value(total_price, order)
      at ||= Time.now
      (price_above_minimum?(total_price) && eligible?(at)) ? coupon_amount(total_price) : 0.0
    end

    def qualified?(total_price, order, at = nil)
      valid_status? && has_available? && price_above_minimum?(total_price) && eligible?(at)
    end

    def has_available?
      res = still_available ? still_available > 0  : true
      raise UserLimitedReachedError unless res
      res
    end

    def valid_status?
      is_deleted = deleted?
      (@message ||= []) << I18n.t('coupons.errors.deleted') if is_deleted
      ! is_deleted
    end

    def has_count?
      still_available.present?
    end

    def decrease_properties_to_order!(order = nil)
      if total_available >= 1
        decrease_availability if has_count?
      else
        decrease_amount(order)
      end
    end

    def decrease_availability
      coupons_available = (still_available > 0) ? still_available - 1 : 0
      update_attributes(still_available: coupons_available)
    end

    def decrease_amount(order)
      _amount = amount_available - order.total_without_discount
      update_attributes(amount_available: _amount)
      decrease_availability if _amount <= 0
    end

    # WIP MODO Pending orders/payments
    def increase_properties_to_order!(order = nil)
      if total_available > 0
        increase_availability if has_count?
      else
        increase_amount(order)
      end
    end

    def increase_availability
      coupons_available = still_available + 1
      update_attributes(still_available: coupons_available)
    end

    def increase_amount(order)
      _amount = amount_available + order.total_without_discount
      update_attributes(amount_available: _amount)
      increase_availability if order.total_without_discount > amount_available
    end
    # WIP MODO Pending orders/payments

    def eligible?(at = nil)
      at ||= Time.now
      res = time_active?(at)
      res
    end

    def time_active?(at = Time.now)
      if starts_at > at
        raise CouponNotActiveError
      elsif expires_at < at
        raise CouponExpiredError
      else
        true
      end
    end

    def status
      deleted? ? "deleted" : ("active" if time_active?)
    rescue CouponNotActiveError
      "inactive"
    rescue CouponExpiredError
      "expired"
    end

    def display_starts_at(format = :us_date)
      starts_at ? starts_at.strftime("%d %b %y") : 'N/A'
    end

    def display_expires_at(format = :us_date)
      expires_at ? expires_at.strftime("%d %b %y") : 'N/A'
    end

    def display_value(value)
     "#{value} #{currency}"
    end

    def display_percent
     "#{percent} %"
    end

    def coupon_types
      COUPON_TYPES
    end

    def used
      total_available - still_available if has_count?
    end

    def value_policy?
      policy == 'value'
    end

    def percent_policy?
      policy == 'percent'
    end

    def coupon_amount(total_price)
      policy_klass = "::Mkp::Coupon::#{policy.camelcase}Policy".constantize
      policy_klass.coupon_amount(self, total_price)
    end

    def calculate_suborder(suborder)
      ((suborder.total_without_shipment / suborder.order.total_without_shipment ) * suborder.order.coupon_discount).round(2)
    end

    private

    def price_above_minimum?(total_price)
      res = (total_price >= minimum_value) || total_price.nil?
      raise BelowAmountLimitError.new("Price is below minimum") unless res
      res
    end

    def init_still_available
      update_attribute(:still_available, total_available)
    end

    def init_amount_available
      update_attribute(:amount_available, amount)
    end

    def validate_the_end_date_is_not_less_than_the_start_date
      return errors.add(:expires_at, "La fecha de expiracion no puede ser menor a la fecha de inicio.") if self.expires_at < self.starts_at
    end

    class Shop < Coupon
      has_many :suborders, class_name: '::Mkp::Suborder',
                           foreign_key: :coupon_id
      belongs_to :shop, class_name: '::Mkp::Shop'
      validates :shop, presence: true

      def soft_destroy?
        suborders.present? ? true : false
      end

      def currency
        shop ? Currency.find_by_network(shop.network.to_s).symbol : ''
      end
    end

    class Network < Coupon
      has_many :orders, class_name: '::Mkp::Order',
                        foreign_key: :coupon_id
      has_many :carts, class_name: '::Mkp::Cart',
                       foreign_key: :coupon_id,
                       dependent: :nullify
      validates :network, presence: true

      def soft_destroy?
        orders.present? ? true : false
      end
    end

    module PercentPolicy
      module_function

      def coupon_amount(coupon, amount_to_apply)
        coupon_amount = (amount_to_apply * coupon.percent.to_f / 100.0).round
        if coupon.discount_limit.zero? || coupon_amount <= coupon.discount_limit
          coupon_amount
        else
          coupon.discount_limit
        end
      end
    end

    module ValuePolicy
      module_function

      def coupon_amount(coupon, amount_to_apply)
        amount_is_applicable = (amount_to_apply >= coupon.minimum_value)
        if amount_is_applicable
          (amount_to_apply > coupon.amount_available) ? coupon.amount_available : amount_to_apply
        else
          0.0
        end
      end
    end

    class InvalidCouponError           < StandardError; end
    class InvalidNetworkError          < StandardError; end
    class OutOfStockError              < StandardError; end
    class CouponExpiredError           < StandardError; end
    class CouponNotActiveError         < StandardError; end
    class UserLimitedReachedError      < StandardError; end
    class InvalidShopError             < StandardError; end
    class BelowAmountLimitError        < StandardError; end
    class InvalidProductError          < StandardError
      attr_reader :data
      def initialize(data)
        @data = data
      end
    end
    class InvalidCategoryError         < StandardError
      attr_reader :data
      def initialize(data)
        @data = data
      end
    end
    class InvalidManufacturerError     < StandardError
      attr_reader :data
      def initialize(data)
        @data = data
      end
    end
    class InvalidShopError     < StandardError
      attr_reader :data
      def initialize(data)
        @data = data
      end
    end
    class UserExcludedError            < StandardError; end
    class CouponNotOnsaleError         < StandardError; end
    class PromotionNotAllowCouponError < StandardError; end
  end
end
