module Mkp
  class Document < ActiveRecord::Base
    self.table_name = :mkp_documents

    has_attached_file :pdf_file

    DOCUMENT_TYPES = [ 'Factura', 'Comprobante de Transferencia' ]

    validates_attachment :pdf_file, presence: true
    validates_attachment :pdf_file, content_type: { content_type: %w[application/pdf] },
                         message: 'El archivo debe ser formato PDF'
    validates_attachment :pdf_file, size: { in: 1..2.megabytes },
                         message: 'El archivo no puede sobrepasar los 2Mb'

    belongs_to :suborder , foreign_key: :suborder_id

    after_initialize :generate_token
    before_validation :maximum_records

    def pdf_file_description
      "#{pdf_file_type} #{pdf_file_file_name}"
    end

    def self.document_types
      DOCUMENT_TYPES
    end

    private

    def generate_token
      unless attribute_present?('token')
        self.token = Digest::MD5.hexdigest("#{rand}#{Time.now.to_i}")
      end
    end

    protected

    def maximum_records
      unless Mkp::Document.where(suborder_id: self.suborder.id).count < 6
        errors.add(:mkp_document, 'Supero la capacidad de subida de archivos permitidos')
      end
    end

  end
end
