class RenaperAnswer < ActiveRecord::Base

  scope :data_entry, -> { where(question_type: 'DataEntry') }
  scope :multiple_choice, -> { where(question_type: 'MultipleChoice') }
  scope :unanswered, -> { where(answered: false) }


  def as_renaper_hash
    Renaper::DataEntryAdapter.new(self).to_hash
  end

  def is_correct?(answer)
    correct_answer.to_s == answer.to_s
  end

  def generate_token
    self.token = Digest::MD5.hexdigest("#{rand}#{Time.now.to_i}")[0..29]
  end

  def correct!
    self.answered = true
    self.generate_token
    self.save
  end
end
