module WhitelistStrategies
  class FirstGeneralPurchaseStrategy < ValidationStrategy

    def self.authorized?(document_type, document_number, store)
      filter = {}
      filter.merge!(document_type: document_type) unless document_type.nil?
      orders = Mkp::Order.joins(:payments).where(store: store, mkp_payments: { document_number: document_number, status: 'collected' }.merge(filter))
      orders.empty?
    end

    def self.build_unauthorized_data(number, store, cart)
      message = 'Usted ya realizó una compra en la tienda.'
      products = []

      {:products => products, :message => message}
    end

    def self.authorized_installment?(document_type, document_number, store, installment)
      installment.valid_for == 'first_purchase' && authorized?(document_type, document_number, store)
    end
  end
end
