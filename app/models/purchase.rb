class Purchase < ActiveRecord::Base
  extend ::Concerns::WiserPolymorphicBelongsTo

  has_many :payments, as: :sale_item, dependent: :destroy, class_name: "Mkp::Payment"
  has_many :direct_shipments, as: :sale_item, dependent: :destroy, class_name: "Mkp::Shipment"
  has_many :suborders, class_name: 'Mkp::Suborder'
  has_many :shipments, ->{ uniq }, through: :suborders, class_name: "Mkp::Shipment"
  has_many :items, ->{ uniq }, through: :suborders, class_name: "Mkp::OrderItem"
  has_many :invoice_items, as: :suborder
  belongs_to :store, foreign_key: :store_id, class_name: "Mkp::Store"
  belongs_to :customer, polymorphic: true

  listable_through :sale_items, :listables

  wiser_polymorphic_belongs_to :buyer,
    'Mkp::Customer',
    'customer_id',
    'customer_type',
    table_name

  after_create :update_miles

  scope :listables, -> { select_as(store_id: 'store_id', customer_id: 'customer_id', 'customer_type collate utf8_unicode_ci' => :customer_type, 'id_cobis collate utf8_unicode_ci' => :id_cobis) }
  scope :with_payment_status, ->(payment_status) { joins(:mkp_payments).where(mkp_payments: { status: payment_status }) }

  def balance_due?
    false
  end

  def coupon_discount
    0
  end

  def customer_uuid
    customer.present? ? customer.uuid : id_cobis
  end

  def customer_full_name
    if customer.present?
      customer.full_name
    else
      customer = Mkp::Customer.not_deleted.find_by(uuid: id_cobis)
      customer.present? ? customer.full_name : 'N/A'
    end
  end

  def is_cancellable?
    false # por ahora ningún purchase es cancelable, puede cambiar según requerimientos de macro
    # return false unless gateway.present?
    # return false unless ['Sube'].include?(gateway) # un array para poder incluir más en la comparación
    # return false unless payment.present?
    # !payment.cancelled?
  end

  def is_refundable?
    false
  end

  def items_count
    1
  end

  def payment
    payments.last
  end

  def sp_subtotal_points
    points
  end

  def title
    gateway == 'Aerolineas' ? 'Millas' : gateway
  end

  def total
    amount || 0
  end

  def total_points
    points
  end

  def total_without_points
    0
  end

  def total_points_price
    points * point_equivalent
  end

  def consumed_stock
    return miles if gateway == 'Aerolineas'
    total_points_price
  end

  def update_miles
    self.update_attributes(miles: (points / store.aerolineas_argentinas_credential.points_equivalent).truncate) if gateway == 'Aerolineas' && points.present?
  end

  def shop
    Mkp::Shop.where(slug: gateway.downcase).first
  end

  def order
    self
  end

  def order_id
    id
  end
end

