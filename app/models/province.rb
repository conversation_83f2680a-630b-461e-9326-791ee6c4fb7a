class Province < ActiveRecord::Base
  has_many :cities, class_name: 'City'

  def self.get_ids(state, city)
    province = Province.find_by(name: state) || Province.first
    province_id = province.id if province.present?

    city = City.where(name: city, province_id: province_id).first || City.first
    city_id = city.id if city.present?

    @result = {province_id: province_id, city_id: city_id}
  end
end
