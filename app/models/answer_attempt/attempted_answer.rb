module AnswerAttempt
  class AttemptedAnswer < ActiveRecord::Base

    include ::Concerns::SoftDestroy

    scope :not_deleted, -> { where(['attempted_answers.deleted_at IS ?', nil]) }
    default_scope { not_deleted }

    scope :search_query, ->(query) do
      query = query.to_s
      return if query.blank?
      where("attempted_answers.doc_number like ?", "%#{query}%")
    end

    def self.types
      %w[Equifax Renaper].to_h {|type| [type, "AnswerAttempt::#{type}"]}
    end
  end
end