class CustomerReservationPurchases < ActiveRecord::Base
  validates :customer_dni, presence: true
  validates :product_id, presence: true
  validates :store_id, presence: true
  validates :mkp_order_id, presence: true

  validate :validate_lended_amount

  belongs_to :store, class_name: 'Mkp::Store'
  belongs_to :product, class_name: 'Mkp::Product'
  belongs_to :mkp_order, class_name: 'Mkp::Order', foreign_key: :mkp_order_id

  scope :search_query, ->(query) do
    query = query.to_s
    return if query.blank?
    where("customer_reservation_purchases.customer_dni like ?", "%#{query}%")
  end

  def force_load_product
    # TODO cargar la asociacion unscoped
    begin
     Mkp::Product.unscoped.find(product_id)
    rescue
      nil
    end
  end

  private

  def validate_lended_amount
    errors.add(:maximum_lended_amount, message: "La cantidad a aprobar no puede ser mayor al monto máximo") if maximum_lended_amount < approved_lended_amount
    errors.add(:maximum_lended_amount, message: "La cantidad no puede ser menor a 0") if 0 > approved_lended_amount
  end
end
