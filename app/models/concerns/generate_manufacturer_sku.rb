module Concerns
  module GenerateManufacturerSku
    extend ActiveSupport::Concern

    MIN_SKU_LENGTH = 3
    MAX_SKU_LENGTH = 4

    included do
      before_create :generate_sku
    end

    protected

    def generate_sku
      @flatten_name = self.slug.gsub(/\W+/, '').upcase

      sku_id = @flatten_name

      if @flatten_name.length < MIN_SKU_LENGTH
        sku_id = @flatten_name
      else
        sku_id = get_sku_id(MIN_SKU_LENGTH)
        sku_id ||= "CHANGE_ME"
      end

      self.sku_id = sku_id
    end

    private

    def get_sku_id(current_length)
      possible_sku = @flatten_name[0...current_length]
      return possible_sku unless sku_exist?(possible_sku)

      base = @flatten_name[0...current_length-1]

      @flatten_name.slice(current_length..-1).chars.each do |char|
        possible_sku = base + char
        return possible_sku unless sku_exist?(possible_sku)
      end

      current_length += 1
      return if current_length > MAX_SKU_LENGTH

      get_sku_id(current_length)
    end

    def sku_exist?(possible_sku)
      @current_skus ||= self.class.pluck(:sku_id)
      @current_skus.include?(possible_sku)
    end

  end
end
