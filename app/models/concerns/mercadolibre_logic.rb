module Concerns
  module MercadolibreLogic
    extend ActiveSupport::Concern

    AVAILABLE_SCOPES = [
      :read,
      :write,
      :offline_access
    ].freeze

    WEBHOOKS_TOPICS = [
      'items'
    ].freeze

    AVAILABLE_STATUSES = [
      'active',
      'paused',
      'closed'
    ].freeze

    ENDPOINT_URL = 'https://api.mercadolibre.com'.freeze

    included do
      store :data
    end

    def authorized?
      access_token.present? && refresh_token.present?
    end

    def account_info
      set_client

      @account_info ||= @client.get_my_user
    end

    def get_all_my_item_ids(filter = {})
      set_client

      filter.tap do |hash|
        if need_custom_configuration? && configuration_complete?
          hash[:official_store_id] = official_store_id
        end
      end

      @item_ids ||= @client.get_all_my_item_ids(filter)
    end

    def get_item(item_id)
      set_client(need_to_refresh: false)

      item = get_naked_item(item_id)
      item.send('descriptions=', get_item_descriptions(item_id))
      item.send('category_id=', get_category(item.category_id))
      item
    end

    def get_naked_item(item_id)
      set_client(need_to_refresh: false)

      @client.get_item(item_id)
    end

    def get_item_descriptions(item_id)
      set_client(need_to_refresh: false)

      item_description = @client.get_item_description(item_id)
      [item_description] unless item_description.blank?
    end

    def get_category(category_id)
      set_client(need_to_refresh: false)

      @client.get_category(category_id)
    end

    def get_official_stores(user_id = nil)
      set_client
      brands = if user_id.blank?
        @client.get_my_brands
      else
        @client.get_user_brands(user_id)
      end
      brands.map do |brand|
        if brand.status == 'active'
          {
            id: brand.official_store_id,
            fantasy_name: brand.fantasy_name,
            name: brand.name,
            categories_ids: brand.categories_ids
          }
        end
      end.compact
    end

    def remote_sync(product, integration_object)
      set_client

      external_product_id = integration_object.external_id

      attributes = if (product_stock = product.total_stock) > 0
        if integration_object.single
          { available_quantity: product_stock }
        else
          variants_stock(product)
        end
      else
        { status: 'paused' }
      end

      @client.update_item_fields(external_product_id, attributes)
    end

    def need_to_process?(item_id)
      external_product = get_external_product(item_id)

      !!external_product && AVAILABLE_STATUSES.include?(external_product.status)
    end

    def process_notification(item_id)
      external_product = get_external_product(item_id)

      if external_product.present?
        begin
          local_object = integrated_product(external_product, true)

          if local_object.present? && simple_type == 'gpmercadolibre'
            sync_product(external_product)
          else
            if local_object.present?
              sync_product(external_product)
            elsif integrated_parent_exist?(external_product)
              update_external_references(external_product)
              sync_product(external_product)
            else
              if external_product.status == 'active' && external_product.pictures.present?
                create_integrated_product(external_product)
              end
            end
          end
        rescue ActiveRecord::RecordInvalid => e
          handle_exception(e, external_product.to_hash(true))
        end
      end
    end

    def set_client(need_to_refresh: true)
      @client ||= ::Mercadolibre::Api.new({
        access_token: access_token,
        app_key:      shop.mercadolibre_app_id,
        app_secret:   shop.mercadolibre_secret_key,
        callback_url: shop.mercadolibre_redirect_uri,
        site:         MELI["site_country"]
      })

      update_token if need_to_refresh && stale_token?
    end

    def get_req(action, params = {}, headers = {})
      headers[:Authorization] = "Bearer #{access_token}" unless headers.key? :Authorization
      url = "#{ENDPOINT_URL}#{action}"
      parse_response(RestClient.get(url, { params: params }.merge(headers)))
    rescue StandardError => e
      parse_response(e.response)
    end

    private

    # Checks if the token is valid between now and the next half hour.
    def stale_token?
      return false unless expires_at.present?
      expires_at <= (Time.now.to_datetime.utc - 30.minutes)
    end

    def update_token
      return false unless refresh_token.present?

      response = @client.update_token(refresh_token)

      raise if response.respond_to?(:error) || response.access_token.blank? || response.refresh_token.blank?

      self.access_token = response.access_token
      self.refresh_token = response.refresh_token
      self.expires_at = response.expired_at
      self.save
    rescue => exception
      handle_exception(exception, response)
    end

    def variants_stock(product)
      {
        variations: product.variants.map do |variant|
          {
            id: external_variant_id(variant).to_i,
            available_quantity: variant.quantity
          }
        end
      }
    end

    def external_variant_id(variant)
      variant.external_objects.first.external_id
    end

    def integrated_parent(external_product)
      objects.where(
        integrable_type: Mkp::Product.name,
        external_id: external_product.external_parent_id
      ).first
    end

    def integrated_parent_exist?(external_product)
      integrated_parent(external_product).present?
    end

    def update_external_references(external_product)
      integrated_parent(external_product).update_attributes(external_id: external_product.external_id)
      product = integrated_product(external_product).integrable
      integration_revert_soft_destroy(product)
      update_external_variants_references(external_product) unless external_product.without_variants?
    end

    def sync_product(external_product)
      if external_product.status == 'closed'
        return unless (product = integrated_product(external_product).integrable).present?
        integration_soft_destroy(product)
      else
        update_related_product(external_product)
        sync_variants(external_product)
      end
    end

    def integration_soft_destroy(product)
      deleted_at = Time.now - 1.day
      product.variants.each { |variant| variant.update_attributes(deleted_at: deleted_at) }
      product.update_attributes(deleted_at: deleted_at)
    end

    def integration_revert_soft_destroy(product)
      product.update_attributes(deleted_at: nil)

      Mkp::Variant.unscoped.where(product_id: product.id).each do |variant|
        variant.update_attributes(deleted_at: nil)
      end
    end

    def update_external_variants_references(external_product)
      variants = integrated_product(external_product).integrable.variants.dup

      external_product.variants.each do |external_variant|
        variant = variants.detect { |var| var.sku == external_variant.sku }
        variant ||= match_variant_by_properties(variants, external_variant)

        next unless variant.present?

        variants.delete(variant)

        variant.external_object(simple_type).update_attributes(external_id: external_variant.external_id)
      end

      variants.map(&:destroy)
    end

    def match_variant_by_properties(variants, external_variant)
      variants.detect { |variant| variant.properties == external_variant.properties }
    end

    def get_external_product(item_id)
      @external_products ||= {}
      @external_products[item_id] ||= integration_product_model.find([item_id]).first
    end

    def parse_response(response)
      {
        headers: response.headers,
        body: (JSON.parse(response.body) rescue response.body),
        status_code: response.code
      }
    end

  end
end
