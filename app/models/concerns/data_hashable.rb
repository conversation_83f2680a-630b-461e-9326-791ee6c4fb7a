module Concerns::DataHashable
  extend ActiveSupport::Concern

  included do
    before_save :serialize_data
  end

  def data_to_simple_hash(column = :data)
    hash = {}
    return hash if self.send(column).nil?
    self.send(column).each do |root|
      root[1].each { |key| hash[key[:name]] = key[:value] }
    end

    hash
  rescue StandardError => e
    # Cuando se recarga la pagina de editar producto.. por algún motivo útopico
    # se cambia el formato del serialize del campo data..
    # Realizo un parseo distinto para estos caso.
    hash = {}
    self.send(column).each do |root|
      hash[root[1]["name"]] = root[1]["value"]
    end
    hash
  end

  def serialize_data
    ["data", "data_shipment"].each do |field|
      next unless self.send("#{field}_changed?")
      next if self.send(field).nil?
      hash = {}
      self.send(field).each do |d|
        d[1] = [d[1]] unless d[1].kind_of?(Array)
        hash[d[1].first[:name]] = []
        hash[d[1].first[:name]][0] = {name: d[1].first[:name], value: d[1].first[:value]}
      end
      self.send("#{field}=", hash)
    end
  end
end
