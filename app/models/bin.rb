class Bin < ActiveRecord::Base
  belongs_to :store, class_name: "Mkp::Store"
  belongs_to :bank, class_name: "Mkp::Bank"
  has_many :installments, dependent: :delete_all
  enum brand: Mkp::PaymentPromotion.brands

  before_save :check_store_id

  serialize :installments_availables, Array

  validates :number, numericality: true, uniqueness: {scope: :store_id,
    message: I18n.t('pioneer.bin.number')}

  scope :unexpired, -> (network = "AR") { where("starts_at <= ? AND ends_at >= ? AND network = ?", Date.today, Date.today, network)  }

  accepts_nested_attributes_for :installments, reject_if: :all_blank, allow_destroy: true

  def installments_lte(max_installments)
    installments.select {|each| each.number <= max_installments}
  end

  # NOTE: Modify conditional of max_installments.present? to ignore when installments provide from MODO
  def available_installments_filtered_by(max_installments, product)
    result = installments
    result = result.select {|each| each.number <= max_installments} if max_installments.present?
    result = result.select {|each| each.category_id.nil? || each.category_id == product.category.id || product.category.path.collect(&:id).include?(each.category_id)}
    result = result.select {|each| each.product_id.nil? || each.product_id == product.id}
    result
  end

  def installments_availables
    installments
  end

  def available_installments_for_checkout(cart, doc_type, doc_number)
    #if has_gateway_installment?(doc_number)
    #  result = store.strategy_gateway_installments.installments_from_bin(self)
    #else
    #  result = installments.where(gateway: [nil,'']).order(:number)
    #end
    result = installments.order(:number)

    cart.items.each do |item|
      result = result.select {|installment| installment.category_id.nil? || installment.category_id == item.product.category.id || item.product.category.path.collect(&:id).include?(installment.category_id)}
      result = result.select {|installment| installment.product_id.nil? || installment.product_id == item.product.id}
    end

    result = result.select {|installment| installment.purchase_total_limit.nil? || installment.purchase_total_limit > cart.total}
    result = result.select {|installment| authorized_installment?(installment, doc_type, doc_number)}

    if !debit_brand?
      unless cart.items.map(&:shop).any? {|each| each.payment_program_for(cart.store).nil?}
        installments_by_payment_programm = (cart.items.map(&:shop).map {|each| each.payment_program_for(cart.store)}).compact.uniq.map(&:installments)
      end
    end

    if installments_by_payment_programm.present?
      final_result = installments_by_payment_programm.flatten
    else
      final_result = []
    end

    result.each do |installment|
      unless final_result.any? {|each| each.number == installment.number }
        final_result << installment
      end
    end

    final_result
  end

  def debit_brand?
    ["cabal_debito", "visa_debito", "mastercard_debito", "maestro"].include? brand
  end

  private

  def check_store_id
    self.store_id = nil if self.store_id.blank?
  end

  def authorized_installment?(installment, doc_type, doc_number)
    return true unless installment.valid_for.present?
    return true unless store.whitelist_configuration.present? && store.whitelist_configuration.active
    return false unless doc_type.present? && doc_number.present?

    result = false
    if store.whitelist_configuration.user_strategy_class.exists?(doc_number, store)
      result = store.whitelist_configuration.validation_strategy_class.authorized_installment?(doc_type, doc_number, store, installment)
    end

    result

  end


  # def first_purchase(cart, doc_type, doc_number)
  #   return true unless store.whitelist_configuration.present? && store.whitelist_configuration.active
  #   return false unless doc_type.present? && doc_number.present?
  #
  #   result = false
  #   if store.whitelist_configuration.user_strategy_class.exists?(doc_number, store)
  #     result = store.whitelist_configuration.validation_strategy_class.authorized?(doc_type, doc_number, store)
  #   end
  #
  #   result
  # end
end
