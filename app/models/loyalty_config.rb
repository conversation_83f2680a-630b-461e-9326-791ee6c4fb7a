class LoyaltyConfig < ActiveRecord::Base
  validates :name, :value, presence: true
  validates :name, uniqueness: true

  def self.active
    ActiveRecord::Type::Boolean.new.type_cast_from_user(find_by(slug: 'active').value)
  end

  def self.conversion
    [find_by(slug: 'conversion').value.to_f, 0].max
  end

  def self.max_amount_per_month
    find_by(slug: 'max_amount_per_month').value.to_i
  end
end
