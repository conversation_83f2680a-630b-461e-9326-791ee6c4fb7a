module Ochenta
  module Cart
    class Warranties
      attr_reader :mapper_instance, :available_categories

      def initialize(args = {})
        @mapper_instance = args[:mapper_instance]
        @available_categories = args[:available_categories]
        @variants_ids = @available_categories.map { |category| mapper_instance.fetch(category) }.flatten.uniq
      end

      def detector
        return OpenStruct.new({ success?: false, payload: {} }) unless detected?

        variant_id = @variants_ids.first
        category_ids = mapper_instance.mapper.map { |k, v| k if v.include?(variant_id) }.compact
        OpenStruct.new(
          {
            success?: true, payload: { variant_id: variant_id, category_ids: category_ids & available_categories }
          }
        )
      end

      private

      def detected?
        @variants_ids.one?
      end
    end
  end
end
