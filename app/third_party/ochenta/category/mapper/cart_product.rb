module Ochenta
  module Category
    module Mapper
      class CartProduct
        attr_reader :category_ids, :mapper

        def initialize(args = {})
          @cart = args[:cart]
          @mapper = @cart.checkout_items.each_with_object(Hash.new { |h, k| h[k] = [] }) do |item, obj|
            obj[item.product.category.id] << item.variant.id
            obj[item.product.second_category.id] << item.variant.id if item.product.second_category.present?
          end
          @category_ids = @mapper.keys
        end

        def fetch(id)
          @mapper[id]
        end
      end
    end
  end
end
