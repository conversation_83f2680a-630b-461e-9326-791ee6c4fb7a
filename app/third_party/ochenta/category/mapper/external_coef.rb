module Ochenta
  module Category
    module Mapper
      class ExternalCoef
        attr_reader :category_warranties, :mapper

        def initialize(args = {})
          @category_warranties = args[:category_warranties]
          @mapper = build_mapper
          @category_ids = @mapper.keys
        end

        def fetch(id)
          @mapper[id]
        end

        private

        def build_mapper
          aux = Hash.new

          @category_warranties.each do |category_warranty|
            category_id = category_warranty.category_id
            aux[category_id] = []
            category_warranty.warranties.each do |warranty|
              aux[category_id] << Coverages::IdCoefficient.new(coverage_id: warranty.provider_coverage_id, coefficient: warranty.coefficient )
            end
          end
          aux
        end
      end
    end
  end
end
