module Ochenta
  module Coverages
    class CategoryWarranties
      def initialize(attributes)
        @attributes = attributes
      end

      def category_id
        @attributes['idCategoria']
      end

      def warranties
        @attributes['garantiasExtendidasDisponibles'].map { |attributes| Warranty.new(attributes) }
      end

      def empty?
        @attributes['garantiasExtendidasDisponibles'].empty?
      end

      def any?
        !empty?
      end
    end
  end
end
