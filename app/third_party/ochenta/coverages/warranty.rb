module Ochenta
  module Coverages
    class Warranty
      def initialize(attributes)
        @attributes = attributes
      end

      def id
        @attributes['id']
      end

      def id_supplier
        @attributes['idProveedor']
      end

      def supplier
        @attributes['proveedor']
      end

      def provider_coverage_id
        @attributes['idCoberturaDelProveedor']
      end

      def start_date
        @attributes['fechaInicio']
      end

      def end_date
        @attributes['fechaFin']
      end

      def name
        @attributes['nombre']
      end

      def product_category
        @attributes['product_category']
      end

      def coefficient
        @attributes['coeficiente']
      end

      def enabled
        @attributes['habilitado']
      end

      def terms_in_months
        @attributes['plazoEnMeses']
      end

      def andromeda_code_category_article
        @attributes['andromedaCodigoCategoriaArticulo']
      end

      def description
        @attributes['descripcion']
      end

      def extended_description
        @attributes['descripcionAmpliada']
      end
    end
  end
end
