module Ochenta
  module Coverages
    class CategoryInsurances
      def initialize(attributes)
        @attributes = attributes
      end

      def category_id
        @attributes['idCategoria']
      end

      def insurances
        @attributes['segurosDisponibles'].map { |attributes| Insurance.new(attributes) }
      end

      def empty?
        @attributes['segurosDisponibles'].empty?
      end

      def any?
        !empty?
      end
    end
  end
end
