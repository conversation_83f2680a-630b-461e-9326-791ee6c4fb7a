module Ochenta
  module Coverages
    class Insurance
      def initialize(attributes)
        @attributes = attributes
      end

      def id
        @attributes['idSeguro']
      end

      def start_date
        @attributes['fechaInicio']
      end

      def end_date
        @attributes['fechaFin']
      end

      def name
        @attributes['nombre']
      end

      def coefficient
        @attributes['coeficiente']
      end

      def enabled
        @attributes['habilitado']
      end

      def description
        @attributes['descripcion']
      end

      def extended_description
        @attributes['descripcionAmpliada']
      end
    end
  end
end
