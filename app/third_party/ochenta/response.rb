module Ochenta
  class Response
    attr_reader :payload

    def initialize(args = {})
      @payload = args[:payload]
    end

    def parse
      @payload.each_with_object({}) do |category, obj|
        next unless category['garantiasExtendidasDisponibles'].any?

        obj[category['idCategoria']] = category['garantiasExtendidasDisponibles'].map { |x| x['idCoberturaDelProveedor'] }
      end
    end
  end
end
