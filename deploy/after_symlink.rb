# encoding: utf-8

run "rm -f #{config.release_path}/config/app_config.yml.example"
run "rm -f #{config.release_path}/config/aws.yml.example"
run "rm -f #{config.release_path}/config/sunspot.yml.example"
run "rm -fr #{config.release_path}/config/keys"
run "ln -nfs #{config.shared_path}/tmp/GeoIP.dat #{config.release_path}/tmp/GeoIP.dat"
run "ln -nfs #{config.shared_path}/tmp/pictures_uploads #{config.release_path}/tmp/"
run "ln -nfs #{config.shared_path}/config/app_config.yml #{config.release_path}/config/app_config.yml"
run "ln -nfs #{config.shared_path}/config/aws.yml #{config.release_path}/config/aws.yml"
run "ln -nfs #{config.shared_path}/config/sunspot.yml #{config.release_path}/config/sunspot.yml"
run "ln -nfs #{config.shared_path}/config/keys #{config.release_path}/config/keys"
run "ln -nfs #{config.shared_path}/tmp/feed.rss #{config.release_path}/public/rss/feed.rss"
run "ln -nfs #{config.shared_path}/tmp/sitemaps #{config.release_path}/public/sitemaps"
run "ln -nfs #{config.shared_path}/lib/certificates/bocajrs.crt #{config.release_path}/lib/certificates/bocajrs.crt"

if config.environment_name =~ /staging/
  run "rm -f #{config.release_path}/public/robots.txt"
  run "ln -nfs #{config.shared_path}/public/robots.txt  #{config.release_path}/public/robots.txt"
end

if config.environment_name =~ /production/
  run "rm -f #{config.release_path}/config/hipchat.yml.example"
  run "ln -nfs #{config.shared_path}/config/hipchat.yml #{config.release_path}/config/hipchat.yml"
  run "ln -nfs #{config.shared_path}/tmp/external_variant_ranking #{config.release_path}/public/external_variant_ranking"
end
