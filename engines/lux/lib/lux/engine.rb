module Lux
  class Engine < ::Rails::Engine
    isolate_namespace Lux

    initializer :assets do |config|
      Rails.application.config.assets.precompile += [
        'lux/application.js',
        'lux/application.css',
        'lux/v2/application.js',
        'lux/v2/application.css'
      ]
    end

    config.generators do |g|
      g.template_engine :haml
      g.test_framework :rspec
      g.fixture_replacement :factory_girl
    end

    config.autoload_paths += Dir[ Lux::Engine.root.join("app", "controllers", "lux", "concerns", "**/") ]

    config.after_initialize do
      Avenida::Payments::LoyaltyBna
      Avenida::Payments::LoanBna
    end

  end
end
