%fieldset
  -# %legend #{t('.errors_on')} #{data['error_at']}
  %legend Algunos productos no pudieron ser importados:
  - data['errors'].each do |k, v|
    %p
      %strong= "Linea #{k.last}"
      %span= v.first.to_s
- if data['row_with_errors'].present?
  %table
    %thead
      %th Row
      - data['row_with_errors'].each do |v|
        %th= v[0].to_s
    %tbody
      %td= data['error_at']
      - data['row_with_errors'].each do |v|
        - e = data['error_messages'].key?(v[0])
        %td{ class: e && 'error has-tip',
             'data-tooltip' => e,
             title: e && data['error_messages'][v[0]] }= v[1].to_s
