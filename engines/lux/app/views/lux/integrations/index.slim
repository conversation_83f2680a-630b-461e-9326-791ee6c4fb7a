.large-3.columns
  = render partial: 'lux/partials/account_submenu'
.large-9.columns
  .gp-simple-panel
    h1.thin
      = t('.title', shop: @shop.title)
    hr
    - if @available_integrations.empty?
      p No tiene integraciones disponibles.
    .integrations.row
      - @available_integrations.each do |integration|
        - integration = integration.downcase
        - @integration = @integrations[integration.to_sym]
        .deleted class=(@integration.authorized? && @integration.deleting? ? 'show' : 'hide')
          .message = t('.deleting')
        .integration-wrapper.large-12.columns
          .row
            .large-2.columns
              = render partial: "lux/integrations/#{integration}/name"
              - if @integration.authorized?
                .authorized
                  = t(".authorized", since: @integration.created_at.to_s(:long))
                - if @integration.can_be_deleted?
                  .actions
                    = button_tag "#{t('.remove')}", class: 'button tiny', form_class: 'delete-form', data: { "reveal-id" => "remove-integration-modal" }
            .large-10.columns
              - if @integration.new_record? || @integration.incomplete?
                = render partial: "lux/integrations/#{integration}/setup"
              - elsif @integration.authorized?
                = render partial: "lux/integrations/#{integration}/detail"
                = render partial: "lux/integrations/configurations"
                .integration-actions.small-12.columns
                  - if @integration.configuration_complete?
                    - if @integration.syncing?
                      = button_tag t('.already_syncing'), class: 'disabled button red small', disabled: 'disabled'
                    - else
                      = form_tag shop_integration_import_and_sync_path(integration_name: integration), id: 'import_and_sync' do
                        = submit_tag t('.import_and_sync'), class: 'button red small'

- if @integration&.authorized? && @integration&.can_be_deleted?
  = render partial: 'lux/integrations/remove_options_modal'
