#defaults.large-10.small-offset-1.columns
  h2= "#{t('.title')}:"
  = form_for @integration, url: shop_integration_path(nil, @integration.shop, @integration), :html => { :class => "custom" } do |f|
    - if @integration.need_custom_configuration?
      = render partial: "lux/integrations/#{@integration.simple_type}/custom_configuration", \
               locals:{ form: f }
    p= t('.caption')
    .row
      - default_settings = @integration.settings['defaults']
      .small-6.columns
        label = "#{t('.category')}:"
        = select_tag 'integration[settings[defaults[category_id]]]', options_from_collection_for_select(@categories, "id", "full_path", default_settings && default_settings['category_id']), include_blank: true, id: 'category-select'
    .row
      .small-3.small-offset-9.columns
        = f.submit t('.save'), class: 'button red small'

  hr
