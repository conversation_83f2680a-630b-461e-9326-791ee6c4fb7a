.row.some{ :style => "margin-left: -1px;" }
  = link_to new_shop_product_path(shop_id: @shop.id, return_to: "/manage/shops/#{@shop.to_param}/products" ), class: 'button red small new_product' do
    = t('.new')
  = link_to new_shop_product_import_path(return_to: "/manage/shops/#{@shop.to_param}/products", strategy: 'create' ), class: 'button small import_products' do
    = t('.import')
  = link_to t('.export'), export_data_shop_products_path(shop_id: @shop.id), class: 'button small export_products', :onclick => 'waiting();'
  = link_to exports_list_shop_products_path(shop_id: @shop.id, return_to: "/manage/shops/#{@shop.to_param}/products/exports_list" ), class: 'button small export_products' do
    = t('.export_list')

:javascript
  function waiting()  { 
    alert("Por favor espere 5 segundos antes de ir al listado de exportaciones"); 
  }
  
