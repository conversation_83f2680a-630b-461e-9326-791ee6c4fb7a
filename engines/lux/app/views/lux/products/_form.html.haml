- url = params[:action] == 'clone' || params[:action] == 'new' || params[:action] == 'create' ? shop_products_url(shop_id: @shop.id) : shop_product_url(shop_id: @shop.id)
- has_integration = @product.has_any_shop_integration? || @product.has_any_network_integration?
.product-form.row
  - if @product.persisted?
    - content_for :js do
      :javascript
        gp.product = #{raw @product_json.to_json}
        gp.product.has_integration = #{has_integration}
        gp.product.has_no_property = #{@product.has_no_property?}
        gp.product.url_reset_properties = "#{reset_properties_shop_product_url(@network.downcase, @shop, @product)}"
  - content_for :js do
    :javascript
      gp.availableColors = #{raw @available_colors.to_json} || []
      gp.availableProperties = #{raw @available_properties.to_json} || []
      gp.variantsNotSaved = #{raw @variants_not_saved.to_json}
      var _totalTechnicalFields = {
        data: #{@product.data_to_simple_hash(:data).count},
        data_shipment: #{@product.data_to_simple_hash(:data_shipment).count}
      };
  = form_for @product, multipart: true, as: 'product', url: url, html: {class: 'custom', id: 'product-form'} do |f|
    = hidden_field_tag :return_to, params[:return_to]
    .small-12.columns
      .row
        .field.large-6.medium-8.small-12.columns
          = render partial: 'lux/products/partials/title', locals: {f: f}
      .field
        = render partial: 'lux/products/partials/description', locals: {f: f}
    .large-7.columns
      .section.row
        .small-12.columns
          %h4= t('.properties')
        .small-12.columns
          .field
            = render partial: 'lux/products/partials/categories', locals: {f: f, product: @product}
          .field
            = render partial: 'lux/products/partials/manufacturer', locals: {f: f, has_integration: has_integration}
          .field
            = render partial: 'lux/products/partials/price', locals: {f: f}
          .field
            = render partial: 'lux/products/partials/sale_price', locals: {f: f}
          .field
            = render partial: 'lux/products/partials/availability', locals: {f: f}
          .field
            = render partial: 'lux/products/partials/picking', locals: {f: f}
          .field
            = render partial: 'lux/products/partials/purchasable', locals: {f: f, product: @product}
    .large-5.columns
      .section.row
        .small-12.columns
          %h4= t('.product_pictures')
        .small-12.columns
          .field
            = render partial: 'lux/products/partials/images', locals: {has_integration: has_integration}
        - if @product.has_no_property?
          %input{ type: 'hidden', name: '[product][available_properties][]', value:'noproperty' }
        .section.row{ style: ('display: none;' if has_integration) }
          .small-12.columns
            %h4= t('.variants')
          .small-12.columns
            = render partial: 'lux/products/partials/variants_form', locals: {f: f, product: @product, has_integration: has_integration }
    .large-12.columns
      .field
        .row
          .small-12.columns
            %label.with_explanation= t('.packages.title')
            .explanation= t('.packages.explanation')
        = f.fields_for :packages do |packages_form|
          = render partial: 'lux/products/partials/packages_field', locals: {f: packages_form}
        = link_to_add_fields "Agregar +", f, :packages
    - ["data", "data_shipment"].each do |field|
      - unless @product.send(field).nil?
        .large-12.columns
          .section.row
            - if field == "data"
              %h4 Ficha técnica
            - else
              %h4 Condiciones de entrega
          .technical-fields{class: "technical-fields-#{field}"}
            - @product.data_to_simple_hash(field).each_with_index do |(key, value), index|
              .field
                .small-5.medium-5.columns
                  %label Atributo
                  %input.text-right.input-text{class: "data-attributes-#{field}", type: "text", name: "product[#{field}][#{index}][name]", value: key }
                .small-5.medium-5.columns
                  %label Detalle
                  %input.input-text{class: "data-values-#{field}", type: "text", name: "product[#{field}][#{index}][value]", value: value }
                .small-2.medium-2.columns
                  = image_tag 'lux/delete.png', class: "technical-delete", style: "cursor: pointer; width: 10px;", title: 'Eliminar'
        .large-12.columns
          %span.button.red.add-technical{data: {type: field}} Agregar nuevo
    .large-12.columns
      %hr
      .product-actions
        %a.button{ href: params[:return_to] }= t('.cancel')
        %button.button.red#save{ type: 'submit', disabled: true }= t('.save')
