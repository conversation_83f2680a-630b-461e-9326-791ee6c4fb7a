- has_any_integration = product.has_any_shop_integration? || product.has_any_network_integration?
- has_no_property =  product.has_no_property?

- if !has_no_property
  .row
    .small-12.columns{ style: ('display: none;' if has_any_integration ) }
      %label.with_explanation= t('.title')
      .explanation= t('.explanation')

.row.variants-form
  .small-12.columns
    .templates
      - if has_no_property
        = render partial: "lux/products/partials/variants_form_noproperty", locals: {product: product}
      - else
        - Mkp::Product::AVAILABLE_PROPERTIES.each do |property|
          = render partial: "lux/products/partials/variants_form_#{property.to_s}", locals: {product: product}
      = render partial: "lux/products/partials/variants_form_property_container", locals: {product: product}
      .properties-list{ style: ('display: none;' if has_any_integration) }
      .variants
    - if !product.has_any_shop_integration? || product.has_any_network_integration?
      .row
        .small-12.columns
          .table-container{ id: "test-popup", class: "white-popup mfp-hide" }
            %h1.thin.text-center= t('.stock-management')
            #stock-table
            .submit
              %button.button#submit-stock-button= t('.submit')

  .small-6.columns#properties-list-v3-container
    = select(product, :properties, options_for_select(Mkp::Product::AVAILABLE_PROPERTIES.map{|p| [t("javascripts.lux.products.properties.#{p.to_s}"), p.to_s]}), {}, {id: "properties-list-v3"})
    .small-6.columns
      %button.add-new-property.button.grey.tiny.wide.right{ type: 'button' }= t('.new_property')
  .small-12.columns{ style: ('display: none;' if has_any_integration if has_any_integration || has_no_property) }
    %p.warning= t('.warning_properties')
  .small-12.columns
    .display-options{ style: 'display: none;' }
      = f.check_box :display_variants, { label: t('.show_all_variants'), style: 'display: none;', checked: false }
    .stock{title: "#{has_integration ? 'Este campo esta inhabilitado para los productos que son de integracion' : ''}"}
      - if has_integration
        = f.check_box :handle_stock, { id: 'manage-stock', label: t('.handle_stock'), style: "cursor: not-allowed; display: none", disabled: "disabled",  checked: false}
      - else
        = f.check_box :handle_stock, { id: 'manage-stock', label: t('.handle_stock'), style: "display: none",  checked: true}
      %span
      %a.button.small.manage-stock= t('.perform_stock_management')
      %a{href: "#test-popup", class: "open-popup-link", styles: "margin-left: 14px !important"}
      .variants-table

  - unless has_no_property
    .small-12.columns
      %label.with_explanation
        = t(".manage_property")
      - Mkp::Product::COMPATIBILITY_PROPERTIES.each do |key, name|
        %label= name
        - value = product.available_properties_names.key?(key) ? product.available_properties_names[key] : ""
        %input{type: 'text', name: "[product][available_properties_names][#{key}]", value: value, class: "input-text"}
