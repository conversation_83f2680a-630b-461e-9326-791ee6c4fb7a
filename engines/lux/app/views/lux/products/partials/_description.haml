= f.text_area :description, placeholder: t('.description'), label: t('.label'), class: 'required'

- if (@shop.stores.map(&:id) & [41, 47]).any?
  = f.select :origin_of_product,
    options_for_select([["",""],["Nacional", "national"],["Importado", "imported"],["Ensamblado en Argentina", "assembled_in_argentina"]], selected: @product.origin_of_product),
    {class: 'required', label: t('.origin_of_product')}
  = f.text_field :brand, label: t('.brand'), class: 'required'
  = f.select :energy_efficiency,
    options_for_select([""] + Mkp::Product::ENERGY_EFFICIENCY_OPTIONS, selected: @product.energy_efficiency),
    {label: t('.energy_efficiency')}
