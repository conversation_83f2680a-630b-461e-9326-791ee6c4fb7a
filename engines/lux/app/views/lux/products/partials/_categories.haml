- roots = Mkp::Category.roots.active.select('id, name, ancestry').where(['network = ?', Mkp::Shop.find(@shop.id).network]).collect { |c| [c.name, c.id, { 'data-children' => c.has_children? }] }
- has_integration = @product.has_any_shop_integration? || @product.has_any_network_integration?
.row
  .small-12.columns
    %label.with_explanation= t('.category_title')
    .explanation= t('.explanation')
.row.categories
  .hidden
    = hidden_field_tag 'product[category_id]', (@product.category.id if @product.category.present? )
    = hidden_field_tag 'product_category_path_ids', [@product.category.path_ids] if @product.category.present?
  .root_categories.large-4.medium-4.small-12.columns
    = select_tag 'root_categories', options_for_select(roots), prompt: "--- #{t('.pick_one')} ---", disabled: has_integration
    %small{class: "error medium input-text", style: @product.errors.messages[:categories].present? && 'display: inherit;'}
      = t('.choose_correctly')
    %script#select-tag{ type: 'text/template' }
      %select{ id: 'category_<%= data.id %>_children', disabled: has_integration }
        %option{value:''}
          ="----- #{t('.loading')} -----"
.row
  .small-12.columns
    %label.with_explanation= t('.second_category_title')
    .explanation= t('.explanation')
.row.categories_2
  .hidden
    = hidden_field_tag 'product[second_category_id]', (@product.second_category.id if @product.second_category.present? )
    = hidden_field_tag 'product_category_path_ids_2', [@product.second_category.path_ids] if @product.second_category.present?
  .root_categories_2.large-4.medium-4.small-12.columns
    = select_tag 'root_categories_2', options_for_select(roots), prompt: "--- #{t('.pick_one')} ---", disabled: has_integration
    %small{class: "error medium input-text category_test", style: "display:none"}
      = t('.choose_correctly')
    %script#select-tag{ type: 'text/template' }
      %select{ id: 'second_category_<%= data.id %>_children', disabled: has_integration }
        %option{value:''}
          ="----- #{t('.loading')} -----"
