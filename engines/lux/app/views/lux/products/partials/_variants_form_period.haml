%script#period-template{type: 'text/template'}
  .row.collapse.percentage-container.property-value
    .small-1.columns &nbsp;
    .small-6.columns
      .row.collapse
        .small-6.columns
          %span.prefix
            #{t('lux.products.partials.properties.period')}:
        .small-6.columns
          - selected = product.variants.find{|v| v.properties && v.properties[:period].present?}
          - selected_value = selected.present? ? selected.properties[:period].to_i : Mkp::Product::PERIOD.first[:value]
          %select{ name: 'properties[][period]', class:"period-value custom dropdown", required: true}
            - Mkp::Product::PERIOD.each do |period|
              %option{ value: period[:value], selected: period[:value] == selected_value}= period[:value]
    .small-2.columns
      %a{class: 'delete-property-value'}= t('lux.products.partials.properties.remove')