%script#coef-template{type: 'text/template'}
  .dimensions-container.row.collapse.property-value
    .small-4.columns &nbsp;
    .small-6.columns
      .row.collapse
        .small-5.columns
          %span.prefix
            #{t('lux.products.partials.properties.coef')}:
        .small-7.columns
          %input.coef-value.required.with_prefix{ type: 'text', name: 'properties[][coef]', value: '<%= data.coef %>', required: true }
    .small-2.columns
      %a{class: 'delete-property-value'}= t('lux.products.partials.properties.remove')