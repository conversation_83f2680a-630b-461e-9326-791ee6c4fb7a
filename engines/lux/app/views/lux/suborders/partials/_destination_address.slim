- is_pickup = shipment.pickable?
- address = is_pickup ? shipment.origin_address : shipment.destination_address
.div.text-center
  - if shipment.virtual?
    span=t('.virtual')
  - else
    h3= is_pickup ? t('.pickup') : t('.title')
  .details
    .email= address.email if address.email
    .name= address_full_name(address)
    .address= full_address(address)
    .country= full_country(address)
    - if @suborder.payment&.gateway == "Empty" && @suborder.payment&.gateway_data&.dig(:cuil).present?
      - cuil = @suborder.payment.gateway_data.dig(:cuil)
      .dni = "CUIL: " + cuil
    - else
      - if (dni = @suborder.customer.doc_number).present?
        .dni= t('.doc_number', number: dni)
    - if address && (number = phone_number(address)).present?
      .phone= t('.phone', number: number)
