- items = shipment.items
- order = suborder.order
- voided_payment = (order.is_paid? || order.payment.try(:pending?)) ? false : true
h3= shipment.shipment_kind_label

hr
.unfulfilled data-soid=suborder.id
  .row
    .small-12.columns
      .actions
        - if suborder.fulfilled_by_gp?
          .message= t('.fulfilled_by_gp_html')
        - else
          - if order.is_paid?
            - if shipment.shipment_kind_label == 'Pickup'
              .message= t('.pickup_by_client')
              = form_for @suborder, url: change_to_ready_to_pickup_shop_suborders_path, method: :put, :html => {:style => 'display: inline-block'} do |f|
                  = f.hidden_field(:suborder_id, :value => @suborder.id)
                  = f.submit t('lux.suborders.state_buttons.ready_to_pickup')
            - else
              .message= t('.fulfilled_by_you')
              span.fulfill-items data-ids=items.map(&:id) data-shipment-id=shipment.id
                = t('.fulfill_items')
          - elsif order.payment.try(:pending?)
            .message.awaiting_payment
              = t('.awaiting_payment')
          - else
            .message.voided_payment
              = t('.voided_payment')
      .general-actions
        .details
          = "#{t('.shipment-updated-at')}: #{shipment.updated_at.strftime('%d-%m-%Y %H:%M')}"
