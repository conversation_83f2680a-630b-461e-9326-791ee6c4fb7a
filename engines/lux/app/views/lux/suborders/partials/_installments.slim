- payment = suborder.payment

.div.gp-panel
  .div.text-center
    h3=t('.title')
    .details
    - if suborder.all_payments.count == 1
      .row
        .medium-6.columns
        h4 = t('.coef_amount', amount: number_to_currency(payment.amount_coef(suborder), unit: '', delimiter: '.', separator: ',', precision: 2))
        h4 = t('.total_installment', amount: number_to_currency(payment.sub_payment_amount(suborder), unit: '', delimiter: '.', separator: ',', precision: 2))
        - if payment.number_of_installments_without_text.present?
          =t('.installments', number: payment.number_of_installments_without_text)
        - else
          =t('.installments', number: payment.get_installments)
    - else
      .row
      - suborder.all_payments.each_with_index do |pay, index|
        div class = "medium-#{12 / suborder.all_payments.count} columns"
          h3=t(pay.loyalty_bna_gateway? ? '.points' : '.card')
          - if pay.loyalty_bna_gateway?
            h4=t('.points_amount', amount: suborder.total_points)
          - else
            h4=t('.coef_amount', amount: number_to_currency(pay.amount_coef(suborder), unit: '', delimiter: '.', separator: ',', precision: 2))
          h4=t('.total_installment', amount: number_to_currency(pay.sub_payment_amount(suborder), unit: '', delimiter: '.', separator: ',', precision: 2))
          - if !pay.loyalty_bna_gateway? && pay.number_of_installments_without_text.present?
            =t('.installments', number: pay.number_of_installments_without_text)
    - if payment.government_installments.present?
      =t('.government_plan', present: "Si")
    - else
      =t('.government_plan', present: "No")