- suborder_link = shop_suborder_url(id: suborder.public_id)
tr
  td
    img src="#{image_path(suborder.store.logo)}" alt="#{suborder.store.title}"
  td= link_to "##{suborder.public_id}", suborder_link
  td= l suborder.created_at.to_date
  -customer = suborder.customer
  td.details
    .name= customer.full_name
    span= customer.email
  td.details
    h3= suborder.title
    span= t('.total_products', count: suborder.total_products)
    - if suborder.order.customer_type == "Mkp::Guest"
      span= t('.subtotal', amount: "#{number_to_currency(suborder.total_price_charged, precision: 2)}")
    - else
      span= t('.subtotal', amount: "#{number_to_currency(suborder.total, precision: 2)}#{(', ' + suborder.subtotal_points.to_s + 'Puntos') if suborder.subtotal_points > 0}")
  td
    span.shipment_kind = t(".operation.#{suborder.logistic_type}")
  td
    - shipment = suborder.shipment
    - if shipment
      span.suborder_status =t(".order_status.#{status_product(suborder.logistic_type, shipment.status)}")
    - elsif suborder.items.first.present?
      span.suborder_status =t(".order_status.#{status_product(suborder.logistic_type, suborder.items.first.status)}")
    - else
      span.suborder_status =t(".order_status.#{status_product(suborder.logistic_type, 'unfulfilled')}")
  td
    - status_of_payment = suborder.payment_status
    span.payment_status class="#{status_of_payment}" = t(".payment.#{status_of_payment}")
  td
    - shipment = suborder.shipment
    - if shipment
      span.shipment_status class="#{shipment.status}" = t(".shipment.#{shipment.status}")
    -else
      span.shipment_status class="unfulfilled" = t(".shipment.unfulfilled")
