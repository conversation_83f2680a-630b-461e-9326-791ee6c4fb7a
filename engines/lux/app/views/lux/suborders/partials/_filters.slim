.general-filters
  .suborder-filters
    .content
      .filtering
        label= t('.filter_by')
        select name='filter_by' id='filter_by' width='150px'
          option value='created_at' selected=('selected' if @order.nil? || selected_field?(:filter_by, 'created_at')) = t('.created_at')
          option value='paid_at' selected=selected_field?(:filter_by, 'paid_at') = t('.payment_received_at')
      .ordering
        label= t('.order')
        select name='order' id='order' width='150px'
          option value='created_at' selected=('selected' if @order.nil? || selected_field?(:order, 'created_at')) = t('.created_at')
          option value='paid_at' selected=selected_field?(:order, 'paid_at') = t('.payment_received_at')
      .filtering
        label= t('.shipment_status')
        = select_tag "shipment_status", options_for_select(status_selector, @shipment_status), { prompt: 'Seleccione: ' }
      .filtering
        label= t('.external_redemption_status')
        = check_box_tag 'external_redemption_completed'

    .content
      .date_from
        label = t('.date.from')
        input type='text' id='start_date' name='start_date' value=@start_date.strftime('%d-%m-%Y')
      .date_to
        label = t('.date.to')
        input type='text' id='end_date' name='end_date' value=@end_date.strftime('%d-%m-%Y')
      .filtering
        label = t('.order_item_status')
        = select_tag "order_item_status", options_for_select(order_item_selector, @order_item_status), {prompt: 'Seleccione: '}
      .filtering
        label = t('.shipment_type')
        = select_tag "shipment_type", options_for_select({ "Normal"=>"normal", "Cambio"=>"exchange", "Devolución"=>"refund", "Pickup"=>"pickup" },
                @shipment_type), { prompt: 'Seleccione: ' }

    .actions
      input id='filter-btn' type='button' class='button tiny' value=t('.filter')
      = link_to 'Exportar CSV', shop_suborders_path(format: :csv, params: params), {class: 'button tiny', type: 'button' }
      = link_to 'Importar etiquetas', suborder_import_shop_suborders_path(strategy: 'create'), {class: 'button tiny', type: 'button' }
      - if @shop.allows_pickup?
        = link_to 'Actualizar Ordenes Pick-up', suborder_pick_up_shop_suborders_path(strategy: 'create'), {class: 'button tiny', type: 'button' }
