module Lux
  module SubordersHelper
    class AddressOwner
      attr_reader :owner
      def initialize(type, id)
        @owner = type.constantize.find(id)
      end
    end

    ###### SUBORDER INDEX STUFF ####
    def selected_field?(param_key, value)
      'selected' if params[param_key] == value
    end

    def payment_status(order)
      if order.payment.present?
        order.payment.status
      elsif order.is_paid?
        'collected'
      else
        'error'
      end
    end

    ###### SUBORDER SHOW STUFF ####
    def customer_avatar_url(customer)
      customer.avatar.url(:t)
    end

    def item_detail(item)
      resume = []
      resume << "SKU: #{item.variant.sku}"
      resume << "GP SKU: #{item.variant.gp_sku}"
      if item.product.points?
        resume << "Puntos: #{item.points}"
      else
        resume << "Precio regular: #{number_to_currency(item.price, delimiter: '.', separator: ',', precision: 2)}"
      end
      resume << "Precio de oferta: #{number_to_currency(item.unit_price_charged, delimiter: '.', separator: ',', precision: 2)}" if item.on_sale?
      resume << "Cantidad: #{item.quantity}"
      resume << "Último envío asociado: #{item.current_shipment.id}" if item.current_shipment.present?
      resume << "<b>Estado: #{item.prefix_status}#{t("pioneer.orders.#{item.status}")}</b>"
      resume.join("<br>").html_safe
    end

    def item_warranty_detail(item)
      warranty_response = WarrantyResponse.where(suborder_id: item.suborder.id)

      resume = []
      resume << "SKU: #{item.variant.sku}"
      resume << "GP SKU: #{item.variant.gp_sku}"
      resume << "Precio regular: #{number_to_currency(item.price, delimiter: '.', separator: ',', precision: 2)}"
      resume << "Precio de oferta: #{number_to_currency(item.unit_price_charged, delimiter: '.', separator: ',', precision: 2)}" if item.on_sale?
      resume << "Cantidad: #{item.quantity}"
      resume << "Transacción: #{warranty_response.first.transaction_id}" if warranty_response.present?
      resume << "Operación: #{warranty_response.first.operation_id}" if warranty_response.present?
      resume.join("<br>").html_safe
    end

    def placed_at(suborder)
      "#{l(suborder.created_at, format: :shortest)}(#{time_ago_in_words suborder.created_at})".html_safe
    end

    def payment_column_class(suborder)
      count = 0
      count += 1 if suborder.payment
      count += 1 if suborder.subtotal_points > 0
      count += 1 if suborder.coupon_discount.positive?

      return "large-#{12/count} medium-#{12/count} small-12 columns"
    end

    def address_full_name(address)
      address.full_name || [address.first_name, address.last_name].join(' ').strip
    rescue
      address_owner = AddressOwner.new(address.addressable_type, address.addressable_id).owner
      address_owner.full_name
    end

    def full_address(address)
      street = address.address
      if (street_number = address.street_number).present?
        output = [["#{street} #{street_number}", address.address_2].select{|t| t.present?}.join(', ').strip]
      else
        output = [[street, address.address_2].select{|t| t.present?}.join(', ').strip]
      end
      output << [address.city, address.state, address.zip].compact.join(' - ').strip
      output.join('<br>').html_safe
    end

    def full_country(address)
      GeoConstants::Countries.name_for_code(address.country)
    end

    def phone_number(address)
      return address.telephone if address.telephone.present?
      address_owner = AddressOwner.new(address.addressable_type, address.addressable_id).owner
      address_owner.addresses.map(&:telephone).uniq.compact.last
    rescue
      nil
    end

    def unfulfilled_items(suborder)
      return [] unless unfulfilled_shipment(suborder).present?
      unfulfilled_shipment(suborder).items
    end

    def unfulfilled_shipment(suborder)
      @unfulfilled_shipment ||= suborder.shipments.select{ |shipment| shipment.unfulfilled? && shipment.outgoing? }.compact.uniq.first
    end

    def active_shipments(suborder)
      suborder.shipments.select{ |shipment| !shipment.unfulfilled? && shipment.outgoing? }
    end

    def couriers_select_options(network)
      couriers = Couriers.get_list(network) || {}
      couriers.map{|k, info| [ info[:name] , k ] }
    end


    def status_product(operation, shipment_status)
      case operation
      when 'refund'
        shipment_status == 'delivered' ? 'refund' : 'delivered'
      when 'exchange_refund', 'exchange_change', 'exchange'
        'delivered'
      else
        shipment_status
      end
    end

  end
end
