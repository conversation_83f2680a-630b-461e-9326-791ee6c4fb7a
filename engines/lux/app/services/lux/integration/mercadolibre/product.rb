module Lux
  module Integration
    module Mercadolibre
      class Product < ::Lux::Integration::Product
        def initialize(integration)
          super
        end

        def find(item_ids)
          if item_ids == :all
            get_all_products
          else
            get_products(item_ids)
          end
        end

        private

        def get_all_products
          item_ids = @integration.get_all_my_item_ids({status: 'active'})

          @products = item_ids.map do |id|
            product = @integration.get_item(id)
            MercadolibreProduct.new(product, @integration)
          end
        end

        def get_products(item_ids)
          @products = item_ids.map do |id|
            product = @integration.get_item(id)
            MercadolibreProduct.new(product, @integration)
          end
        end
      end

      class MercadolibreProduct < ::Lux::Integration::ComplexItem
        attr_reader :data, :shop, :network, :payload, :pictures, :available_properties, :status

        DATA_MAP = {
          external_id:        :id,
          title:              :title,
          description:        :descriptions,
          regular_price:      :original_price,
          price:              :price,
          available_on:       :start_time,
          pictures:           :pictures,
          variants:           :variations,
          attributes:         :attributes,
          category:           :category_id,
          status:             :status,
          product_quantity:   :available_quantity,
          external_parent_id: :parent_item_id
        }.freeze

        CHARS_ENCODING_MAP = {
          "&iexcl;"  => "¡",
          "&iquest;" => "¿",
          "&apos;"   => "'",
          "&aacute;" => "á",
          "&eacute;" => "é",
          "&iacute;" => "í",
          "&oacute;" => "ó",
          "&uacute;" => "ú",
          "&Aacute;" => "Á",
          "&Eacute;" => "É",
          "&Iacute;" => "Í",
          "&Oacute;" => "Ó",
          "&Uacute;" => "Ú",
          "&ntilde;" => "ñ",
          "&Ntilde;" => "Ñ",
          "&nbsp;"   => " "
        }

        def initialize(mercadolibre_item, integration)
          @integration = integration
          @shop = @integration.shop
          @network = @shop.network
          @available_properties = []
          @variants_properties = []
          @payload = mercadolibre_item
          super(mercadolibre_item)
          get_all_pictures
          create_variants
        end

        def external_id
          data[:external_id]
        end

        def external_parent_id
          data[:external_parent_id]
        end

        def currency_id
          Network.for(@network).currency_id
        end

        def regular_price
          data[:regular_price] || data[:price]
        end

        def sale_price
          data[:regular_price].present? && data[:regular_price] != data[:price] ? data[:price] : nil
        end

        def status
          data[:status]
        end

        def available_on
          data[:status] == 'active' ? data[:available_on] : nil
        end

        def description
          return title if data[:description].blank?

          data[:description]&.reverse&.find { |desc| desc.plain_text.present? }&.plain_text || title
        end

        def sanitize_description(text)
          text.gsub!(/(\&\S{1}acute;|\&\S{1}tilde;|\&iexcl;|\&iquest;|\&apos;|\&nbsp;)/) { CHARS_ENCODING_MAP[Regexp.last_match[1]] }
          text.gsub!(/<p>\s*<\/p>/, "")
          text.gsub!(/<br>/, "\r\n")
          text.gsub!(/(\r\n){2,}/, "\r\n\r\n")
          ActionView::Base.full_sanitizer.sanitize(text)
        end

        def variants
          @variants
        end

        def without_properties?
          @properties.blank?
        end

        def without_variants?
          data[:variants].blank?
        end

        def with_variants?
          data[:variants].present?
        end

        def create_variants
          if without_variants?
            @variants = [MercadolibreDummyVariant.new(self)]
          else
            @variants = data[:variants].map do |variant|
              variant = MercadolibreVariant.new(OpenStruct.new(variant), self)
              @variants_properties << variant.properties
              variant
            end
          end
        end

        def available_properties
          return @available_properties unless @available_properties.blank?
          @available_properties = without_properties? ? [:noproperty] : properties.map(&:name_sym)
        end

        # The reason for this is the primary and secondary color from MELI
        def variants_properties
          @variants_properties
        end

        def category_mapper
          return @gp_category if @gp_category.present?

          category_path = data[:category].path_from_root.map(&:name).join('/')
          return if category_path.blank?

          conditions = ['full_path_name LIKE ?', "%#{category_path}%"]
          category = Mkp::Category.where(network: network).where(conditions).first
          @gp_category = category if category.present?
        end

        def manufacturer_mapper
          return @gp_manufacturer if @gp_manufacturer.present?

          ml_brand = data[:attributes]&.find { |attr| attr[:id] == :brand.to_s.upcase }
          manufacturer_name = ml_brand.present? ? ml_brand[:value_name] : nil
          if manufacturer_name.present?
            manufacturer = get_manufacturer(manufacturer_name)
            return (@gp_manufacturer = manufacturer) if manufacturer.present?
          else
            # Actually, the `shop.brand` call returns the shop admin, that could be a Brand
            # or a SocialUser. So, for now, we better be careful.
            pseudobrand = shop.try(:brand)

            manufacturer = pseudobrand.try(:manufacturer) if pseudobrand.respond_to?(:manufacturer)
            # Fallback to a prefixed manufacturer.
            manufacturer ||= Mkp::Manufacturer.first
            @gp_manufacturer = manufacturer
          end
        end

        def get_manufacturer(name)
          conditions = ['name LIKE ?', "%#{name}%"]
          manufacturer = Mkp::Manufacturer.where(conditions).first
          if manufacturer.blank?
            manufacturer = Mkp::Manufacturer.new(name: name)
            manufacturer.save
          end
          manufacturer
        end

        def get_all_pictures
          product_pictures = Array.wrap(data[:pictures])
          @pictures ||= product_pictures.map do |picture|
            MercadolibrePicture.new(picture)
          end
        end

        def get_picture(external_id)
          @pictures.detect{ |pic| pic.external_id == external_id }
        end

        def set_available_properties(property_keys = [])
          @available_properties = property_keys
        end

        def to_hash(debugable = false)
          hash = {
            available_on: available_on,
            currency_id: currency_id,
            display_variants: true,
            handle_stock: true,
            regular_price: regular_price,
            sale_price: sale_price,
            title: title,
            description: description,
            shop_id: shop.id,
            category_id: (category_mapper.id if category_mapper),
            manufacturer_id: (manufacturer_mapper.id if manufacturer_mapper),
            available_properties: available_properties
          }

          if sale_price.present? && !sale_price.zero?
            hash.merge!({
              sale_on: Time.now.beginning_of_day,
              sale_until: Time.now.end_of_day + 1.week
            })
          end

          if debugable
            hash.merge!({
              variants_attributes: variants.map(&:to_hash),
              payload: payload
            })
          end

          hash.merge!({
            packages_attributes: [{
              mass_unit: Network.for(@network).mass_unit,
              length_unit: Network.for(@network).length_unit,
              width: 0,
              height: 0,
              length: 0,
              weight: 0
            }]
          })

          hash
        end
      end

      class MercadolibreDummyVariant
        attr_reader :product, :properties

        def initialize(mercadolibre_product)
          @product = mercadolibre_product
          setup_properties
        end

        def quantity
          product.data[:product_quantity]
        end

        def picture
          product.pictures.first
        end

        def sku
          nil
        end

        def external_id
          product.external_id
        end

        def setup_properties
          @properties = {
            noproperty: true
          }
          product.set_available_properties([:noproperty])
        end

        def to_hash
          {
            quantity:   quantity,
            visible:    true,
            properties: properties,
            sku: nil
          }
        end
      end

      class MercadolibreVariant < ::Lux::Integration::ComplexItem
        attr_reader :data, :product, :picture, :properties

        DATA_MAP = {
          external_id: :id,
          pictures:    :picture_ids,
          quantity:    :available_quantity,
          price:       :price,
          properties:  :attribute_combinations,
          sku:         :seller_custom_field
        }.freeze

        AVAILABLE_PROPERTIES = {
          'color'  => :color,
          '83000'  => :color,
          '73001'  => :secondary_color,
          '93000'  => :size,
          '73002'  => :size,
          '103000' => :size,
          '30000'  => :size,
          '40000'  => :size
        }.freeze

        def initialize(mercadolibre_item, product)
          @product = product
          super(mercadolibre_item)
          setup_properties
          @picture = picture
        end

        def external_id
          data[:external_id]
        end

        def quantity
          data[:quantity]
        end

        def sku
          data[:sku]
        end

        def setup_properties
          @properties ||= {}

          valid_properties = data[:properties].select do |property|
            property_id = property['id'].to_s.downcase

            AVAILABLE_PROPERTIES.key?(property_id) ||
            AVAILABLE_PROPERTIES.key?(property_id.to_sym)
          end

          properties_for_product = []


          valid_properties.each do |property|
            property_id = property['id'].to_s.downcase

            if AVAILABLE_PROPERTIES[property_id].present?
              property[:property_name] = AVAILABLE_PROPERTIES[property_id]
            else
              property[:property_name] = property_id.to_sym if property[:property_name].blank?
            end

            if property[:property_name] == :secondary_color
              @properties[:color][:name] += "/#{property['value_name']}"

              if color_hex_duplicated?(@properties[:color][:name], @properties[:color][:hex])
                @properties[:color][:hex] = alternative_color_hex(property)
              end
            else
              mercadolibre_property = MercadolibreProperty.new(OpenStruct.new(property)).to_hash

              @properties[property[:property_name]] = mercadolibre_property

              if single_color_variant?(valid_properties) && color_hex_duplicated?(@properties[:color][:name], @properties[:color][:hex])
                @properties[:color][:hex] &&= modify_color_hex(@properties[:color][:hex])
              end
            end

            properties_for_product = @properties.keys
          end

          remaining_properties = data[:properties] - valid_properties
          remaining_properties.each do |rem_property|
            id = rem_property['id'].to_s.downcase
            name = rem_property['name'].downcase

            @properties[id.to_sym] = MercadolibreProperty.new(OpenStruct.new(rem_property)).to_hash

            properties_for_product << { slug: id, name: name }

          end

          product.set_available_properties(properties_for_product)
        end

        def picture
          return unless data[:pictures].any?

          picture_id = data[:pictures].detect{ |pid| product.get_picture(pid) }
          if picture_id
            picture = product.get_picture(picture_id)
            picture.variant_ids << external_id

            picture
          end
        end

        def to_hash
          {
            quantity:   quantity,
            visible:    true,
            sku:        sku,
            properties: @properties
          }
        end

        private

        def color_hex_duplicated?(color_name, color_hex)
          return unless self.product.variants_properties.first.present?

          self.product.variants_properties.detect do |properties|
            properties[:color][:name] != color_name && properties[:color][:hex] == color_hex
          end
        end

        def alternative_color_hex(property)
          secondary_color_hex = MercadolibreProperty::COLOR_MAP[property[:value_name]]
          alternative_color_hex = if color_hex_duplicated?(@properties[:color][:name], secondary_color_hex)
            modify_color_hex(@properties[:color][:hex])
          else
            secondary_color_hex
          end
        end

        def modify_color_hex(hex_string)
          hex_string == "#FFFFFF" ? change_color(hex_string, :darker) : change_color(hex_string)
        end

        def change_color(hex_string, way = :ligthen, by = 1)
          rgb = hex_string.gsub('#','').scan(/../).map(&:hex)
          if way == :darker
            rgb[0] = [(rgb[0].to_i - by), 0].max
            rgb[1] = [(rgb[1].to_i - by), 0].max
            rgb[2] = [(rgb[2].to_i - by), 0].max
          else
            rgb[0] = [(rgb[0].to_i + by), 255].min
            rgb[1] = [(rgb[1].to_i + by), 255].min
            rgb[2] = [(rgb[2].to_i + by), 255].min
          end
          "#%02x%02x%02x" % rgb
        end

        def single_color_variant?(valid_properties)
          valid_properties.detect do |property|
            property[:name] == "Color Secundario"
          end.blank?
        end
      end

      class MercadolibreProperty
        attr_reader :value_name, :property_name

        COLOR_MAP = {
          'Rojo' =>           '#FF0000',
          'Rosa' =>           '#F4CCCC',
          'Terracota' =>      '#E06666',
          'Beige' =>          '#FCE5CD',
          'Piel' =>           '#F6B26B',
          'Marrón' =>         '#B45F06',
          'Amarillo' =>       '#FFFF00',
          'Crema' =>          '#FFF2CC',
          'Ocre' =>           '#FFD966',
          'Dorado' =>         '#BF9000',
          'Verde' =>          '#0C9800',
          'Verde claro' =>    '#D9EAD3',
          'Verde cielo' =>    '#D9EAD5',
          'Verde oscuro' =>   '#38761D',
          'Agua' =>           '#83DDFF',
          'Azul marino' =>    '#76A5AF',
          'Azul petróleo' =>  '#134F5C',
          'Azul' =>           '#1717FF',
          'Celeste' =>        '#CFE2F3',
          'Azul acero' =>     '#6FA8DC',
          'Azul oscuro' =>    '#0B5394',
          'Violeta oscuro' => '#7600FF',
          'Lavanda' =>        '#D9D2E9',
          'Lila' =>           '#8E7CC3',
          'Fucsia' =>         '#E828FF',
          'Salmón' =>         '#EAD1DC',
          'Fucsia oscuro' =>  '#C27BA0',
          'Púrpura' =>        '#741B47',
          'Negro' =>          '#000000',
          'Gris' =>           '#666666',
          'Blanco' =>         '#FFFFFF',
          'Bordó' =>          '#800000',
          'Naranja' =>        '#FF9900',
          'Esmeralda' =>      '#01D758',
          'Plateado' =>       '#C0C0C0',
          'Piel clara' =>     '#EED5B7'
        }.freeze

        def initialize(mercadolibre_property)
          @value_name = mercadolibre_property.value_name
          @property_name = mercadolibre_property.property_name
        end

        def to_hash
          case property_name
          when :color
            {
              hex: COLOR_MAP.key?(value_name) ? COLOR_MAP[value_name] : nil,
              name: value_name
            }
          else
            value_name
          end
        end
      end

      class MercadolibrePicture
        attr_accessor :variant_ids
        attr_reader :external_id, :src, :styles

        SIZE_MAP = {
          st:       'S',
          t:        'T',
          m:        'E',
          ml:       'C',
          l:        'F',
          original: 'O'
        }

        def initialize(mercadolibre_image)
          @external_id = mercadolibre_image.id
          @src = mercadolibre_image.url
          @variant_ids = []

          generate_style_urls
        end

        def generate_style_urls
          @styles = SIZE_MAP.keys.each_with_object(HashWithIndifferentAccess.new) do |style, hash|
            hash[style] = url(style)
          end
        end

        def url(style = nil)
          return src if style.nil?
          # Available sizes:
          # S    => limit the height to 90px
          # T    => limit the height to 160px
          # H    => limit the height to 250px
          # E    => limit the height to 280px
          # C    => limit the height to 400px
          # O    => original
          meli_style = "-#{SIZE_MAP[style]}."
          src.gsub(/-O./, meli_style)
        end

        def to_hash
          {
            styles: styles,
            src: src,
            external_id: external_id,
            variant_ids: variant_ids,
          }
        end
      end

    end
  end
end
