module Lux
  module ProductImport
    class Mapper
      ATTRIBUTES_FOR_UPDATE = { product: [:regular_price, :title, :width, :height, :length, :length_unit, :weight, :mass_unit], variant: [:sku, :quantity, :ean] }

      def initialize(headers, shop)
        @headers = headers
        @shop = shop
      end

      def to_product_attributes(row, for_update = false)
        product_attributes = {
          title: row[:title],
          description: row[:description],
          category_id: to_category_id(row),
          manufacturer_id: to_manufacturer_id(row),
          regular_price: row[:price],
          regular_price_without_taxes: row[:price_without_taxes],
          sale_until: row[:sale_until].blank? ? nil : to_date(row[:sale_until]),
          sale_on: row[:sale_on].blank? ? nil : to_date(row[:sale_on]),
          sale_price: row[:sale_price],
          sale_price_without_taxes: row[:sale_price_without_taxes],
          available_on: row[:available_on].blank? ? Date.today : to_date(row[:available_on]),
          iva: row[:iva] || 21,
          data: data_details(row),
          data_shipment: data_shipment(row),
          currency_id: @shop.currency.id,
          available_properties: available_properties(row),
          handle_stock: true,
          shop_id: @shop.id,
          packages_attributes: packages_properties(row),
          transaction_type: transaction_type(row)
        }

        product_attributes[:origin_of_product] = row[:origin_of_product].to_i if row[:origin_of_product].present?
        product_attributes[:brand] = row[:brand] if row[:brand].present?
        product_attributes[:energy_efficiency] = row[:energy_efficiency] if row[:energy_efficiency].present?

        product_attributes.select do |key, value|
          !for_update || ATTRIBUTES_FOR_UPDATE[:product].include?(key)
        end
      end

      def transaction_type(row)
        return 0 unless row[:transaction_type]
        Mkp::Product.transaction_types[row[:transaction_type]] || 0
      end

      def to_packages_attributes(row)
        packages_properties(row)
      end

      def to_variants_attributes(row)
        return {} if %i[property_names property_values property_quantity property_points_price property_discount_top].all?{ |attr| row[attr].nil? }
        variants_properties(row).map do |variant|
          {
            sku: variant[:sku],
            quantity: variant[:quantity].to_i,
            ean_code: variant[:ean],
            properties: properties(variant[:properties]),
            points_price: variant[:points_price],
            discount_top: variant[:discount_top]
          }
        end
      end

      private

      def data_details(row)
        if row[:data_key].present? && row[:data_detalle].present?
          combination(row[:data_key], row[:data_detalle])
        end
      end

      def data_shipment(row)
        if row[:data_shipment_key].present? && row[:data_shipment_detalle].present?
          combination(row[:data_shipment_key], row[:data_shipment_detalle])
        end
      end

      def combination(keys, values)
        Import::Combinate.new(keys, values).params.each_with_object({}).with_index{|((k,v),h), i| h[i] = {name: k, value: v}}
      end

      def properties(property)
        if property.present? && property[:color]
          property[:color] = color_value_of(property[:color])
        end
        property
      end

      def available_properties(row)
        return {} if variants_properties(row).blank?
        variants_properties(row).first[:properties].keys
      end

      def packages_properties(row)
        Import::GenerationPackages.new(row).build
      end

      def variants_properties(row)
        return {} unless row[:property_names].present? && row[:property_values].present?
        Import::GenerationVariants.new(row[:property_names], row[:property_values], row).build
      end

      def to_category_id(row)
        if (id = row[:category].to_i) > 0
          Mkp::Category.find_by_id(id).try(:id)
        else
          name = row[:category]
          Mkp::Category.active.find_by_name(name).try(:id)
        end
      end

      def color_value_of(color_name)
        Mkp::ProductColors.value_of(color_name.capitalize, @shop.network)
      end

      def to_manufacturer_id(row)
        Mkp::Manufacturer.find_by_name(row[:manufacturer]).try(:id)
      end

      def to_date(date)
        Time.zone.local_to_utc(DateTime.strptime(date, '%Y-%m-%d'))
      rescue
        Date.today
      end

      def valid_length_unit?(unit)
        value = (unit == 'inches' || unit == 'millimeters') ? true : false
      end

      def valid_mass_unit?(unit)
        value = (unit == 'kilograms' || unit == 'pounds') ? true : false
      end

    end
  end
end
