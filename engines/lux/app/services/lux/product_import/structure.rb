module Lux
  module ProductImport
    class Structure
      attr_accessor :errors, :error_messages, :position, :error_row

      IMAGES = %i[imagen_1 imagen_2 imagen_3 imagen_4 imagen_5 imagen_6 imagen_7 imagen_8].freeze
      PRODUCT = %i[title description manufacturer category price price_without_taxes sale_until sale_on sale_price sale_price_without_taxes iva available_on weight packages height length width pickeable energy_efficiency brand origin_of_product].freeze
      VARIANT = %i[stock sku ean].freeze + IMAGES
      ALL_FIELDS = %i[av_sku data_name data_details].freeze + Structure::PRODUCT + Structure::VARIANT
      DATE_FIELDS = %i[sale_until available_on sale_on].freeze
      LANG_PATH = 'lux.product_imports.structure'

      def initialize
        @errors = false
        @error_messages = {}
        @error_row = []
      end

      def validate(file)
        file.each_with_index do |row, i|
          @info = row[:av_sku]
          @type = variant_av_sku_valid?(@info) ? :VARIANT : :PRODUCT
          @position = i + 1
          validates_columns(row.headers) if @position == 1
          send("validate_#{@type.downcase.to_s}", row[:av_sku])
          row.headers.each do |header|
            validates_presents(header, row)
            validates_obsolete(header, row)
            validates_dates(header, row) if @type == :PRODUCT
          end
        end
      end

      private

      def obsolete_type
        @type == :PRODUCT ? :VARIANT : :PRODUCT
      end

      def variant_av_sku_valid?(field)
        return handle_error(I18n.t("#{LANG_PATH}.presences", header: "av_sku")) if field.nil?
        field.match(/\w{2,3}\-\d+\-\w*\-*/).present?
      end

      def validate_variant(field)
        unless Mkp::Variant.exists?(gp_sku: field)
          handle_error(I18n.t("#{LANG_PATH}.unavailable", type: @type.to_s, info: field))
        end
      end

      def validate_product(field)
        unless Mkp::Product.exists?(id: field)
          handle_error(I18n.t("#{LANG_PATH}.unavailable", type: @type.to_s, info: field))
        end
      end

      def validate_format_date(field)
        field.match(/\d{4}-\d{2}-\d{2}/).present?
      end

      def validates_presents(header, row)
        return true if IMAGES.include?(header)

        if const(@type).include?(header) && row[header].nil?
          handle_error(I18n.t("#{LANG_PATH}.presences", header: header.to_s.humanize))
        end
      end

      def validates_obsolete(header, row)
        return true if IMAGES.include?(header)

        if const(obsolete_type).include?(header) && row[header].present?
          # handle_error("FIELD: #{header}, no es un campo valido para esta fila.")
          handle_error(I18n.t("#{LANG_PATH}.obsolete", header: header.to_s.humanize, type: obsolete_type.to_s))
        end
      end

      def validates_columns(headers)
        headers.each do |head|
          handle_error(I18n.t("#{LANG_PATH}.invalid_headers", header: head.to_s.humanize.upcase)) unless ALL_FIELDS.include?(head)
        end
      end

      def validates_dates(header, row)
        if DATE_FIELDS.include?(header) && row[header].present?
          handle_error(I18n.t("#{LANG_PATH}.invalid_date", header: header.to_s.humanize)) unless validate_format_date(row[header])
        end
      end

      def handle_error(message)
        errors = true
        error_messages["ROW#{position}".to_sym] = ["#{message}"]
        error_row << position - 1

      end

      def const(type)
        Lux::ProductImport::Structure.const_get(type)
      end
    end
  end
end
