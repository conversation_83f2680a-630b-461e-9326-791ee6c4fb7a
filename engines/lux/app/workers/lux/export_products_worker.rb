module Lux
  class ExportProductsWorker
    include Sidekiq::Worker
    sidekiq_options queue: :critical, retry: true

    HEADER = ['slug',
              'ID',
              'SKU Externo',
              'SKU Avenida',
              'EAN_CODE',
              'Valor en puntos',
              'Tope de descuento',
              'Titulo',
              'Descripcion',
              'Categoria',
              'Directorio de categoria',
              'Tipo de producto',
              'Nombre de propiedad 1',
              'Valor de propiedad 1',
              'Nombre de propiedad 2',
              'Valor de propiedad 2',
              'Nombre de propiedad 3',
              'Valor de propiedad 3',
              'Fabricante',
              'Precio',
              'Precio Sin Impuestos Nacionales',
              'Precio de oferta',
              'Precio de oferta Sin Impuestos Nacionales',
              'Fecha comienzo de oferta',
              'Fecha fin de oferta',
              'Fecha de disponibilidad',
              'Alto',
              'Largo',
              'Ancho',
              'Unidad de medida',
              'Peso',
              'Unidad de masa',
              'Iva',
              'Costo',
              'Stock',
              'Visibilidad de variante',
              'Visibilidad del proveedor',
              'Titulo del proveedor',
              'Fecha de creacion',
              'Cantidad vendida',
              'Imagenes',
              'Ficha Técnica']

    def perform(shop_id, export_id, store_id = nil)
      store = store_id.present? ? Mkp::Store.find(store_id) : nil
      shop = Mkp::Shop.find(shop_id)
      products = shop.products.map(&:id)
      # Si es una store de BNA
      if [41, 43, 47].include?(store_id)
        HEADER.push('Origen del Producto', 'Marca', 'Eficiencia Energetica')
      end

      if (broken_products = broken_products_to_export(products)).present?
        message = "[EXPORTACION - PRODUCTOS] N° #{export_id}, correspondiente al shop #{shop.title} (#{shop.id}),
                  no podra exportar los siguientes productos, ya que los mismos se encuentran dañados:
                  #{broken_products}"
        RocketChatNotifier.notify message, webhook_dest: :platform
      end

      csv_content = CSV.generate do |csv|
        csv << build_header(store)
        variants = Mkp::Variant
                   .where(product_id: products)
                   .includes(:shop,:costs)
                   .includes(product: %i[packages category])
                   .order('mkp_variants.product_id desc')

        @lists_manufacturer = Mkp::Manufacturer.pluck(:id, :name).to_h

        broken_variants = []
        variants.each_with_index do |variant, index|
          begin
            if index == 0 || variant.product_id != variants[index - 1].product_id
              @product = variant.product
              @dimensions = {
                width: @product.packages.to_a.sum(&:width),
                length: @product.packages.to_a.sum(&:length),
                height: @product.packages.to_a.sum(&:height),
                weight: @product.packages.to_a.sum(&:weight)
              }
            end
            csv << build_content_row(variant, store)
          rescue => e
            broken_variants << variant.id
            next
          end
        end
        if !broken_variants.blank?
          message = "[EXPORTACION - VARIANTES] N° #{export_id} correspondiente al shop #{shop.title} (#{shop.id}),
          no podra exportar las siguientes variantes, ya que las mismas se encuentran dañadas:  #{broken_variants}"
          RocketChatNotifier.notify message, webhook_dest: :platform
        end
      end
      return csv_content
    end

    private

    def broken_products_to_export(product_ids)
      product_ids_by_variants = Mkp::Variant.where(product_id: product_ids).map(&:product_id)

      product_ids - product_ids_by_variants
    end

    def build_header(store)
      if store.present?
        store.product_approval ? HEADER << 'Approval status' : HEADER
      else
        HEADER
      end
    end

    def build_content_row(variant, store)
      row = [
        @product.slug,
        @product.id,
        sanatize(variant.sku),
        variant.gp_sku,
        variant.try(:ean_code),
        variant.try(:points_price),
        variant.try(:discount_top),
        sanatize(@product.title).gsub(/[\r\n]?/, ''),
        sanatize(@product.description.gsub(/[\r\n]?/, '').tr('"', "'")),
        sanatize(@product.category.try(:name)),
        sanatize(@product.category.try(:full_path)),
        sanatize(@product.try(:transaction_type)),
        variant.property_name(1),
        sanatize(variant.property_value(1)),
        variant.property_name(2),
        parse_number(variant.property_value(2)),
        variant.property_name(3),
        parse_number(variant.property_value(3)),
        @lists_manufacturer[@product.manufacturer_id],
        @product.regular_price.to_f,
        @product.regular_price_without_taxes.try(:to_f),
        @product.sale_price.try(:to_f),
        @product.sale_price_without_taxes.try(:to_f),
        @product.sale_on.try(:strftime, '%d/%m/%Y'),
        @product.sale_until.try(:strftime, '%d/%m/%Y'),
        @product.available_on.try(:strftime, '%d/%m/%Y'),
        data_or_dd(@dimensions[:height]),
        data_or_dd(@dimensions[:length]),
        data_or_dd(@dimensions[:width]),
        data_or_dd(@product.length_unit),
        data_or_dd(@dimensions[:weight]),
        data_or_dd(@product.mass_unit),
        @product.iva,
        variant.current_cost.to_f,
        variant.quantity,
        @product.available? && variant.quantity.positive? ? 'visible' : 'no visible',
        variant.shop.visible? ? 'visible' : '--',
        data_or_dd(variant.shop.title.to_s),
        variant.created_at.strftime('%d-%m-%Y'),
        @product.sold_count,
        @product.pictures.count
      ]

      ##FICHA TECNICA
      data_sheet = []
      ["data", "data_shipment"].each do |field|
        unless @product.data_to_simple_hash(field).blank?
          @product.data_to_simple_hash(field).each do |name, value|
            data_sheet << "#{name} | #{parse_hash_value(value)}"
          end
        end
      end
      row << data_sheet.join

      # Si es una store de BNA
      if store.present? && [41, 43, 47].include?(store.id)
        row.push(@product.origin_of_product, @product.brand, @product.energy_efficiency)
      end

      if store.present? && store.product_approval
        product_store = store.product_stores.find_by(product: @product)
        status = product_store.present? ? product_store.status : ' - '
        row << status
      end

      row
    end

    def sanatize(field)
      field.gsub(/[^A-Za-z0-9_.\- ]/, '') if field.present?
    end

    def parse_number(number)
      number.tr(',', '.') if number.present?
    end

    def data_or_dd(data)
      data.blank? || data == 0 ? '--' : data
    end

    def parse_hash_value(value)
      return I18n.t('mkp.variants.v5.partials.does') if value == "1"
      return I18n.t('mkp.variants.v5.partials.doesnt') if value == "0"
      value
    end
  end
end

# Headers en ingles
# 'slug',
# 'ID',
# 'SKU',
# 'AV SKU',
# 'EAN_CODE',
# 'Points Price',
# 'Discount Top',
# 'Title',
# 'Description',
# 'Category',
# 'Category Full Path',
# 'Product Type',
# 'Property Name 1',
# 'Property Value 1',
# 'Property Name 2',
# 'Property Value 2',
# 'Property Name 3',
# 'Property Value 3',
# 'Manufacturer',
# 'Price',
# 'Price Without Taxes',
# 'Sale Price',
# 'Sale Price Without Taxes',
# 'Sale start date',
# 'Sale end date',
# 'Available On',
# 'Height',
# 'Length',
# 'Width',
# 'Length Unit',
# 'Weight',
# 'Mass Unit',
# 'Iva',
# 'Current Cost',
# 'Stock',
# 'Variants Visibility',
# 'Shop Visibility',
# 'Shop Title',
# 'Created At',
# 'Sold Quantity',
# 'Images'
