module Lux
  class IntegrationSaleNotificationWorker
    include Sidekiq::Worker

    sidekiq_options retry: false

    def perform(shop_id, suborder_id, integration_name)
      shop = Mkp::Shop.find shop_id
      suborder = Mkp::Suborder.find suborder_id

      integration = ("Mkp::Integration::#{integration_name}".constantize)
                      .for(shop)

      suborder.items.each do |order_item|
        order_item.product.external_objects.each do |external_object|
          if integration_name == external_object.integration_name.camelcase
            integration.remote_sync(order_item.product, external_object)
          end
        end
      end
    end
  end
end
