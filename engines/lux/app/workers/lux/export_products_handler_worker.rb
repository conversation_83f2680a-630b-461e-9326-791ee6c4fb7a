module Lux
  class ExportProductsHandlerWorker
    include Sidekiq::Worker
    sidekiq_options queue: Socket.gethostname, retry: false

    def perform(shop_id, export_id)
      begin
        data = ExportProductsWorker.new.perform(shop_id, export_id)

        export = Mkp::Export.find export_id
        #csv = export.csv_file = File.open(path_file)
        csv = create_file(path_file, data)
        file = StringIO.new(csv)
        export.csv_file = file
        export.title = filename
        export.csv_file.instance_write(:content_type, 'text/csv')
        export.csv_file.instance_write(:file_name, filename)
        export.url = url_path
        export.save!
      rescue StandardError => e
        message = "[EXPORTACION - PRODUCTOS] N° #{export_id}, correspondiente al shop - #{shop_id}(#{e})"
        RocketChatNotifier.notify message, webhook_dest: :platform
      end

      #export.update_attributes(url: url_path, title: filename)
    end

    private

    def path_file
      @path_file ||= "#{Rails.root.to_s}/public/#{filename}"
    end

    def filename
      @filename ||= "products-#{security}-#{Time.current.strftime('%d-%m-%Y-%H-%M')}.csv"
    end

    def url_path
      'https://' + HOSTNAME + '/' + filename
    end

    def security
      SecureRandom.hex(8)
    end

    def create_file(filename, data)
      CSV.open(filename, "wb") do |csv|
        data.each_line do |line|
          csv << line.split(',')
        end
      end
    end
  end
end
