gp.Lux.NestedSelectView = gp.App.View.extend({
  className: 'large-4 medium-4 small-12 columns end',

  events: {
    'change select':      'changeOption'
  },

  initialize: function(options){
    this.options = options || {}
    this.options.template = this.options.template || this.template('select-tag')
    _.bindAll(this, 'builtOptions', 'bubbleUpSelected')
  },

  render: function(){
    this.$el.html(this.options.template({id: this.options.category_id}))
    this.collection.fetch({ data:{category_id: this.options.category_id} })
    this.collection.on('fetch:success', this.builtOptions)
    return this
  },

  changeOption: function() {
    var $option = this.$("select option:selected"),
    has_children = $option.data("children")
    if (has_children) {
      this.handleChildrenCategories($option.val())
    } else {
      this.trigger("option:selected", $option)
      this.removeDown()
    }
  },

  handleChildrenCategories: function(category_id){
    if (this.children_categories){
      this.children_categories.removeDown()
      this.children_categories.options.category_id = category_id
      this.children_categories.render()
    } else {
      this.children_categories = new gp.Lux.NestedSelectView({
                                    template: this.options.template,
                                    collection: new gp.Lux.Categories(),
                                    category_id: category_id
                                  })
      this.children_categories.setup_data = this.setup_data
      this.children_categories.on('option:selected', this.bubbleUpSelected)
      this.$el.after(this.children_categories.render().$el)
    }
  },

  builtOptions: function(scope){
    var that = this,
        $select = that.$('select'),
        options = scope.models[0].get('children')
    options.unshift({id:'',name: I18n.t('javascripts.lux.products.pick_one')})
    $select.html('')
    _.each(options, function(o){
      var html = '<option '
      if (typeof(o.has_children) !== 'undefined') {
        html += 'data-children="'+o.has_children+'" '
      }
      html += 'value="'+o.id+'">'+o.name+'</option>'
      $select.append(html)
    })
    Foundation.libs.forms.append_custom_select(0, $select)
    if(this.setup_data){
      this.setup()
    }
    that.collection.off('fetch:success', this.builtOptions)
  },

  removeDown: function() {
    if (this.children_categories) {
      this.children_categories.off('option:selected', this.bubbleUpSelected)
      this.children_categories.removeDown()
      this.children_categories.remove()
      delete this.children_categories
    }
  },

  bubbleUpSelected: function(option) {
    this.trigger('option:selected', option);
  },

  setup: function(){
    var options = this.$('select option'),
        options_ids = _.map(options, function(o){ return $(o).val() })
    if(this.setup_data){
      var option_selected = _.intersection(options_ids, this.setup_data)
      if (option_selected.length){
        var index = options_ids.indexOf(option_selected[0])
        this.setup_data = _.difference(this.setup_data, option_selected)
        $(options[index]).prop("selected",true)
        Foundation.libs.forms.refresh_custom_select(this.$('select'), true)
      }
    }
    this.changeOption()
  }
})
