;if( gp.controller.product_imports && gp.action.progress ) $(function(){
  var $item = $('.product-imports-item')
  var $progress = $item.find('.product-imports-progress')
  var $status = $item.find('.product-imports-item-status')
  var $errors = $item.find('.product-imports-item-errors')
  var $summary = $('.product-imports-summary')
  var $backToProducts = $('.back-to-products')
  var $backToImport = $('.back-to-import-products')

  function setInProgress(pct){
    $progress.attr('data-status', 'in_progress')
      .find('.meter').css('width', pct + '%')
    $status
      .attr('data-status', 'in_progress')
      .html('in progress...')
  }

  function setComplete(products, variants, strategy){
    $progress.attr('data-status', 'complete')
      .find('.meter').css('width', '100%')
    $status
      .attr('data-status', 'complete')
      .html('completed')
    $summary
      .show()
      .html('You have succesfully ' + strategy + 'd ' + products + ' product(s) with ' + variants + ' variant(s)')
    $backToProducts.css('display', 'inline-block')
  }

  function setError(errorsHtml){
    $progress.fadeOut()
    $status
      .attr('data-status', 'complete')
      .html('Importación completa')
    if( errorsHtml ){
      $errors.show().html(errorsHtml)
    }
    $backToImport.css('display', 'inline-block')
    $backToProducts.css('display', 'inline-block')
  }

  function fetch(){
    $.ajax({
      url: 'status',
      data: {strategy: $(location).attr('href').split('?')[1].split('=')[1]}
    }).done(function(res){
      if( res.data.status === 'in_progress' ) {
        setInProgress(res.data.pct_complete)
        _.delay(fetch, 1500)
      } else if( res.data.status === 'complete' ) {
        setComplete(res.data.products, res.data.variants, res.data.strategy)
      } else {
        setError(res.data.errors_html, res.data.products, res.data.variants, res.data.strategy)
      }
    }).fail(setError)
  }

  fetch()
});
