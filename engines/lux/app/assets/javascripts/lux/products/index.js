;if( gp.controller.products && gp.action.index ) $(function(){

  // Initialize Products
  ;(function(){
    gp.products = new gp.Lux.Products()
    var shop_id = $('meta[name="shop_id"]').attr('content')
    if( shop_id ) {
      gp.products.setParams({ shop_id: shop_id, network: gp.network })
    }
  })()

  var ProductsListPaginator = gp.App.View.extend({
    className: 'products-list-pagination',

    events: {
      'click [data-page]': 'showPageEvent',
      'click .prev': 'showPrevPage',
      'click .next': 'showNextPage'
    },

    initialize: function(options) {
      _.bindAll(
        this,
        'reset',
        'showPage',
        'showNextPage',
        'showPrevPage',
        'showPageEvent'
      )

      this.listenTo(options.collection, 'reset', this.reset)
    },

    reset: function() {
      var that = this, c = this.collection

      var total = +c.info.total_pages
      if( _.isNumber(total) && total > 1 ) {
        var links = []
        var page = +c.params.current_page;
        var max_show = 9, sib = Math.floor(max_show / 2) / 2;
        var addPage = function(p) {
          if( _.isArray(p) ) _.each(p, addPage);
          if( p === 0 ) {
            links.push('<li><span>...</span></li>');
          } else if( p >= 1 && p <= total ) {
            var className = page === p ? ' class="selected"' : '';
            links.push('<li'+className+' data-page="'+p+'">'+p+'</li>');
          }
        }
        if( page > 1 ) links.push('<li class="prev">&laquo;</li>');
        if( total > max_show ) {
          var half = Math.floor(max_show / 2);
          var start = page - half > 0 ? page - half : 1;
          pages = _.range( start, start + max_show + 1 );
          while( _.last(pages) > total ) {
            pages.splice(-1, 1);
            var val = pages[0] - 1;
            pages.splice(0, 0, val);
          }
          if( _.first(pages) != 1 ) {
            pages.splice(0, 2, 1, 0);
          }
          if( _.last(pages) != total ) {
            pages.splice(-2, 2, 0, total);
          }
          addPage(pages);
        } else {
          _.times(total, function(i){
            addPage( i + 1 );
          });
        }
        if( page < total ) links.push('<li class="next">&raquo;</li>');
        links = links.join('');
        this.$el.html(links);
        this.show()
      } else {
        this.$el.html('');
        this.hide()
      }

      return this
    },

    showPage: function(page) {
      this.collection.fetchPage(page)
    },

    showNextPage: function() {
      this.collection.fetchNextPage()
    },

    showPrevPage: function() {
      this.collection.fetchPrevPage()
    },

    showPageEvent: function(e) {
      var $el = $(e.currentTarget)
      this.showPage(+$el.attr('data-page'))
    }
  })

  var ProductsListView = gp.App.View.extend({
    className: 'products-list',

    itemTemplate: null,

    events: {
      'keyup .products-list-search': 'searchEvent',
      'click .refinements button': 'refinementClick',
      'click .list-item-category:not(.editing)': 'editCategoryEvent',
      'click .list-item-sports:not(.editing)': 'editSportsEvent',
      'click .list-item-manufacturer:not(.editing)': 'editManufacturerEvent',
      'click .list-item-title:not(.editing)': 'editTitleEvent',
      'click .list-item-regular_price:not(.editing)': 'editRegularPriceEvent',
      'click .list-item-sale_price:not(.editing)': 'editSalePriceEvent',
      'click .list-item-sale_on:not(.editing)': 'editSaleOnEvent',
      'click .list-item-sale_until:not(.editing)': 'editSaleUntilEvent',
      'click .list-item-actions a.should-return': 'actionEvent',
      'click .orderable': 'orderEvent',
      'change .products-list-for-category-search': 'searchCategorySelect'
    },

    initialize: function(options){
      var that = this
      var c = this.collection = options.collection

      this.itemTemplate = this.template('products-list-item', { variable: 'data' })
      this.$list = this.$('.products-list-list')
      this.$refinements = this.$('.refinements button')

      this.listenTo(c, 'request', function(collection){
        if( collection === c ) that.showLoader()
      })
      this.listenTo(c, 'sync', this.hideLoader)
      this.listenTo(c, 'reset', this.reset)
      this.listenTo(c, 'remove', this.remove)

      new ProductsListPaginator({
        el: this.$('.'+ProductsListPaginator.prototype.className),
        collection: c
      })
    },

    reset: function(collection){
      var that = this
      if( collection.length ){
        this.$el.removeClass('empty')
        this.$list.html('')
        collection.each(function(m){
          that.add(m)
        })
      } else {
        this.$el.addClass('empty')
      }
      this.initRefinements()
      this.initSearch()
      this.initOrder()
      this.initCategorySelect()
    },

    add: function(model){
      var data = model.toJSON()
      _.extend(data, {
        sale_price_text: data.has_sale? data.currency_symbol + data.sale_price : '-',
        sale_on_text: data.has_sale ? data.sale_on_text : '',
        sale_until_text: data.has_sale ? data.sale_until_text : '',
        category_id: model.getCategoryId(),
        gp_sku: data.gp_sku,
        second_category_id: model.getSecondCategoryId(),
        category_name: model.getCategoryName(),
        second_category_name: model.getSecondCategoryName(),
        sports_names: model.getSportsNames(),
        manufacturer_name: model.getManufacturerName()
      })
      var $item = this.itemTemplate(data)
      this.$list.append($item)
    },

    remove: function(model){
      this.$list.find('#product-'+model.id).remove()
    },

    initSearch: function(){
      if( this.collection.params.query ){
        this.$('.products-list-search').val(this.collection.params.query)
      }
    },

    searchEvent: function(e){
      var keyCode = (e.keyCode ? e.keyCode : e.which)
      if( keyCode !== 13 ) return

      var $input = $(e.currentTarget)
      this.collection.search($input.val())
    },

    initRefinements: function(){
      var that = this, c = this.collection
      this.$refinements.removeClass('active')
      if( !c.params || !c.params.refinement || !c.params.refinement.length ) {
        return
      }
      var r = c.params.refinement
      that.$refinements.filter('[data-refinement="'+r+'"]').addClass('active')
    },

    refinementClick: function(e){
      var $el = $(e.currentTarget)
      if( $el.hasClass('active') ) {
        this.collection.setRefinement(null).fetch()
        $el.removeClass('active')
      } else {
        this.$refinements.removeClass('active')
        this.collection.setRefinement($el.attr('data-refinement')).fetch()
        $el.addClass('active')
      }
    },

    initCategorySelect: function(){
      if( this.collection.params.category_id ){
        this.$('.products-list-for-category-search').val(this.collection.params.category_id)
      }
    },

    searchCategorySelect: function(e){
      categoryId = this.$('.products-list-for-category-search').val();
      this.collection.setCategoryId(categoryId);
    },

    editTitleEvent: function(e){
      var $el = $(e.currentTarget)
      var origValue = $el.attr('data-value')
      var productId = +$el.parents('.products-list-item').attr('data-product-id')
      var model = this.collection.get(productId)
      if (model.attributes.has_integration) return;
      var $form = $('<form/>')
      var $input = $('<input/>', {
        type: 'text',
        value: origValue,
        required: true,
        max: 1024
      })
      $form.append($input)

      $el
        .removeClass('not-editing')
        .addClass('editing')
        .html($form)
        .append('<span class="close">x</span>')

      function close(){
        $form.add($input).off().remove()
        $el
          .removeClass('editing')
          .addClass('not-editing')
          .html(model.get('title'))
      }

      $el.on('click', '.close', close)
      $input.on('keyup', function(e){
        var keyCode = (e.keyCode ? e.keyCode : e.which)
        if( keyCode !== 13 /*ENTER*/ ) return
        $form.submit()
      })
      $form.on('submit', function(e){
        e.preventDefault(), e.stopPropagation()
        if( !$input[0].checkValidity || $input[0].checkValidity() ) {
          var val = $input.val()
          $el.off('click', '.close')
          $input.prop('disabled', true)
          model.save({ title: val }, {
            success: function(){
              $el.attr('data-value', val)
              close()
            }
          })
        }
      })
    },

    _$mfrSelector: null,
    newManufacturerSelect: function(){
      if( !this._$mfrSelector ) {
        var sel = ['<select>']
        gp.manufacturers.each(function(c){
          sel.push('<option value="'+c.id+'">'+c.get('name')+'</option>')
        })
        sel.push('</select>')
        this._$mfrSelector = $(sel.join(''))
      }
      return this._$mfrSelector.clone()
    },

    editManufacturerEvent: function(e){
      var $el = $(e.currentTarget)
      var origValue = +$el.attr('data-value')
      var productId = +$el.parents('.products-list-item').attr('data-product-id')
      var model = this.collection.get(productId)
      if (model.attributes.has_integration) return;
      var $sel = this.newManufacturerSelect().val(origValue)

      $el
        .removeClass('not-editing')
        .addClass('editing')
        .html($sel)
        .append('<span class="close">x</span>')

      $sel.selectize()
      var selectize = $sel[0].selectize
      selectize.open()
      selectize.focus()

      function close(){
        selectize.destroy()
        $sel.remove()
        $el
          .removeClass('editing')
          .addClass('not-editing')
          .html(model.getManufacturerName())
      }

      $el.on('click', '.close', close)
      $sel.on('change', function(){
        var val = +$sel.val()
        if( !val ) return
        selectize.disable()
        $el.off('click', '.close')
        model.save({ manufacturer_id: val }, {
          success: function(){
            $el.attr('data-value', val)
            close()
          }
        })
      })
    },

    _$catSelector: null,
    newCategorySelect: function(){
      if( !this._$catSelector ) {
        var sel = ['<select>']
        gp.categories.each(function(c){
          var name = _.map(gp.categories.ancestorsOf(c), function(a){
            try {
              return a.get('name');
            } catch (error) {
            }
          })
          name.push(c.get('name'))
          name = name.join(' / ')
          sel.push('<option value="'+c.id+'">'+name+'</option>')
        })
        sel.push('</select>')
        this._$catSelector = $(sel.join(''))
      }
      return this._$catSelector.clone()
    },

    editCategoryEvent: function(e){
      var $el = $(e.currentTarget)
      var categoryId = +$el.attr('data-value')
      var productId = +$el.parents('.products-list-item').attr('data-product-id')
      var model = this.collection.get(productId)
      if (model.attributes.has_integration) return;
      var $sel = this.newCategorySelect().val(categoryId)

      $el
        .removeClass('not-editing')
        .addClass('editing')
        .html($sel)
        .append('<span class="close">x</span>')

      $sel.selectize()
      var selectize = $sel[0].selectize
      selectize.open()
      selectize.focus()

      function close(){
        selectize.destroy()
        $sel.remove()
        $el
          .removeClass('editing')
          .addClass('not-editing')
          .html(model.getCategoryName())
      }

      $el.on('click', '.close', close)
      $sel.on('change', function(){
        var val = +$sel.val()
        if( !val ) return
        selectize.disable()
        $el.off('click', '.close')
        model.save({ category_id: val }, {
          success: function(){
            $el.attr('data-value', val)
            close()
          }
        })
      })
    },

    _$sportsSelector: null,

    newSportsSelect: function(){
      if( !this._$sportsSelector ) {
        var sel = ['<select multiple="multiple">']
        gp.sports.each(function(c){
          var name = _.map(gp.sports.ancestorsOf(c), function(a){
            return a.get('name')
          })
          name.push(c.get('name'))
          name = name.join(' / ')
          sel.push('<option value="'+c.id+'">'+name+'</option>')
        })
        sel.push('</select>')
        this._$sportsSelector = $(sel.join(''))
      }
      return this._$sportsSelector.clone()
    },

    editSportsEvent: function(e){
      var $el = $(e.currentTarget)
      var sportsIds = (sportsIds = $el.attr('data-value')) && sportsIds.split(',')
      var productId = +$el.parents('.products-list-item').attr('data-product-id')
      var model = this.collection.get(productId)
      if (model.attributes.has_integration) return;
      var $sel = this.newSportsSelect().val(sportsIds)

      $el
        .removeClass('not-editing')
        .addClass('editing')
        .html($sel)
        .append('<span class="close">x</span>')

      $sel.selectize()
      var selectize = $sel[0].selectize
      selectize.open()
      selectize.focus()

      function close(){
        selectize.destroy()
        $sel.remove()
        $el
          .removeClass('editing')
          .addClass('not-editing')
          .html(model.getSportsNames())
      }

      $el.on('click', '.close', close)
      function save(){
        var val = $sel.val()
        if( !val ) return
        selectize.disable()
        $el.off('click', '.close')
        model.save({ sports_ids: val.length ? val : null}, {
          success: function(){
            $el.attr('data-value', val)
            close()
          }
        })
      }
      function onKeyUp(e){
        var keyCode = (e.keyCode ? e.keyCode : e.which)
        if( keyCode !== 13 /*ENTER*/ ) return
        selectize.$control_input.off('keyup', onKeyUp)
        save()
      }
      selectize.on('dropdown_close', function(){
        selectize.$control_input.on('keyup', onKeyUp)
      })
      selectize.on('dropdown_open', function(){
        selectize.$control_input.off('keyup', onKeyUp)
      })
    },

    editRegularPriceEvent: function(e){
      var $el = $(e.currentTarget)
      var origValue = $el.attr('data-value')
      var productId = +$el.parents('.products-list-item').attr('data-product-id')
      var model = this.collection.get(productId)
      if (model.attributes.has_integration) return;
      var $form = $('<form/>')
      var $input = $('<input/>', {
        type: 'text',
        value: origValue,
        required: true,
        pattern: '\\d+(\\.\\d+)?',
        title: 'Must be an entire number with optional decimals separated by a point.'
      })
      $form.append($input)

      $el
        .removeClass('not-editing')
        .addClass('editing')
        .html($form)
        .append('<span class="close">x</span>')

      function close(){
        $form.add($input).off().remove()
        $el
          .removeClass('editing')
          .addClass('not-editing')
          .html(model.get('currency_symbol') + model.get('regular_price'))
      }

      $el.on('click', '.close', close)
      $input.on('keyup', function(e){
        var keyCode = (e.keyCode ? e.keyCode : e.which)
        if( keyCode !== 13 /*ENTER*/ ) return
        $form.submit()
      })
      $form.on('submit', function(e){
        e.preventDefault(), e.stopPropagation()
        if( !$input[0].checkValidity || $input[0].checkValidity() ) {
          var val = $input.val()
          $el.off('click', '.close')
          $input.prop('disabled', true)
          model.save({ regular_price: val }, {
            success: function(){
              $el.attr('data-value', val)
              close()
            }
          })
        }
      })
    },

    editSalePriceEvent: function(e){
      var $el = $(e.currentTarget)
      var origValue = $el.attr('data-value')
      var productId = +$el.parents('.products-list-item').attr('data-product-id')
      var model = this.collection.get(productId)
      var $form = $('<form/>')
      var $input = $('<input/>', {
        type: 'text',
        value: origValue,
        pattern: '\\d+(\\.\\d+)?',
        title: 'Must be an entire number with optional decimals separated by a point.'
      })
      $form.append($input)

      $el
        .removeClass('not-editing')
        .addClass('editing')
        .html($form)
        .append('<span class="close">x</span>')

      function close(){
        $form.add($input).off().remove()
        $el
          .removeClass('editing')
          .addClass('not-editing')
          .html(model.get('currency_symbol') + model.get('sale_price'))
      }

      $el.on('click', '.close', close)
      $input.on('keyup', function(e){
        var keyCode = (e.keyCode ? e.keyCode : e.which)
        if( keyCode !== 13 /*ENTER*/ ) return
        $form.submit()
      })
      $form.on('submit', function(e){
        e.preventDefault(), e.stopPropagation()
        if( !$input[0].checkValidity || $input[0].checkValidity() ) {
          var val = $input.val()
          $el.off('click', '.close')
          $input.prop('disabled', true)
          model.save({ sale_price: val }, {
            success: function(){
              $el.attr('data-value', val)
              close()
            }
          })
        }
      })
    },

    editSaleOnEvent: function(e){
      var $el = $(e.currentTarget)
      var productId = +$el.parents('.products-list-item').attr('data-product-id')
      var model = this.collection.get(productId)
      var origValue = $el.attr('data-value') || $el.text()

      if( !origValue ){
        var t = (new Date)
        var newValue = t.getFullYear()+'-'+t.getMonth()+'-'+t.getDate()
      }

      var $input = $('<input/>', {
        type: 'text',
        value: origValue || newValue,
        title: 'Date of when the sale is going to start'
      })

      $el
        .removeClass('not-editing')
        .addClass('editing')
        .html($input)
        .append('<span class="close">x</span>')

      $input.fdatepicker({format: 'yyyy-mm-dd'})

      function close(){
        $input.off().remove()
        var date = model.get('sale_on') && moment(model.get('sale_on')).format('YYYY-MM-DD')
        $el
          .removeClass('editing')
          .addClass('not-editing')
          .html(date || '')
      }

      $el.on('click', '.close', close)
      $input.on('keyup', function(e){
        var keyCode = (e.keyCode ? e.keyCode : e.which)
        if( keyCode !== 13 /*ENTER*/ ) return
        $input.trigger('changeDate')
      })

      $input.on('changeDate', function(e){
        var val = $input.val()
        $el.off('click', '.close')
        $input.prop('disabled', true)
        model.save({ sale_on: val }, {
          success: function(){
            $el.attr('data-value', val)
            close()
          }
        })
      })
    },

    editSaleUntilEvent: function(e){
      var $el = $(e.currentTarget)
      var productId = +$el.parents('.products-list-item').attr('data-product-id')
      var model = this.collection.get(productId)
      var origValue = $el.attr('data-value') || $el.text()

      if( !origValue ){
        var t = (new Date)
        var newValue = t.getFullYear()+'-'+t.getMonth()+'-'+t.getDate()
      }

      var $input = $('<input/>', {
        type: 'text',
        value: origValue || newValue,
        title: 'Date of when the sale is going to end'
      })

      $el
        .removeClass('not-editing')
        .addClass('editing')
        .html($input)
        .append('<span class="close">x</span>')

      $input.fdatepicker({format: 'yyyy-mm-dd'})

      function close(){
        var date = model.get('sale_until') && moment(model.get('sale_until')).format('YYYY-MM-DD')
        $input.off().remove()
        $el
          .removeClass('editing')
          .addClass('not-editing')
          .html(date || '')
      }

      $el.on('click', '.close', close)
      $input.on('keyup', function(e){
        var keyCode = (e.keyCode ? e.keyCode : e.which)
        if( keyCode !== 13 /*ENTER*/ ) return
        $input.trigger('changeDate')
      })
      $input.on('changeDate', function(e){
        var val = $input.val()
        $el.off('click', '.close')
        $input.prop('disabled', true)
        model.save({ sale_until: val }, {
          success: function(){
            $el.attr('data-value', val)
            close()
          }
        })
      })
    },

    actionEvent: function(e){
      if( window.location.hash ) {
        var $link = $(e.currentTarget)
        var href = $link.attr('href')
        var params = gp.Helpers.url.toParams({
          return_to: window.location.href
        })
        $link.attr('href', href+params)
      }
    },

    _$ordering: null,
    initOrder: function(){
      var c = this.collection
      if( !c.params.order_column ) return
      this._$ordering = this.$('.orderable[data-order-column="'+c.params.order_column+'"]')
        .addClass('ordering')
        .attr('data-order-direction', c.params.order_direction)
    },

    orderEvent: function(e){
      var $column = $(e.currentTarget)
      var column = $column.attr('data-order-column')

      if( !$column.is(this._$ordering) ) {
        if( this._$ordering ) {
          this._$ordering
            .removeClass('ordering')
            .attr('data-order-direction', null)
        }
        var direction = 'desc'
        $column
          .addClass('ordering')
          .attr('data-order-direction', direction)
        this._$ordering = $column

        this.collection.setParams({
          order_column: column,
          order_direction: direction
        }).fetch()
      } else {
        var direction = $column.attr('data-order-direction')
        if( direction === 'desc' ) {
          direction = 'asc'
          $column.attr('data-order-direction', direction)

          this.collection.setParams({
            order_column: column,
            order_direction: direction
          }).fetch()
        } else {
          $column
            .removeClass('ordering')
            .attr('data-order-direction', null)
          this._$ordering = null

          this.collection.setParams({
            order_column: null,
            order_direction: null
          }).fetch()
        }
      }
    }
  })

  var Router = Backbone.Router.extend({
    routes: {
      '*params': 'index',
    },

    initialize: function(){
      var that = this
      that.navigate(gp.Helpers.url.toParams(gp.products.params), { trigger: false })
      this.listenTo(gp.products, 'params:change', function(){
        that.navigate(gp.Helpers.url.toParams(gp.products.params), { trigger: false })
      })
    },

    index: function(params){
      params = gp.Helpers.url.toObject(params)
      current_page = 1
      if( params ) {
        if( params.shop_id ) delete params.shop_id
        if( params.network ) delete params.network
      }
      get = Backbone.history.getFragment();
      if (get.search("current_page") !== -1){
        current_page = get.substring(get.search("current_page"))
        current_page = current_page.substring( current_page.search("=") + 1 , current_page.search("&") )
      }
      if (current_page !== 'current_page='){
        params.current_page = current_page
      } else{
        params.current_page = 1
      }
      gp.products.setParams(params).fetch()
    }
  })

  var productsListView = new ProductsListView({
    el: $('.'+ProductsListView.prototype.className),
    collection: gp.products
  })

  var router = new Router()

  Backbone.history.start()
})
