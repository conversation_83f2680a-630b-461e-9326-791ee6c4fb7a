;(function() {
  StockTable = function StockTable(el, temporaryVariants, variantForm) {
    var $el = $(el)
    var variants
    var properties
    var propNames
    var isDone = false
    function populate() {
        generateModels() //generate each variant model by combination
        addDataToVariants()//adds matching variant data from db
    }

    function generateModels(){
        variants = []
        variants.push({quantity: 0, sku: '', ean_code: '', properties: {}, points_price: '', discount_top: ''})
        _.each(properties, function(values, key){
            var newVariants = []
            _.each(variants, function(v){
                _.each(values, function(value){
                    var newVariant= JSON.parse(JSON.stringify(v)) // deep clone
                    newVariant.properties[key] = value
                    newVariants.push(newVariant)
                })
            })
            variants = newVariants
        })
    }

    function buildVariants(){
        _.each(variants, function(variant){
            $el.append(buildVariant(variant))
        })
    }

    function buildVariant(variant){
      var el = $('<div><div class="row"><div class="small-12"><h4></h4></div></div>' +
        '<div class="row">' +
        '<div class="small-1 columns stock">Stock:</div>'  +
        '<div class="small-1 columns"><input type="text" placeholder="Stock" name="variants[][quantity]" value="' + variant.quantity + '"></div>' +
        '<div class="small-2 columns"><input type="text" placeholder="SKU" name="variants[][sku]" value="' + variant.sku + '"></div>' +
        '<div class="small-2 columns"><input type="text" placeholder="EAN" name="variants[][ean_code]" value="' + variant.ean_code + '"></div>' +
        '<div class="small-3 columns"><input type="text" placeholder="Points Price" name="variants[][points_price]" value="' + variant.points_price + '"></div>' +
        '<div class="small-3 columns"><input type="text" placeholder="Discount top" name="variants[][discount_top]" value="' + variant.discount_top + '"></div>' +
        '</div></div>')
        el.find("h4").append(buildVariantTitle(variant))
        if(!_.isUndefined(variant.id)) el.prepend('<input type="hidden" name="variants[][id]" value="' + variant.id + '"></input>')
        if(!_.isUndefined(variant.properties.color)) el.append(variantForm.buildColorPropertyInputs(variant.properties.color))
        if(!_.isUndefined(variant.properties.dimensions)) el.append(variantForm.buildDimensionsPropertyInputs(variant.properties.dimensions))
        if(!_.isUndefined(variant.properties.hardness)) el.append(variantForm.buildHardnessPropertyInputs(variant.properties.hardness))
        if(!_.isUndefined(variant.properties.length)) el.append(variantForm.buildLengthPropertyInputs(variant.properties.length))
        if(!_.isUndefined(variant.properties.material)) el.append(variantForm.buildMaterialPropertyInputs(variant.properties.material))
        if(!_.isUndefined(variant.properties.size)) el.append(variantForm.buildSizePropertyInputs(variant.properties.size))
        if(!_.isUndefined(variant.properties.percentage)) el.append(variantForm.buildPercentagePropertyInputs(variant.properties.percentage))
        if(!_.isUndefined(variant.properties.period)) el.append(variantForm.buildPeriodPropertyInputs(variant.properties.period))
        if(!_.isUndefined(variant.properties.coef)) el.append(variantForm.buildCoefPropertyInputs(variant.properties.coef))
        if(!_.isUndefined(variant.properties.date)) el.append(variantForm.buildDatePropertyInputs(variant.properties.date))
        if(!_.isUndefined(variant.properties.external)) el.append(variantForm.buildExternalPropertyInputs(variant.properties.external))
        if(!_.isUndefined(variant.properties.articulo)) el.append(variantForm.buildArticuloPropertyInputs(variant.properties.articulo))
        if(!_.isUndefined(variant.properties.payment_method)) el.append(variantForm.buildPayment_methodPropertyInputs(variant.properties.payment_method))
        return el
    }

    function buildVariantTitle(variant){
      return _.map(variant.properties, function(value, prop){
        return "<b>"+I18n.t('javascripts.lux.products.properties.' + prop) +":</b> "+
            (prop == "color" ? value.name : (prop == "noproperty" ? "" : value))
      }).join(", ")
    }

    function addDataToVariants(){
      if(!(gp.product && gp.product.variants)) return; // no data avaible
      _.each(variants, function(v){
        _.each(gp.product.variants, function(dbv){
          if(_.isObject(dbv.properties.color)) delete dbv.properties.color["slug_name"]
          if(_.isEqual(dbv.properties, v.properties) || v.properties.noproperty){
            v.id = dbv.id
            if(dbv.quantity != null) v.quantity = dbv.quantity
            if(dbv.sku != null) v.sku = dbv.sku
            if(dbv.ean_code != null) v.ean_code = dbv.ean_code
            if(dbv.points_price != null) v.points_price = dbv.points_price
            if(dbv.discount_top != null) v.discount_top = dbv.discount_top
          }
        })
      })
    }

    return {
      render: function(propertiesWithValues){
        properties = propertiesWithValues
        propNames = _.keys(propertiesWithValues)
        populate()
        buildVariants()
        isDone=true
      },
      reset: function(){
        $el.empty()
          isDone=false
      },
      done: function(){
        return isDone
      }
    }
  }
}).call(this)
