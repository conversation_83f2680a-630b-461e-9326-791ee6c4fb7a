;(function() {
  var ColorModel = Backbone.Model.extend({
    idAttribute: 'hex',
    defaults: {
      name: ''
    },

    set: function(attrs, options) {
      if (attrs && attrs.hex) attrs.hex = ColorModel.normalizeId(attrs.hex)
      return Backbone.Model.prototype.set.call(this, attrs, options)
    }
  },{
    normalizeId: function(id) {
      return tinycolor(id).toHexString()
    }
  })
  var ColorsCollection = Backbone.Collection.extend({
    model: ColorModel,

    get: function(obj) {
      if( typeof obj === 'string' && obj !== "" ) obj = this.model.normalizeId(obj)
      return Backbone.Collection.prototype.get.call(this, obj)
    }
  })

  gp.Lux.VariantsFormView = gp.App.View.extend({
    events: {
      'click .delete-property':        'deleteProperty',
      'click .delete-property-value':  'deletePropertyValue',
      'click .add-new-property':       'addNewProperty',
      'click .add-new-property-value': 'addNewPropertyValue',
      'click .manage-stock':           'buildStockTable'
    },

    initialize: function() {
      _.bindAll(
        this,
        'addColorProperty',
        'addNewProperty',
        'getSelectedProperties',
        'updatePropertySelector',
        'addNewPropertyValue',
        'loadDOMProperties',
        'loadDOMArticuloPropertyValues',
        'loadDOMColorPropertyValues',
        'loadDOMDimensionsPropertyValues',
        'loadDOMHardnessPropertyValues',
        'loadDOMLengthPropertyValues',
        'loadDOMMaterialPropertyValues',
        'loadDOMSizePropertyValues',
        'loadDOMPercentagePropertyValues',
        'loadDOMPeriodPropertyValues',
        'loadDOMDatePropertyValues',
        'loadDOMExternalPropertyValues',
        'loadDOMCoefPropertyValues',
        'loadDOMPayment_methodPropertyValues',
        'buildUnlimitedStockVariants',
        'buildPropertiesCombinations',
        'buildIdInput',
        'addVariantInputs',
        'buildArticuloPropertyInputs',
        'buildColorPropertyInputs',
        'buildDimensionsPropertyInputs',
        'buildHardnessPropertyInputs',
        'buildLengthPropertyInputs',
        'buildMaterialPropertyInputs',
        'buildSizePropertyInputs',
        'buildPercentagePropertyInputs',
        'buildPeriodPropertyInputs',
        'buildDatePropertyInputs',
        'buildCoefPropertyInputs',
        'buildExternalPropertyInputs',
        'buildPayment_methodPropertyInputs',
        'buildStockInput',
        'buildSKUInput',
        'displayProperties',
        'addArticulo',
        'addColor',
        'addDimensions',
        'addHardness',
        'addLength',
        'addMaterial',
        'addSize',
        'addPercentage',
        'addPeriod',
        'addCoef',
        'addDate',
        'addExternal',
        'addPayment_method',
        'getColorModel',
        'getColorFormValues',
        'enableColor',
        'disableColor',
        'createArticuloPropertyValue',
        'createColorPropertyValue',
        'createDimensionsPropertyValue',
        'createHardnessPropertyValue',
        'createLengthPropertyValue',
        'createMaterialPropertyValue',
        'createSizePropertyValue',
        'createPercentagePropertyValue',
        'createPeriodPropertyValue',
        'createDatePropertyValue',
        'createCoefPropertyValue',
        'createExternalPropertyValue',
        'createPayment_methodPropertyValue',
        'deleteProperty',
        'deletePropertyValue',
        'getPropertyMsg',
        'loadProperties',
        'defaultDisplayOptions',
        'initStockManagement',
        'submitStockManagement',
        'buildStockTable'
      )

      var colors = this.colors = new ColorsCollection(gp.availableColors)

      this.$propertiesList = this.$('.properties-list')

      this.$checkbox = this.$('.custom.checkbox')

      this.$variants = this.$('.variants')
      this.$propertySelector = $('#properties-selector')
      this.$manageStockCheckbox = $('#manage-stock')

      this.articuloTemplate = this.template('articulo-template')
      this.colorTemplate = this.template('color-template')
      this.dimensionsTemplate = this.template('dimensions-template')
      this.hardnessTemplate = this.template('hardness-template')
      this.lengthTemplate = this.template('length-template')
      this.materialTemplate = this.template('material-template')
      this.sizeTemplate = this.template('size-template')
      this.percentageTemplate = this.template('percentage-template')
      this.periodTemplate = this.template('period-template')
      this.dateTemplate = this.template('date-template')
      this.coefTemplate = this.template('coef-template')
      this.externalTemplate = this.template('external-template')
      this.paymentMethodTemplate = this.template('payment_method-template')

      this.propertyContainerTemplate = this.template('property-container-template')

      this.temporaryVariants = []

      this.$stockTable  = $stockTable = this.$('#stock-table')
      this.stockTable = stockTable = new StockTable('#stock-table', this.temporaryVariants, this)

      this.properties = this.loadProperties()

      this.displayProperties()

      // if(gp.product && gp.product.has_integration && gp.product.has_no_property )
        // this.$(".properties-list input").prop('readonly', true);

      if(_(this.properties).size() == 0)
        this.addColorProperty()

      this.initStockManagement()
      this.updatePropertySelector()
    },

    addColorProperty: function(e) {
      var $propertyContainer = this.propertyContainerTemplate({ name: 'color', msg: this.getPropertyMsg('color') })
      $propertyContainer.appendTo(this.$propertiesList)
      this.createColorPropertyValue()
    },

    addNewProperty: function(e) {
      var property = $('#properties-list-v3').val();

      var $propertyContainer = this.propertyContainerTemplate({name: property, msg: this.getPropertyMsg(property)})
      $propertyContainer.appendTo(this.$propertiesList)

      this['create' + gp.Helpers.string.capitalize(property) + 'PropertyValue'](e) //This...

      this.stockTable.reset()
      this.updatePropertySelector()
    },

    getSelectedProperties: function(){
      return _.map($('.properties-list').children(),function(el) {
        return $(el).attr('data-property')
      })
    },

    updatePropertySelector: function() {
      var selectedProperties = this.getSelectedProperties()
      var selector_data = _.map(_.difference(gp.availableProperties, selectedProperties), function(property, index) {
        return { id: property, selected: false, text: I18n.t('javascripts.lux.products.properties.' + property) }
      });
      var placeholder = I18n.t('javascripts.lux.products.click_here')
      this.$propertySelector.select2({ data: selector_data, placeholder: placeholder })
    },

    addNewPropertyValue: function(e) {
      var property = $(e.currentTarget).attr('data-property')

      propertyValue = this['create' + gp.Helpers.string.capitalize(property) + 'PropertyValue'](e)

      this.stockTable.reset()
    },

    loadDOMProperties: function() {
      var that = this
      var properties = {}

      $('.properties-list').children().each(function() {
        var $propertyValues = $(this).find('.property-values')
        var propertyName = $propertyValues.attr('data-property-name')

        properties[propertyName] = []

        if (propertyName === 'noproperty'){
          properties[propertyName].push(true)
        } else {
          $propertyValues.children().each(function() {
            var fnName = 'loadDOM' + gp.Helpers.string.capitalize(propertyName) + 'PropertyValues'
            var value = that[fnName]($(this))

            properties[propertyName].push(value)
          })
        }
      })

      return properties
    },


    loadDOMArticuloPropertyValues: function($el) {
      var value = $el.find('.articulo-value').val()
      return value
    },

    loadDOMColorPropertyValues: function($el) {
      var size = {}

      size['name'] = $el.find('.color-name-selector').val()
      size['hex'] = $el.find('.color-hex').val()

      return size
    },

    loadDOMDimensionsPropertyValues: function($el) {
      var value = $el.find('.dimensions-value').val()
      return value
    },

    loadDOMHardnessPropertyValues: function($el) {
      var value = $el.find('.hardness-value').val()
      return value
    },

    loadDOMLengthPropertyValues: function($el) {
      var value = $el.find('.length-value').val()
      return value
    },

    loadDOMMaterialPropertyValues: function($el) {
      var value = $el.find('.material-value').val()
      return value
    },

    loadDOMSizePropertyValues: function($el) {
      var value = $el.find('.size-value').val()
      return value
    },

    loadDOMPercentagePropertyValues: function($el) {
      var value = $el.find('.percentage-value').val()
      return value
    },

    loadDOMPeriodPropertyValues: function($el) {
      var value = $el.find('.period-value').val()
      return value
    },

    loadDOMDatePropertyValues: function($el) {
      var value = $el.find('.date-value').val()
      return value
    },

    loadDOMCoefPropertyValues: function($el) {
      var value = $el.find('.coef-value').val()
      return value
    },

    loadDOMExternalPropertyValues: function($el) {
      var value = $el.find('.external-value').val()
      return value
    },

    loadDOMPayment_methodPropertyValues: function($el) {
      var value = $el.find('.payment_method-value').val()
      return value
    },

    buildUnlimitedStockVariants: function() {
      var that = this
      var variants = this.buildPropertiesCombinations()

      _.each(variants, function(variant) {
        that.addVariantInputs(variant)
      })
    },

    buildPropertiesCombinations: function() {
      var that = this

      var properties = this.loadDOMProperties()

      var propertiesToCombine = _.map(_.keys(properties), function(property) {
        return _.map(properties[property], function(propertyValue) {
          var propertyValue_w_key = {}
          propertyValue_w_key[property] = propertyValue
          return propertyValue_w_key
        })
      })

      if (propertiesToCombine.length <= 0) return

      return gp.Helpers.array.cartesianProductOf.apply(null, propertiesToCombine)
    },

    buildIdInput: function(variant) {
      var input = ''
      var variantId
      var match
      _.each(gp.product.variants, function(variantWithId) {
        match = true
        _.each(variant, function(property) {
          var properties = variantWithId.properties[_.keys(property)[0]]
          if (_.keys(property)[0] == 'color') {
            properties = _.omit(properties, 'slug_name')
          }
          match = match && _.isEqual(properties, property[_.keys(property)[0]])
        })
        if (match) variantId = variantWithId.id
      })

      variantId = variantId || ''

      input += '<input type="hidden" name="variants[][id]" value="' + variantId + '"></input>'

      return input
    },

    addVariantInputs: function(variant) {
      var that = this
      var inputs = ''

      inputs += this.buildStockInput(999, 'hidden', '')

      if (gp.product) inputs += this.buildIdInput(variant)
      _.each(variant, function(property){
        var propertyName = _.keys(property)[0]
        inputs += that['build' + gp.Helpers.string.capitalize(propertyName) + 'PropertyInputs'](property[propertyName])
      })

      this.$variants.append('<div class="variant">' + inputs + '</div>')
    },

    buildArticuloPropertyInputs: function(articulo) {
      var input = '<input type="hidden" name="variants[][properties][articulo]" value="' + articulo + '"></input>'
      return input
    },

    buildColorPropertyInputs: function(color) {
      var inputs = '<input type="hidden" name="variants[][properties][color][hex]" value="'+color.hex+'"></input>'
      inputs += '<input type="hidden" name="variants[][properties][color][name]" value="'+color.name+'"></input>'
      return inputs
    },

    buildDimensionsPropertyInputs: function(dimensions) {
      var input = '<input type="hidden" name="variants[][properties][dimensions]" value="'+dimensions+'"></input>'
      return input
    },

    buildHardnessPropertyInputs: function(hardness) {
      var input = '<input type="hidden" name="variants[][properties][hardness]" value="'+hardness+'"></input>'
      return input
    },

    buildLengthPropertyInputs: function(length) {
      var input = '<input type="hidden" name="variants[][properties][length]" value="' + length + '"></input>'
      return input
    },

    buildMaterialPropertyInputs: function(material) {
      var input = '<input type="hidden" name="variants[][properties][material]" value="' + material + '"></input>'
      return input
    },

    buildSizePropertyInputs: function(size) {
      var input = '<input type="hidden" name="variants[][properties][size]" value="' + size + '"></input>'
      return input
    },

    buildPercentagePropertyInputs: function(percentage) {
      var input = '<input type="hidden" name="variants[][properties][percentage]" value="' + percentage + '"></input>'
      return input
    },

    buildPeriodPropertyInputs: function(period) {
      var input = '<input type="hidden" name="variants[][properties][period]" value="' + period + '"></input>'
      return input
    },

    buildDatePropertyInputs: function(date) {
      var input = '<input type="hidden" name="variants[][properties][date]" value="' + date + '"></input>'
      return input
    },

    buildCoefPropertyInputs: function(coef) {
      var input = '<input type="hidden" name="variants[][properties][coef]" value="' + coef + '"></input>'
      return input
    },

    buildExternalPropertyInputs: function(external) {
      var input = '<input type="hidden" name="variants[][properties][external]" value="' + external + '"></input>'
      return input
    },

    buildPayment_methodPropertyInputs: function(payment_method) {
      var input = '<input type="hidden" name="variants[][properties][payment_method]" value="' + payment_method + '"></input>'
      return input
    },

    buildNopropertyPropertyInputs: function() {
      // Do nothing.
    },

    buildStockInput: function(value, display, attr) {
      return '<input type="' + display +'" name="variants[][quantity]" value="' + value + '" attr-position="' + attr + '" class="stock"></input>'
    },

    buildSKUInput: function(value) {
      return '<input type="text" name="variants[][sku]" value="' + value + '"></input>'
    },

    displayProperties: function() {
      var that = this
      _.each(_.keys(this.properties), function(propertyName) {
        var $propertyContainer = that.propertyContainerTemplate({ name: propertyName, msg: that.getPropertyMsg(propertyName) })
        $propertyContainer.appendTo(that.$propertiesList)
        _.each(that.properties[propertyName], function(property) {
          that['add' + gp.Helpers.string.capitalize(propertyName)](property)
        })
      })

      // setup selected properties for existing products
      $.each(this.properties, function(index, value){
        if(index == 'percentage' || index == 'payment_method'){
          selects_list = $('.' + index + '-value')
          var i = 0
          $.each(selects_list, function(index, select_item) {
            select_item.value = value[i];
            i++;
          })
        }
      });
    },

    addArticulo: function(articulo) {
      var $list = $('.articulo-list')
      var $articulo = this.articuloTemplate(_.clone({ articulo: articulo }))

      $articulo.appendTo($list)

      return $articulo
    },

    addColor: function(color) {
      var that = this
      var _color = _.clone(color)
      var $color = this.colorTemplate(color)
      var $colorName = $color.find('.color-name')
      var $colorNameSelector = $color.find('.color-name-selector')
      var $colorHex = $color.find('.color-hex')
      var $colorList = $('.color-list')

      function getSelectedColor() {
        return { hex: $colorHex.val(), name: $colorName.val() }
      }
      function setSelectedColor(color) {
        $colorHexPreview.css('background-color', color.hex)
        $colorHex.val(color.hex)
        $colorName.add($colorNameSelector).val(color.name)
        $color.trigger('color:change', color)
      }

      var $colorHexPreview = $color.find('.color-hex-preview').spectrum({
        color: color.hex,
        showInput: true,
        showPalette: true,
        palette: gp.Helpers.array.chunks(that.colors.pluck('hex'), 4),
        clickoutFiresChange: true,
        showInitial: true,
        showButtons: false,
        theme: 'variant-color-selector-picker',
        preferredFormat: 'hex',
        change: function(c) {
          var hex = c.toHexString()
          var model = that.colors.get(hex)
          if (!model) {
            model = new ColorModel({ hex: hex })
            that.colors.add(model)
          }
          setSelectedColor(model.toJSON())
        }
      })

      this.colors.on('add remove', function() {
        var palette = gp.Helpers.array.chunks(that.colors.pluck('hex'), 4)
        $colorHexPreview.spectrum('option', 'palette', palette)
      })

      var onModelChange = _.debounce(function(model) {
        var selectedColor = getSelectedColor()
        if (model.id === selectedColor.hex) {
          setSelectedColor(model.toJSON())
        }
      }, 5)

      this.colors.on('change', onModelChange)

      ;(function() {
        var color = getSelectedColor()
        that.colors.add(color)
      })()

      return $color.appendTo($colorList)
    },

    addDimensions: function(dimensions) {
      var $list = $('.dimensions-list')
      var $dimensions = this.dimensionsTemplate(_.clone({ dimensions: dimensions}))

      $dimensions.appendTo($list)

      return $dimensions
    },

    addHardness: function(hardness) {
      var $list = $('.hardness-list')
      var $hardness = this.hardnessTemplate(_.clone({ hardness: hardness }))

      $hardness.appendTo($list)

      return $hardness
    },

    addLength: function(length) {
      var $list = $('.length-list')
      var $length = this.lengthTemplate(_.clone({ length: length }))

      $length.appendTo($list)

      return $length
    },

    addMaterial: function(material) {
      var $list = $('.material-list')
      var $material = this.materialTemplate(_.clone({ material: material }))

      $material.appendTo($list)

      return $material
    },

    addSize: function(size) {
      var $list = $('.size-list')
      var $size = this.sizeTemplate(_.clone({ size: size }))

      $size.appendTo($list)

      return $size
    },

    addPercentage: function(percentage) {
      var $list = $('.percentage-list')
      var $percentage = this.percentageTemplate(_.clone({ percentage: percentage }))

      $percentage.appendTo($list)

      return $percentage
    },

    addPeriod: function(period) {
      var $list = $('.period-list')
      var $period = this.periodTemplate(_.clone({ period: period }))

      $period.appendTo($list)

      return $period
    },

    addDate: function(date) {
      var $list = $('.date-list')
      var $date = this.dateTemplate(_.clone({ date: date }))

      $date.appendTo($list)

      return $date
    },

    addCoef: function(coef) {
      var $list = $('.coef-list')
      var $coef = this.coefTemplate(_.clone({ coef: coef }))

      $coef.appendTo($list)

      return $coef
    },

    addExternal: function(external) {
      var $list = $('.external-list')
      var $external = this.externalTemplate(_.clone({ external: external }))

      $external.appendTo($list)

      return $external
    },

    addPayment_method: function(payment_method) {
      var $list = $('.payment_method-list')
      var $payment_method = this.paymentMethodTemplate(_.clone({ payment_method: payment_method }))

      $payment_method.appendTo($list)

      return $payment_method
    },

    addNoproperty: function(value) {
      // Do nothing.
    },

    getColorModel: function($color) {
      var colorHex = $color.find('.color-hex').val()
      return this.colors.get(colorHex)
    },

    getColorFormValues: function($color) {
      return $color.find('.color-id, .color-qty, .color-name, .color-id, .color-hex')
    },

    enableColor: function($color) {
      this.getColorFormValues($color).prop('disabled', false)
    },

    disableColor: function($color) {
      this.getColorFormValues($color).prop('disabled', true)
    },

    createColorPropertyValue: function() {
      var color = (this.colors.length && this.colors.first().toJSON()) || { hex: '#ffffff', name: '' }
      this.addColor(color)

      return color
    },

    createDimensionsPropertyValue: function() {
      var dimensions = ''
      var $dimensions = this.addDimensions(dimensions)

      _.defer(function() {
        $dimensions.find('.dimensions-value').focus()
      })

      return dimensions
    },

    createHardnessPropertyValue: function() {
      var hardness = ''
      var $hardness = this.addHardness(hardness)

      _.defer(function() {
        $hardness.find('.hardness-value').focus()
      })

      return hardness
    },

    createLengthPropertyValue: function() {
      var length = ''
      var $length = this.addLength(length)

      _.defer(function() {
        $length.find('.length-value').focus()
      })

      return length
    },

    createMaterialPropertyValue: function() {
      var material = ''
      var $material = this.addMaterial(material)

      _.defer(function() {
        $material.find('.material-value').focus()
      })

      return material
    },

    createSizePropertyValue: function(e) {
      var size = ''
      var $size = this.addSize(size)

      _.defer(function() {
        $size.find('.size-value').focus()
      })

      return size
    },

    createPercentagePropertyValue: function(e) {
      var percentage = ''
      var $percentage = this.addPercentage(percentage)

      _.defer(function() {
        $percentage.find('.percentage-value').focus()
      })

      return percentage
    },

    createPeriodPropertyValue: function(e) {
      var period = ''
      var $period = this.addPeriod(period)

      _.defer(function() {
        $period.find('.period-value').focus()
      })

      return period
    },

    createArticuloPropertyValue: function(e) {
      var articulo = ''
      var $articulo = this.addArticulo(articulo)

      _.defer(function() {
        $articulo.find('.articulo-value').focus()
      })

      return articulo
    },

    createDatePropertyValue: function(e) {
      var date = ''
      var $date = this.addDate(date)

      _.defer(function() {
        $date.find('.date-value').focus()
      })

      return date
    },

    createExternalPropertyValue: function(e) {
      var external = ''
      var $external = this.addExternal(external)

      _.defer(function() {
        $external.find('.external-value').focus()
      })

      return external
    },

    createCoefPropertyValue: function(e) {
      var coef = ''
      var $coef = this.addCoef(coef)

      _.defer(function() {
        $coef.find('.coef-value').focus()
      })

      return coef
    },


    createPayment_methodPropertyValue: function(e) {
      var payment_method = ''
      var $payment_method = this.addPayment_method(payment_method)

      _.defer(function() {
        $payment_method.find('.payment-method-value').focus()
      })

      return payment_method
    },

    createNopropertyPropertyValue: function(e) {
      return
    },

    deleteProperty: function(e) {
      if (~e.currentTarget.className.indexOf('noproperty')) {
        var xhr = $.post(gp.product.url_reset_properties, function() {
          window.location.reload();
        }).fail(function() {
          alert('Un error ha ocurrido, vuelva a intentarlo');
        });

        return
      }

      var $property = $(e.currentTarget).parents('.property-container')
      $property.remove()
      this.updatePropertySelector()
      this.stockTable.reset()
    },

    deletePropertyValue: function(e) {
      var $propertyValue = $(e.currentTarget).parents('.property-value')
      $propertyValue.remove()

      this.stockTable.reset()
    },

    getPropertyMsg: function(property) {
      var msg = ''
      if (property == 'dimensions' || property == 'length') {
        msg = I18n.t('javascripts.lux.products.properties.explanation')
      }

      return msg
    },

    loadProperties: function() {
      var properties = {},
          variants,
          availableProperties

      if (gp.variantsNotSaved) {
        availableProperties = _.keys(gp.variantsNotSaved[0].properties)
        variants = gp.variantsNotSaved
      } else {
        availableProperties = gp.availableProperties
        availableProperties.push('noproperty')

        variants = gp.product && gp.product.variants
      }

      _.each(availableProperties, function(property) {
        var propertyValues = []

        _.each(variants, function(variant){
          if (!_.find(propertyValues, function(propertyValue) { return _.isEqual(variant.properties[property], propertyValue) })) {
            if (variant.properties[property] != undefined) propertyValues.push(variant.properties[property])
          }
        })

        if (propertyValues.length > 0) properties[property] = propertyValues
      })

      return properties
    },

    defaultDisplayOptions: function() {
      this.$checkbox.trigger('click')
    },

    initStockManagement: function() {
      var that = this
      $('.manage-stock').hide()
      if ($('#manage-stock').prop('checked')) $('.manage-stock').show()
      $('#manage-stock').change(function() { $('.manage-stock').toggle() })

      $('.open-popup-link').magnificPopup({
        type:'inline',
        midClick: true,
      })

      $('#submit-stock-button').click(function(e) {
        if(that.submitStockManagement(e)) {
          $.magnificPopup.close()
        }
      })
    },

    submitStockManagement: function(e) {
      e.preventDefault()

      var inputs_required = $('input.stock')
      var halt = false
      _.each(inputs_required, function(input) {
        input = $(input)
        if (input.val() == "") {
          input.addClass('error')
          halt = true
          input.click(function() {
            input.removeClass('error')
          })
        } else {
          input.removeClass('error')
        }
      })

      if (halt) {
        alert('Please, fill all required fields.')
        return false
      }else{
        return true
      }
    },

    buildStockTable: function() {
      this.stockTable.reset()

      var propertiesWithValues = this.loadDOMProperties()
      this.stockTable.render(propertiesWithValues)

      $('.open-popup-link').click()
    },
  })

})()
