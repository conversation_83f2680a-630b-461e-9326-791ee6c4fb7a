gp.Lux.ProductsFormView = gp.App.View.extend({
  events: {
    'click .gender-container .checked': 'removeSelectedGender',
    'click .add_fields': 'addPackage',
    'click .remove_fields': 'deletePackage',
    'keyup #product_packages': 'observerPackagesField',
    'change #product_packages': 'observerPackagesField'
  },
  initialize: function(){
    var that = this
    _.bindAll(
      this,
      'addImageFiles',
      'addImageFile',
      'setup',
      'categorySelected',
      'categorySelected2',
      'getSelectedProperties',
      'removeSelectedGender'
    )

    this.$hiddens = this.$('.hiddens')

    this.category = new gp.Lux.NestedSelectView({el: '.root_categories'});
    if( $('#product_category_path_ids').length ) {
      this.category_path_ids = $('#product_category_path_ids').val().split(' ')
    }
    this.category.on('option:selected', this.categorySelected)
    this.$category_hidden = this.$('#product_category_id')

    this.category_2 = new gp.Lux.NestedSelectView({el: '.root_categories_2'});
    if( $('#product_category_path_ids_2').length ) {
      this.category_path_ids_2 = $('#product_category_path_ids_2').val().split(' ')
    }
    this.category_2.on('option:selected', this.categorySelected2)
    this.$category_hidden_2 = this.$('#product_second_category_id')

    $('#product_sale_on').fdatepicker({format: 'yyyy/mm/dd'})
    $('#product_sale_until').fdatepicker({format: 'yyyy/mm/dd'})
    $('#product_available_on').fdatepicker({format: 'yyyy/mm/dd'})

    this.initSaveButton()

    gp.Helpers.file.init()
      .done(function(){
        that.uploader = that.autoInstantiate(gp.Social.FileUploaderView)[0]
        that.uploader.show()
        that.initAddMoreImages()
        that.initDeleteImages()
      })
      .fail(function(){
        alert('Your device is not compatible for uploading pictures.')
      })

      this.variantsForm = new gp.Lux.VariantsFormView({ el: this.$('.variants-form') })
  },

  initSaveButton: function() {
    var that = this
    this.$saveButton = $('#save')
    this.$saveButton.on('click', function(e){
      if(that.priceIsChecked()){
        if( !gp.product || !gp.product.has_integration ) {
          if(!that.variantsForm.$manageStockCheckbox.prop('checked')){
            if (!gp.product.has_no_property) {
              that.variantsForm.stockTable.reset()
              that.variantsForm.buildUnlimitedStockVariants()
            }
          } else {
            if(!that.variantsForm.stockTable.done()){
              $('#submit-stock-button').click(function(e){
                if(that.variantsForm.submitStockManagement(e)){
                  $('#save').click()
                }
              })
              that.variantsForm.buildStockTable()
              e.preventDefault()
              return
            }
          }
        }
        e.preventDefault()
        var inputs_required = $('input.required, textarea.required')
        var halt = false
        _.each(inputs_required, function(input){
          input = $(input)
          if(input.val() == ""){
            input.addClass('error')
            halt = true
            input.click(function(){
              input.removeClass('error')
            })
          }else{
            input.removeClass('error')
          }
        })

        $('.categories .current').each(function(category){
          if($(this).text().match(I18n.t('javascripts.lux.products.pick_one'))){
            $('.categories .error').show()
            halt = true
          }
        })
        var halt2 = false
        $('.categories_2 .current').each(function(category){
          if ($('.categories_2 .current').length > 1){
            if($(this).text().match(I18n.t('javascripts.lux.products.pick_one'))){
              $('.categories_2 .error').show()
              halt2 = true
              $('.category_test').css("display", ""); 
            }
          }
        })

        if(halt || halt2){
          alert('Please, fill all required fields.')
          return
        }

        $('#product-form').submit()
      }else{
        e.preventDefault()
      }
    }).prop('disabled', false)
  },

  initDeleteImages: function(){
    var that = this
    var deleteButtons = $('#picture .delete-btn')

    deleteButtons.hover(function(){
      $(this).addClass('delete-btn-hovered')
    }, function(){
      $(this).removeClass('delete-btn-hovered')
    })

    deleteButtons.on('click', function(){
      var picture_id = $(this).data('picture_id')
      var deletedPics = $('<input/>', {
        type: 'hidden',
        name: 'deleted_picture_ids[]',
        value: picture_id
      })

      deletedPics.appendTo(that.$hiddens)
      $(this).closest('#picture').hide('fast')
    })
  },

  initAddMoreImages: function(){
    if (gp.product && gp.product.has_integration) return;
    var that = this
    var fileOptions = gp.Lux.ProductsFormView.fileOptions
    var $addImages = this.$('.add-photos')
    var fileListener = gp.Helpers.file.listen($addImages, fileOptions)

    if( !fileListener ) {
      alert('There was an error creating the album.')
    }

    fileListener
      .on('files:selected', function(files){
        that.addImageFiles(files)
      })
      .on('files:error:maxFileSize', function(file, maxFileSize){
        alert( I18n.t('javascripts.partials.form.upload_file_too_big', {max: maxFileSize}) )
      })
      .on('files:error:accept', function(file, types){
        alert( I18n.t('javascripts.partials.form.file_type_not_allowed', {types: types.join(', ')}) )
      })
  },

  addImageFiles: function(files){
    if( !files ) return
    if( files.length ) {
      for(var i = 0; i < files.length; i++) {
        this.addImageFile(files[i])
      }
    } else if( files.type ) {
      this.addImageFile(files)
    }
  },

  addImageFile: function(file){
    var self = this
    this.$saveButton.prop('disabled', true)

    var $picHidden = $('<input/>', {
      type: 'hidden',
      name: 'product[published_pictures][][id]'
    })

    $picHidden.appendTo(this.$hiddens)

    var $file = this.uploader.add(file)

    $file
      .on('file:done', function(e, data){
        $picHidden.val(data.picture.id)
      })
      .on('file:fail', function(e, data){
        $picHidden.remove()
        alert( I18n.t('javascripts.partials.form.upload_error') )
      })
      .on('file:remove', function(e, data){
        $picHidden.remove()
      })
      .on('file:always', function(e, data){
        if( !gp.Helpers.file.pending() ) {
          self.$saveButton.prop('disabled', false)
        }
      })
  },

  setup: function(setup_data){
    if(this.category_path_ids) {
      this.category.setup_data = this.category_path_ids
    }
    if(this.category_path_ids_2) {
      this.category_2.setup_data = this.category_path_ids_2
    }
    this.category.setup()
    this.category_2.setup()
  },

  getSelectedProperties: function(){
    return _.map($('.properties-list').children(),function(el) {
      return $(el).attr('data-property')
    })
  },

  removeSelectedGender: function(e){
    e.stopPropagation()
    var $genderBtn = $(e.currentTarget)
    $genderBtn.removeClass('checked')
    $genderBtn.siblings('input').val('')
  },

  priceIsChecked: function(){
    var price = parseInt($('#product_regular_price').val())
    var voucher = $('#product_voucher_attributes_supplier').val()
    if (price > 0 || voucher) {
     return true
    } else {
      return window.confirm(I18n.t('javascripts.lux.products.properties.regular_price'))
    }
  },

  categorySelected: function(option) {
    this.$category_hidden.val(option.val())
  },

  categorySelected2: function(option) {
    this.$category_hidden_2.val(option.val())
  },

  addPackage: function(e) {
    var regexp, time;
    time = new Date().getTime();
    regexp = new RegExp($('.add_fields').data('id'), 'g');
    $('.add_fields').before($('.add_fields').data('fields').replace(regexp, time));
    return e.preventDefault();
  },

  deletePackage: function(e) {
    selector = e.currentTarget;
    fieldset = $(selector).closest('fieldset');
    fieldset.find('input[type=hidden]').val('true');
    fieldset.hide();
    return e.preventDefault();
  },

  observerPackagesField: function(e) {
     if(parseInt($('#product_packages').val()) <= 0) {
       $('#product_packages').val(1);
       alert("Value must be greater than zero");
     }
  },

},{
  fileOptions: {
    accept: ['image/bmp','image/gif','image/jpeg','image/png'],
    multiple: true,
    maxFileSize: 24
  }
})

$(function() {
  $('#product_manufacturer_id').select2();

  $(document).on('input', '.data-attributes', function() {
    var $_this = $(this),
      to_target = $_this.data('value');

    $('#' + to_target).attr('name', "product[data][" + $_this.val() + "]");
  });

  $(document).on('click', '.technical-delete', function() {
    $(this).closest('.field').remove();
  });

  $('.add-technical').click(function() {
    var data_type = $(this).data('type');
    var data = "";
    data += '<div class="field">';
    data +=   '<div class="small-5 medium-5 columns">';
    data +=     '<label>Atributo</label>';
    data +=     '<input type="text" class="text-right input-text" name="product[' + data_type + '][' + _totalTechnicalFields[data_type] + '][name]" value="" />';
    data +=   '</div>';
    data +=   '<div class="small-5 medium-5 columns">';
    data +=     '<label>Detalle</label>';
    data +=     '<input type="text" class="input-text data-values" name="product[' + data_type + '][' + _totalTechnicalFields[data_type] + '][value]" value="" />';
    data +=   '</div>';
    data +=   '<div class="small-2 medium-2 columns">';
    data +=     '<img alt="" src="/assets/lux/delete.png" class="technical-delete" style="cursor: pointer; width: 10px;" title="Eliminar" />';
    data +=   '</div>';
    data += '</div>';
    _totalTechnicalFields[data_type]++;

    $('.technical-fields-' + data_type).append(data);
  });
});


