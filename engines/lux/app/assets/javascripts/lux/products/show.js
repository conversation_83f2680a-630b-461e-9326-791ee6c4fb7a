$(function(){
  if( !gp.controller.products || !gp.action.show ) return

  // Asigna la vaariante a la imagen
  ;(function(){
    var $container = $('#pictures-container')
    var $selects = $container.find('.color-hex-selector')
    const $saveButton = $container.find('.save-button')

    var variants = {}
    $selects.find('option').each(function(i, el){
      var picture_id = +$(el).data('picture_id')
      if (picture_id) {
        variants[picture_id] = 0
      }
    })
    $selects.each(function(i, el){
      var $el = $(el), pictureId = +$el.data('picture_id')
      if ( pictureId ) variants[pictureId] = +$el.val()
    })

    window.$selects = $selects
    window.variants = variants

    function saveVariants() {
      var $form = $('#update-form-js form').clone()
      var $hiddens = $form.find('.hiddens')
      var $hiddenId = $('<input type="hidden" name="pictures_data[][picture_id]">')
      var $hiddenPic = $('<input type="hidden" name="pictures_data[][variant_id]">')

      _.each(variants, function(pId, vId){
        $hiddenId.clone().val(vId).appendTo($hiddens)
        $hiddenPic.clone().val(pId).appendTo($hiddens)
      })

      return $.ajax({
        url: $form.prop('action'),
        type: $form.prop('method'),
        data: $form.serialize()
      })
    }

    var select2Format = function(c){
      return '<div class="left color" style="background-color:'+$(c.element).data('color_hex')+'"></div>'+c.text
    }
    $selects.select2({
      formatResult: select2Format,
      formatSelection: select2Format,
      escapeMarkup: function(m){ return m }
    })
      .on('change', function(e){
        var $el = $(e.currentTarget)
        var picId = +$el.data('picture_id')
        var variantId = +$el.val()

        if (picId) {
          variants[picId] = variantId
        }
      })

    $saveButton.on('click', () => {
      saveVariants()
        .done(function(){
          $container.removeClass('loading')
        })
        .fail(function(){
          alert('there was an error associating the pictures.')
        })
    })

    })()
  });