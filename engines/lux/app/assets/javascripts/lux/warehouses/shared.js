$(function() {
  if (!gp.controller.warehouses) return

  function initPickupSwitcher(){
    $pickupFields = $('.pickup')

    if ($('#warehouse_pickup:checked').length) $pickupFields.show()

    $('#warehouse_pickup').click(function(){
      if($('input:checked').size() > 0){
        $pickupFields.show()
      }else{
        $pickupFields.hide()
      }
    })
  }

  function validateTelephone(phone){
    if (gp.network != 'US') return true
    // Got it from http://blog.stevenlevithan.com/archives/validate-phone-number
    return /^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$/.test(phone);
  }

  function setUpSubmitButton(){
     $('.add').click(function(e){
      e.preventDefault();
      var inputs_required = $('input.required');
      var halt = false
      _.each(inputs_required, function(input){
        input = $(input)
        if(input.val() == ""){
          input.addClass('error')
          halt = true
          input.click(function(){
            input.removeClass('error')
          })
        }else{
          input.removeClass('error')
        }
      })
      if(halt){
        alert(I18n.t('javascripts.lux.warehouses.required_fields_warn'))
        return
      }
      if ($('#s2id_warehouse_country .select2-chosen').html() == 'Select Country'){
        alert(I18n.t('javascripts.lux.warehouses.choose_country'))
        return
      }

      var phone = $('#warehouse_telephone').val()
      if (!validateTelephone(phone)){
        alert('Invalid phone number. Insert valid format. eg. 1234567890, ************, ************, ************, (*************')
        return
      }

      $('#new-warehouse').submit()
    })
  }

  var ar_network_countries = [
    { id: 'AR', text: 'Argentina' }
  ]

  var us_network_countries = [
    { id: 'US', text: 'United States' }
  ]
  var cl_network_countries = [
    { id: 'CL', text: 'Chile' }
  ]

  var getCountryCode = function(country) {
  if (country == 'Argentina') {
      return 'AR'
   } else if (country == 'United States') {
      return 'US'
   } else if(country == 'Canada') {
      return 'CA'
   } else if(country == 'Chile') {
      return 'CL'
   } else{
      return country
    }
  }

  getCountriesByNetwork = function(network) {
    switch(network) {
      case 'AR':
        return ar_network_countries
      case 'US':
        return us_network_countries
      case 'CL':
        return cl_network_countries
      default:
        return us_network_countries
    }
  }

  $('#warehouse_state').select2({
    width: '100%'
  })
 
  //$('#warehouse_country').linkToStates('#province');
  /*
  if (countries.length > 1) {
    $('#warehouse_country').select2({
      placeholder: I18n.t('javascripts.mkp.checkout.country_placeholder'),
      data: countries
    })

    $('#warehouse_country').select2('val', countries[0].id).change()
  } else{
    $('#warehouse_country').select2({
      data: countries,
      formatResultCssClass: 'crazy'
    })

    $('#warehouse_country').select2('val', countries[0].id).change()
    $('#warehouse_country').select2('enable', false)
    $('#s2id_warehouse_country .select2-choice').css('opacity', '.5')
  }
*/
  initPickupSwitcher()
  setUpSubmitButton()
});
