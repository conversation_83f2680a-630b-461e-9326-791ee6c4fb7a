//= require globals/base_application
//= require foundation/foundation.section
//= require foundation/foundation.alerts

// LIBS
//= require selectize
//= require v5/globals/geo_constants
//= require jquery.selectboxes.pack.js
//= require foundation-datepicker
//= require spectrum
//= require ui/jquery.ui.core.min
//= require ui/jquery.ui.widget.min
//= require ui/jquery.ui.mouse.min
//= require ui/jquery.ui.sortable.min
//= require ui/jquery.ui.draggable.min

// APP
//= require lux/globals/lux
//= require lux/globals/views/_nested_select

// MIXINS, MODELS & COLLECTIONS
//= require lux/globals/mixins/ancestryMixin
//= require lux/globals/collections/categories
//= require lux/globals/collections/manufacturers
//= require lux/globals/collections/sports
//= require lux/globals/collections/products

// PARTIALS
//= require lux/partials/_shop_title
//= require lux/products/_form
//= require lux/products/stock_table
//= require lux/products/_variants_form
//= require social/partials/_file_uploader

// VIEWS
//= require lux/questions
//= require lux/merchants
//= require lux/products/new
//= require lux/products/edit
//= require lux/products/show
//= require lux/products/index
//= require lux/product_imports/new
//= require lux/product_imports/progress
//= require lux/coupons/new
//= require lux/shipping_methods/index
//= require lux/warehouses/shared
//= require lux/warehouses/pickupSelect
//= require lux/taxes/index
//= require lux/suborders/index
//= require lux/banners/form
//= require lux/wide_discount
//= require lux/integrations/index
