;if(gp.controller.integrations && gp.action.index) (function() {

  var $sportsSelect = $('select#sports-select')
  var $removeOptions = $('.modal-content .option')

  $sportsSelect.select2({ maximumSelectionSize: 3 })

  $removeOptions.find('label').on('click', function(e){
    $(e.currentTarget).siblings('input[type=radio]').click()
  })

  $('#import_and_sync').on('submit', function(){
    $(this).children('input[type=submit]').prop('disabled', true);
  }) 
})()
