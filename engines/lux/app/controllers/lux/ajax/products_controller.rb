class Lux::Ajax::ProductsController < Lux::ApplicationController
  include ActionView::Helpers::NumberHelper

  skip_before_filter :store_return_to_location

  ALLOWED_REFINEMENTS = %w(on_sale with_stock without_stock available unavailable)
  ALLOWED_TO_UPDATE = %w(title category_id manufacturer_id regular_price sale_price sale_on sale_until sports_ids)
  ALLOWED_ORDER_COLUMNS = %w(title regular_price sale_price manufacturer stock sold)
  ALLOWED_ORDER_DIRECTIONS = %w(asc desc)
  PRODUCTS_PER_PAGE = 20

  def index
    @products = @shop.products
                     .not_deleted
                     .includes([:category, :currency, :pictures, variants: [product: [:manufacturer, :shop]]])
    filter_by_query
    filter_by_restrictions
    filter_by_category
    refine_products
    order_products
    unless params.key?(:category_ids) || params.key?(:manufacturer_ids)
      params[:current_page] = 1 if params[:current_page] === 'current_page='
      @products = @products.paginate(page: params[:current_page], per_page: 20)
    end

    render json: build_json_response
  end

  def show
    product = @shop.products.find(params[:id])
    render json: { product: json_show(product) }
  end

  def update
    product = Mkp::Product.find(params[:id])

    shop = product.shop

    unless current_user.shops.exists?(shop)
      render status: :bad_request and return
    end

    new_attrs = params[:product].slice(*ALLOWED_TO_UPDATE)

    new_attrs.each do |attrName, attrVal|
      if attrName == 'sports_ids'
        product.sports = Sport.where(id: attrVal)
      else
        product.send(attrName+'=', attrVal)
      end
    end

    if product.save
      render json: { product: json_show(product) }, status: :ok
    else
      render json: { errors: product.errors.full_messages }, status: :error
    end
  end

  def destroy
    product = @shop.products.find(params[:id])
    product.destroy

    render json: {status: :ok}
  end

  def sort_pics
    product = Mkp::Product.find(params[:product_id])
    pics = params[:pics].values
    pics.each_with_index do |pic, i|
      picture = product.pictures.find(pic[:id])
      picture.update_attributes(view_order: i)
    end
    render json: { data: true, status: 'success'}, status: 200
  end

  private

  def build_json_response
    json = {
      params: {
        network: @network,
        current_page: @products.current_page,
        refinement: @refinement,
        query: params[:query],
        shop_id: @shop.id,
        order_column: @ordering_column,
        order_direction: @ordering_direction,
        category_id: params[:category_id],
      },
      info: {
        total_entries: @products.total_entries,
        total_pages: @products.total_pages,
      },
      products: json_index(@products)
    }
  end


  def filter_by_query
    if query = params[:query].presence
      query = '%' + query.strip + '%'

      where_clause = "mkp_products.title LIKE :query " \
                     "OR mkp_products.description LIKE :query " \
                     "OR mkp_variants.gp_sku LIKE :query " \
                     "OR mkp_variants.sku LIKE :query " \
                     "OR mkp_manufacturers.name LIKE :query"
      @products = @products.joins(:manufacturer).joins(:variants).where(where_clause, query: query, shop_id: @shop.id)
    end
  end

  def json_index(products)
    products.map { |p| json_show(p) }
  end

  def json_show(product)
    p = product
    thumb = p.pictures.first.presence
    variants = p.variants.presence
    sku = (p.variants.any?) ? p.variants.take.gp_sku : p.get_gp_sku
    {
      id: p.id,
      title: p.title,
      thumb: thumb && thumb.url(:m),
      regular_price: number_with_precision(p.regular_price, precision: 2, delimiter: ''),
      sale_price: p.sale_price ? number_with_precision(p.sale_price, precision: 2, delimiter: '') : ' - ',
      sale_on: p.sale_on,
      sale_on_text: p.sale_on && p.sale_on.strftime('%Y-%m-%d'),
      sale_until: p.sale_until,
      sale_until_text: p.sale_until && p.sale_until.strftime('%Y-%m-%d'),
      currency_symbol: p.currency_symbol,
      on_sale: p.on_sale?,
      has_sale: p.has_sale?,
      gp_sku: sku,
      available: p.available?,
      stock: p.total_stock,
      sold_count: p.sold_count,
      hide_url: change_visibility_shop_product_url(id: p.id, shop_id: p.shop_id, visibility: 'hide'),
      display_url: change_visibility_shop_product_url(id: p.id, shop_id: p.shop_id, visibility: 'show'),
      preview_url: variants && variants.first.get_url(preview: true),
      edit_url: edit_shop_product_url(id: p.id, shop_id: p.shop_id),
      delete_url: delete_shop_product_url(id: p.id, shop_id: p.shop_id),
      clone_url: clone_shop_product_url(id: p.id, shop_id: p.shop_id),
      category_id: p.category.id,
      second_category_id: p.second_category ? p.second_category.id : '',
      sports_ids: p.sports.pluck('id').presence,
      manufacturer_id: p.manufacturer_id,
      has_integration: p.has_any_shop_integration?,
      integration_image: p.has_any_shop_integration? && integration_image(p)
    }
  end

  def refine_products
    @refinement = nil
    if params[:refinement].present? && index = ALLOWED_REFINEMENTS.index(params[:refinement])
      @refinement = ALLOWED_REFINEMENTS[index]
      ids = @products.ids rescue @products.map(&:id)
      products = Mkp::Product.where(id: ids, shop_id: @shop.id)
      @products = products.send(@refinement.to_sym)
    end
  end

  def order_products
    if !ALLOWED_ORDER_COLUMNS.include?(params[:order_column])
      return nil
    end
    if !ALLOWED_ORDER_DIRECTIONS.include?(params[:order_direction])
      return nil
    end
    @ordering_column = params[:order_column]
    @ordering_direction = params[:order_direction]

    @products = if @ordering_column == 'manufacturer'
      @products.joins(:manufacturer)
               .order("mkp_manufacturers.name #{@ordering_direction.upcase}")
    elsif @ordering_column == 'stock'
      sql = @products.joins(:variants)
                     .select('SUM(mkp_variants.quantity) AS stock, mkp_products.*')
                     .group('mkp_variants.product_id')
                     .order("stock #{@ordering_direction.upcase}").to_sql
      # This hack is required to avoid a weird Rails behavior when using SUM
      # inside a SELECT clause, combined with an ORDER clause
      @products = Mkp::Product.find_by_sql(sql)
    elsif @ordering_column == 'sold'
      @products.joins(order_items: :suborder)
               .group('mkp_order_items.product_id')
               .select('mkp_products.*, SUM(mkp_order_items.quantity) as sold')
               .order("sold #{@ordering_direction.upcase}")
    else
      @products.order("mkp_products.#{@ordering_column} #{@ordering_direction}")
    end
  end

  def filter_by_restrictions
    shop = @shop
    category_ids = params[:category_ids] ? params[:category_ids].split(',').map(&:to_i) : nil
    manufacturer_ids = params[:manufacturer_ids] ? params[:manufacturer_ids].split(',').map(&:to_i) : nil
    unless category_ids.nil? && manufacturer_ids.nil?
      search = Mkp::Product.search(include: [:manufacturer]) do
        all_of do
          with :shop_id, shop.id
          with :with_stock, true
          with :category_id, category_ids if category_ids
          with :manufacturer_id, manufacturer_ids if manufacturer_ids
        end
        paginate page: (params[:current_page] || 1), per_page: PRODUCTS_PER_PAGE
      end
      @products = search.results
    end
  end

  def integration_image(product)
    integration_name = product.external_objects.first.integration_name
    "https://s3-us-west-2.amazonaws.com/gpcdn-mkp/statics/integrations/#{integration_name}.png"
  end

  def filter_by_category
    shop = @shop
    category_id = params[:category_id]
    return @products if category_id == "0"
    if category_id.present? && shop.present?
      search = Mkp::Product.search(include: [:manufacturer]) do
        all_of do
          with :category_id, category_id
          with :shop_id, shop.id
        end
        paginate page: (params[:current_page] || 1), per_page: PRODUCTS_PER_PAGE
      end
      @products = search.results
    end
  end
end
