module Lux
  module Ajax
    class LabelsController < ActionController::Base
      include UserSessionator
      include NetworkExposure

      #protect_from_forgery with: :exception
      
      before_filter :ensure_user_allowance

      # POST /ajax/labels/new
      def new
        @items = @suborder.items.where(id: params[:item_ids])

        if (shipments = @items.first.shipments).present?
          @shipment = shipments.detect do |shipment|
            shipment.unfulfilled? && shipment.outgoing?
          end
        end
      end

      # POST /ajax/labels
      def create
        @items = @suborder.items.where(id: params[:item_ids])
        @shipment = load_shipment

        if @suborder.shop.delivery_by_matrix
          if @items.present?
            option = params[:option]

            @label = process_option(option, params[option])

            if @label.present?
              Mkp::StatusChange::EntityStatusManage.status_change(@shipment, 'shipped')

              render_success(@label)
            else
              render_error('Error al procesar la etiqueta')
            end
          else
            render_error('Error al procesar los items para el envío')
          end
        else
          if params['option'] == 'prepaid'
            service = Gateways::Shipments::Krabpack::CreateShipmentService.new(shipment: @shipment)
            if @suborder.refunded?
              service.refund(params[:shipment_date])
            elsif @suborder.exchange?
              service.exchange
              if service.valid
                next_shipment = load_shipment
                service = Gateways::Shipments::Krabpack::CreateShipmentService.new(shipment: next_shipment)
                service.refund(params[:shipment_date])
              end
            else
              service.perform
            end

            if service.valid
              render_success(service.label)
            else
              render_error(service.error)
            end
          else
            label = process_other_label(params['other'])

            if label.present?
              Mkp::StatusChange::EntityStatusManage.status_change(@shipment, 'shipped')
              @suborder.update_attributes(fulfilled_by_gp: true)
              render_success(label)
            else
              render_error('Error al procesar la etiqueta')
            end
          end
        end
      end

      # POST /ajax/labels/:id/status
      def status
        label = load_label
        if label.present?
          if process_status!(params[:status], label)
            render json: {
              data: { label: @label },
              status: 'success'
            }, status: 200
          else
            render json: {
              data: { message: I18n.t('lux.ajax.labels.status.transition_failed') },
              status: 'fail'
            }, status: 422
          end
        else
          render json: {
            data: { message: I18n.t('lux.ajax.labels.status.transition_failed') },
            status: 'fail'
          }, status: 422
        end
      end

      private

      def ensure_user_allowance
        load_suborder

        unless (current_user.present? && current_user_can_manage_suborder?)
          render nothing: true, status: :unauthorized and return
        end
      end

      def load_suborder
        @suborder = Mkp::Suborder.includes(:shop).find(params[:suborder_id])
      end

      def current_user_can_manage_suborder?
        @suborder.shop.can_be_managed_by?(current_user) && @shop = @suborder.shop
      end

      def process_option(option, params)
        return false if params.blank?

        case option
        when 'prepaid'
          return false if @shipment.id != params[:shipment_id].to_i

          process_prepaid_label(params)
        when 'other'
          process_other_label(params)
        else
          false
        end
      end

      def load_shipment
        shipments = @suborder.shipments.select{|shipment| shipment.unfulfilled? && shipment.outgoing?}.compact.uniq

        raise "No shipment is associated to this items: #{@items}" unless shipments.any?

        shipments.first
      end

      def process_prepaid_label(params)
        case params[:type]
        when 'customer-election'
          new_label = @shipment.labels.create do |label|
            label.courier = @shipment.extra_info[:courier]
            label.gateway = @shipment.extra_info[:gateway_class]
          end
        when 'on-demand'
          new_extra_info = @shipment.extra_info.dup
          new_extra_info[:gateway_class] = params[:gateway]
          new_extra_info[:gateway_info] = @suborder.shop.setting.on_demand_labels_config

          @shipment.update_attributes!(extra_info: new_extra_info)

          new_label = @shipment.labels.create do |label|
            label.gateway = if Smartcarrier::Const::CARRIERS.include?(new_extra_info[:gateway_class])
              label.courier = new_extra_info[:gateway_class]
              'SmartCarrier'
            else
              @shipment.extra_info[:gateway_class]
            end
          end
        else
          false
        end

        if new_label.purchase!
          new_label.reload
        else
          new_label.destroy
          false
        end
      end

      def process_other_label(params)
        @shipment.labels.create do |label|
          label.tracking_number = params[:tracking_number].strip
          label.courier = params[:courier].strip
          label.price = params[:price].gsub(/\,/,'.').to_f
        end
      end

      def process_status!(status, label)
        case status
        when 'delivered'
          if Mkp::StatusChange::EntityStatusManage.status_change(label.shipment, status)
            label.activity_log << create_manual_activity('delivered')
            label.save!
          end
        when 'cancel'
          if label.cancel! && Mkp::StatusChange::EntityStatusManage.status_change(label.shipment, 'unfulfilled')
            label.activity_log << create_manual_activity('cancelled')
            label.save!
          end
        end
      end

      def load_label
        possible_label = Mkp::ShipmentLabel.find_by_id(params[:id])

        return unless possible_label.present?

        shipments = @suborder.items.flat_map(&:shipments).uniq.compact

        shipment = shipments.detect do |shipment|
          shipment.labels.active.include?(possible_label)
        end

        if shipment.blank? || shipment.id != params[:shipment_id].to_i
          raise "Label ##{possible_label.id} isn't related to this suborder."
        end

        possible_label
      end

      def create_manual_activity(status)
        current_user_info = "[#{current_user.class.name}:#{current_user.id}] #{current_user.full_name}"

        {
          message: "This admin, #{current_user_info}, has marked this label as #{status}.",
          status: status,
          datetime: Time.now.to_datetime,
          source: :shop_admin,
          ip: real_remote_ip
        }
      end

      def render_error(message)
        render json: { message: message }, status: 422
      end

      def render_success(label)
        render json: { data: { label: label }, status: 'success' }, status: 200
      end
    end
  end
end
