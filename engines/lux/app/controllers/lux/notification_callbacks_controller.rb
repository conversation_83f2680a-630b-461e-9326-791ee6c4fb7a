module Lux
  class NotificationCallbacksController < Lux::ApplicationController

    skip_before_filter :verify_authenticity_token,
                       :require_current_logged_in_user_can_manage_a_shop,
                       :set_current_shop,
                       :load_authorized_integrations, only: :notification

    def notification
      @request_log ||= Logger.new("#{Rails.root}/log/notification-meli.log")
      @request_log.info("@#{'=' * 80}@")
      @request_log.info("REQUEST_URL: #{request.original_url}")
      envs = %w[REQUEST_METHOD REQUEST_PATH HTTP_USER_AGENT HTTP_X_FORWARDED_FOR]
      envs.each { |env| @request_log.info("#{env}: #{request.env[env]}") }
      @request_log.info("Params: #{notification_callback_sanitize_params}")
      @request_log.info("Body: #{JSON.parse(request.raw_post)}")
      begin
        notification_callback = NotificationCallback.create(notification_callback_sanitize_params)
        Lux::AttendNotificationCallbacksWorker.perform_async(notification_callback.id)
      rescue => e
        @request_log.info("Error: #{e.message}")
      end
      @request_log.info("@#{'=' * 80}@ \n")

      render json: 'ok', adapter: :json, status: '200'
    end

    def notification_callback_sanitize_params
      {
        shop_id: params[:shop_id],
        integration_name: params[:integration_name].to_s.camelize,
        external_id: params[:resource].split('/').last
      }
    end
  end
end
