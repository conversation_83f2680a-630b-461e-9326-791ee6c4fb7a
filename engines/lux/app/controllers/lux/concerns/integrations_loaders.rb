module IntegrationsLoaders
  extend ActiveSupport::Concern

  protected

  # This method is intended to be used as a `before_filter`.
  #
  # Examples:
  #   before_filter :load_authorized_integrations,
  #                 if: -> { Network[@network].shop_integrations.present? }
  def load_authorized_integrations
    return @authorized_integrations unless @authorized_integrations.nil?
    @authorized_integrations = @shop.integrations.select(&:authorized?)
  end

  # This method is intended to be used as a `before_filter`.
  #
  # Examples:
  #   before_filter :load_available_integrations
  def load_available_integrations
    @available_integrations ||= available_integrations
  end

  def available_integrations
    allowed_integrations = []
    available_integrations = Network[@network].shop_integrations
    allowed_integrations = available_integrations.map(&:downcase)
    allowed_integrations.delete("mercadolibre") if @shop.stores.all?{|var| var.allow_meli_integration == false}
    allowed_integrations
  end

  def load_authorized_integration(integration_name)
    @authorized_integrations.detect do |integration|
      integration.simple_type == integration_name.downcase
    end
  end

  def get_integration(integration_name)
    return unless @available_integrations.map(&:downcase).include?(integration_name.downcase)

    integration_class_constant(sanitize_integration_name(integration_name)).for(@shop)
  end

  def sanitize_integration_name(integration_name)
    integration_name.to_s.camelize
  end

  def integration_class_constant(integration_name, static_value = nil)
    return "Mkp::Integration::#{integration_name}".constantize if static_value.nil?
    "Mkp::Integration::#{integration_name}::#{static_value}".constantize
  end
end
