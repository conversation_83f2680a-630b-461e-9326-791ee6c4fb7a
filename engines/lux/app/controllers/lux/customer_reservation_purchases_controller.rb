module Lux
  class CustomerReservationPurchasesController < Lux::ApplicationController
    before_filter :find_reservation, only: [:update]
    def update
      if !current_user.eshop_user? && !current_user.is_bna_administrator?
        flash[:alert] = "Usuario no autorizado"
      elsif @reservation.update(customer_reservation_purchase_params)
        flash[:success] = "Updated" 
      else
        flash[:alert] = @reservation.errors.map { |attribute, message| message[:message] }.join(' ')
      end
      redirect_to request.referer || root_path
    end
    
    private

    def find_reservation
      @reservation = CustomerReservationPurchases.find(params[:id])
    end
    def customer_reservation_purchase_params
      params.require(:customer_reservation_purchases).permit(:approved_lended_amount)
    end
  end
end