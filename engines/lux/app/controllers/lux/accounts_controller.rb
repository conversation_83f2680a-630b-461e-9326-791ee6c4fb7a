module Lux
  class AccountsController < Lux::ApplicationController
    before_action :set_eshop_user, only: :show
    force_ssl if SSL_ENABLED

    def update
      set_customs_signer_name
      if @shop.update_attributes(account_parameters)
        if @shop.setting.update_attributes(setting_parameters)
          redirect_to shop_account_url(shop_id: @shop.id), notice: t('lux.controllers.updated_successfully')
        else
          render_error_details
        end
      else
        render_error_details
      end
    end

    def show; end

    private

    def set_customs_signer_name
      # TODO: Revise this. It used to be because of a validation. Don't know if still applies.
      if params["shop_setting"]["customs_signer_name"].blank? && @shop.has_international_shipping_method?
        params["shop_setting"]["customs_signer_name"] = @shop.customs_signer_name
      end
    end

    def render_error_details
      flash[:alert] = t('lux.controllers.updating_trouble', errors: shop_errors)

      render template: 'lux/accounts/show'
    end

    def setting_parameters
      params.require(:shop_setting).permit(:customs_signer_name, :notify_purchases)
    end

    def account_parameters
      permitted_parameters = [:description, :title, :web, :phone, :public_name, :public_email]
      permitted_parameters.push(:visible) if set_eshop_user.present?
      params.require(:shop).permit(permitted_parameters)
    end

    def shop_errors
      @shop.errors.values.flatten.join(', ')
    end

    def set_eshop_user
      @is_eshop_user ||= current_user.eshop_user?

    end
  end
end
