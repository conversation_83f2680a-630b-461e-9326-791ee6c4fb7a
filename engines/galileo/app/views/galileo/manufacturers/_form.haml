= form_for @manufacturer, as: :manufacturer, url: url do |f|
  .row
    .large-12.columns.fields
      = f.text_field :name, placeholder: 'Name'
      %hr
      = f.label :slug, '(Friendly Name used in the URLs replacing white spaces with dashes like: some-manufacturer-name)'
      = f.text_field :slug, placeholder: 'Slug'
      %hr
      = f.file_field :logo

  .row
    .large-12.columns
      = f.submit action, class: 'button small secondary radius right'
