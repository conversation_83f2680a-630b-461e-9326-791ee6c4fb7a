@import 'hero';
@import 'cols2genericv1';
@import 'cols5genericv1';
@import 'cols4brandsv1';
@import 'cols4variantsv1';
@import 'shared/activation_section';

.component {
  .image-container, .mobile-image-container {
    &.empty, &.uploading {
      border: 2px dashed #bdbdbd;
      background-repeat: no-repeat;
      background-position: center center;
      height:88px;
    }
    &.empty {
      background-image: image-url('pioneer/warning.svg');
    }
    &.uploading {
      background-image: image-url('pioneer/rolling.svg');
    }
  }
  .progress-msg{
    margin: 20px 0;
    color: #bdbdbd;
    font-style: italic;
    &.failed{
      color: #d81e05;
    }
    &.success{
      color: #66c17b;
    }
  }
  .picture-buttons {
    margin: 20px 0;
  }
  .line-divider {
    clear: both;
    border-bottom: 1px solid #ccc;
    padding: 20px;
    margin-bottom: 20px;
  }
  .buttons {
    .progress-msg{
      margin: 0px 20px;
    }
    .remove {
      margin-left: 20px;
    }
  }
}

.right{
  float: right !important;
}

.variant-result {
  text-align: center;

  .variant-image img { border-radius: 5px; }
  .chose {
    text-align: center;
    img {
      max-height:170px;
    }
  }
}
