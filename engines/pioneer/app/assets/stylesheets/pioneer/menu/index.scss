.marketplace-menu {
  margin-bottom: 1em;
  width: 100%;
  border: 1px solid #dddddd;
}

.menu-header {
  height: 30px;
  background-color: #f5f5f5;
  .order, .display {
    padding: 0.5em 0.625em 0.625em;
    font-weight: bold;
  }
}
.order {
  float: left;
  width: 92%;
  font-size: 0.875em;
}
.display {
  float: right;
  width: 8%;
  font-size: 0.875em;
  text-align: center;
}

.menu-items {
  @include clearfix;
}
.menu-item {
  @include clearfix;
  margin: 0.2em;
  background-color: #f9f9f9;
  border: 1px solid #dddddd;
  .order, .display {
    padding: 0.5625em 0.625em;
  }
  span {
    position: relative;
    top: 2px;
    display: inline-block;
    width: 16px;
    height: 16px;
    background-image: image-url('pioneer/ui-icons_888888_256x240.png');
    background-position: -128px -48px;
  }
  input[type='checkbox'] {
    margin: 0em;
  }
}
.menu-item:nth-child(odd) {
  background-color: #ffffff;
}

.menu.button {
  float: right;
}