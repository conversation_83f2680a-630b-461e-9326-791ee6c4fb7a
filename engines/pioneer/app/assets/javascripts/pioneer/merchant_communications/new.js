;(function(){
  if( !gp.controller.merchant_communications || !gp.action.new ) return

  function initValidation(){
    $form = $('form:first')
    $('.success').click(function(e){
      e.preventDefault()
      e.stopPropagation()

      var $inputsRequired = $('.required')
      var halt = false
      $inputsRequired.each(function(){
        var $input = $(this)
        if($input.val() == ""){
          $input.addClass('error')
          halt = true
          $input.click(function(){
            $input.removeClass('error')
          })
        }else{
          $input.removeClass('error')
        }
      })
      if(!$('#system_communication_merchant_customer_id').select2('val')[0]){
        halt = true
      }

      if(halt){
        alert('Please, fill all required fields.')
        return false
      }
      $form.submit()
    })
  }

  function initShopsSelector(){
    $('#system_communication_merchant_customer_id').select2({placeholder: 'Select Shops'})
  }

  initShopsSelector()
  initValidation()

}).call(this)
