;(function(){
  $('#integrate-all').click(function(e){
    $('.to-integrate input[type="checkbox"]').click()
  })

  $('.to-integrate input[type="checkbox"]').click(function(e){
    reload_integrate_button()
  })

   function reload_integrate_button(){
    not_integrated = $('.to-integrate input[type=checkbox]:checked').length
    integrate_button = $('.integrate')
    if (not_integrated > 0){
      integrate_button.removeClass('hidden')
    }else if(not_integrated == 0) {
      integrate_button.addClass('hidden')
    }
  }

  $('#delete-all').click(function(e){
    $('.to-delete input[type="checkbox"]').click()
  })

  $('.to-delete input[type="checkbox"]').click(function(e){
    reload_delete_button()
  })

   function reload_delete_button(){
    integrated = $('.to-delete input[type=checkbox]:checked').length
    delete_button = $('.delete')
    if (integrated > 0){
      delete_button.removeClass('hidden')
    }else if(integrated == 0) {
      delete_button.addClass('hidden')
    }
  }
})()
