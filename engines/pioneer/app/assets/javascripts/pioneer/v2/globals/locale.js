;(function(){
  // Set current locale and the default one.
  I18n.defaultLocale = 'en'
  I18n.fallbacks = true
  var locale = $('[name="locale"]').attr('content')

  if( locale === 'cl' ) I18n.defaultLocale = 'es'

  if( _.isString(locale) && locale.length ) I18n.locale = locale

  I18n.withScope = function(scope){
    return function(locale, options){
      if( !options ) options = {}
      if( !options.scope ) options.scope = scope
      return I18n.t(locale, options)
    }
  }

  // jQuery.timeago translation based on locale
  if( window.moment && locale == 'es' ) moment.lang('es')
})()
