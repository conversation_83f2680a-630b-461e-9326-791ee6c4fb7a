gp.Views['partials/menu'] = Backbone.View.extend({
  events: {
    'click .has-dropdown > a': 'openDropdown'
  },

  initialize: function(){
    this.$dropdownSections = this.$el.find('.has-dropdown')
    if( gp.menuSection ){
      this.openSection(this.$dropdownSections.filter('.'+gp.menuSection).find('a'))
    }
  },

  openDropdown: function(evt){
    evt.preventDefault()
    var $link = $(evt.currentTarget)
    if ( $link.hasClass('active') ) {
      this.closeSection($link)
    } else {
      this.openSection($link)
    }
  },

  openSection: function(section){
    section.addClass('active')
    section.siblings('.dropdown').show()
  },

  closeSection: function(section){
    section.removeClass('active')
    section.siblings('.dropdown').hide()
  }

})

;(function(){
  new gp.Views['partials/menu']({
    el: 'nav .menu'
  })
})()
