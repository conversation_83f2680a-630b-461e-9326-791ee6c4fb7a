;if( !!gp.controller.invoicing && !!gp.action.index ) (function(){
  gp.Views['orders/invoice/index'] = Backbone.View.extend({
    t: I18n.withScope('pioneer.js.v2'),
    events: {
      'click a.process-invoice': 'processInvoice'
    },

    initialize: function(){
      _.bindAll(
        this,
        'invoiceGenerated'
      )
      gp.pubSub.on('accounting:invoice:generated', this.invoiceGenerated)
    },

    processInvoice: function(evt){
      evt.preventDefault();
      var that = this
      var url = $(evt.currentTarget).data('ajax')
      this.initProcess('<h3>'+ this.t('ajax.loading') +'</h3>')
      $.ajax({
        url: url,
        type: 'post'
      }).done(function(data){
        that.initProcessDone(data)
      }).fail(function(){
        that.initProcessFail()
      })
      return false
    },
    initProcess: function(content){
      this.invoiceInProcess = new gp.Views['orders/invoice/init_process']({
        content: content
      })
    },
    initProcessDone: function(content){
      this.invoiceInProcess.displayContent(content)
    },
    initProcessFail: function(){
      this.invoiceInProcess.displayContent('<h3 class="fail">'+ this.t('ajax.fail') +'</h3>')
    },

    invoiceGenerated: function(orderId, number){
      var $orderRow = this.$el.find('tr.order_'+orderId)
      var content = '<h5>'+ this.t('invoice.title') +'</h5>'
      content += '<span>'+number+'</span>'
      $orderRow.children('.actions').html(content)
      $orderRow.addClass('updated')
    }
  })

  var orderList = new gp.Views['orders/invoice/index']({
    el: $('.main-wrapper #order-list')
  })
})()
