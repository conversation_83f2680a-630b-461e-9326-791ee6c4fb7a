gp.Views['orders/invoice/init_process'] = Backbone.View.extend({
  t: I18n.withScope('pioneer.js.v2.invoice.process'),
  className: 'invoice_process',
  events: {
    'click .actions .process': 'processInvoice'
  },

  initialize: function(options){
    var that = this
    this.$el.append(options.content)
    this.processing = false
    $.featherlight(this.$el, {
      closeOnClick: false,
      closeOnEsc: false,
      beforeClose: function(){
        if( that.processing ) return false
      }
    })
  },

  displayContent: function(content){
    this.$el.html(content)
    $.featherlight.current().$content.html(this.$el)
  },

  processInvoice: function(evt){
    evt.preventDefault()
    if( this.processing ) return false
    var that = this
    var $link = $(evt.currentTarget)
    var originalText = $link.text()
    var url = $link.data('ajax')
    this.processing = true
    $link.addClass('disabled')
    $link.text(this.t('processing'))
    $.ajax({
      url: url,
      type: 'post'
    }).done(function(response){
      that.processing = false
      var invoice = response.data.invoice
      var number = invoice.gateway_data.number
      var orderId = invoice.order_id
      gp.pubSub.trigger('accounting:invoice:generated', orderId, number)
      that.el.remove()
      $.featherlight.current().close()
    }).fail(function(){
      that.processing = false
      $link.removeClass('disabled')
      $link.text(originalText)
    })
    return false
  }
})
