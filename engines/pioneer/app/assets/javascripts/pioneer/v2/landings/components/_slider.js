gp.Views['pages/components/slider/setup'] = Backbone.View.extend({
  initialize: function(options){
    _.bindAll(
      this,
      'uploaded',
      'removed'
    )
    new gp.Views['pages/components/picture_form']({
      el: this.$el.find('.picture-form'),
      componentName: 'Slider'
    }).on({
      'uploaded': this.uploaded,
      'removed': this.removed
    })
  },

  uploaded: function(data) {
    this.$el.find('input.desktop_picture_id').val(data.id)
  },

  removed: function() {
    this.$el.find('input.desktop_picture_id').val('')
  }
})
