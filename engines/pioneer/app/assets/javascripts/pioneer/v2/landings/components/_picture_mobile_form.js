gp.Views['pages/components/mobile_picture_form'] = Backbone.View.extend({
  events: {
    'click .mobile-upload': 'choose_image',
    'click .mobile-remove': 'remove',
    'change .mobile-file-input': 'upload'
  },

  initialize: function(options){
    if( typeof options['componentName'] !== 'undefined' ){
      this.$el.find('input[name=component_name]').val(options['componentName'])
    }
    this.$imageContainer = this.$el.find('.mobile-image-container')
    this.$imageThumb = this.$imageContainer.find('img')
    this.$fileInput = this.$el.find('.mobile-file-input')

    this.on({
      'uploading': this.uploading,
      'uploaded': this.uploaded,
      'removing': this.removing,
      'removed': this.removed,
      'failed': this.failed,
    }, this)
  },

  choose_image: function() {
    this.$fileInput.trigger('click')
  },

  upload: function(evt){
    var that = this,
        url  = this.getUrl()
    this.trigger('uploading')
    $.ajax({
      url:      url,
      data:     that.getForm(),
      dataType: 'json',
      type:     'post',
      cache:    false,
      processData: false,
      contentType: false
    }).done(function(response){
      that.$el.data('picture_id', response.data.id)
      that.trigger('uploaded', response.data)
    }).fail(function(){
      that.trigger('failed', arguments[2])
    })
  },

  remove: function(evt){
    var that = this,
        url  = this.getUrl()
    this.trigger('removing')
    $.ajax({
      url:      url + '/' + that.$el.data('picture_id'),
      dataType: 'json',
      type:     'delete',
      cache:    false,
      processData: false,
      contentType: false
    }).done(function(){
      that.$el.data('picture_id', '')
      that.trigger('removed')
    }).fail(function(){
      that.trigger('failed', arguments[2])
    })
  },

  getUrl: function(){
    return this.$el.data('action')
  },

  getForm: function(){
    var form = new FormData()
    form.append('component_name', this.$el.find('input[name=component_name]').val())
    form.append('image', this.$el.find('input[name=image]')[0].files[0])
    return form
  },

  uploading: function(){
    this.$el.find('.progress-msg').remove()
    this.$imageContainer.addClass('uploading').removeClass('empty')
    this.$el.find('.picture-buttons').addClass('hidden')
    this.$el.append('<div class="progress-msg">Uploading image, please wait</div>')
  },

  uploaded: function(data){
    this.$imageContainer.removeClass('uploading')
    this.$el.find('.progress-msg').remove()
    this.$imageThumb.prop('src', data.url)
    this.toggleButtons()
  },

  removing: function(){
    this.$el.find('.progress-msg').remove()
    this.$el.find('.picture-buttons').addClass('hidden')
    this.$el.append('<div class="progress-msg">Removing image, please wait</div>')
  },

  removed: function(){
    this.$imageContainer.addClass('empty')
    this.$el.find('.progress-msg').remove()
    this.$imageThumb.prop('src', '')
    this.toggleButtons()
  },

  failed: function(error_msg){
    var $msg = this.$el.find('.progress-msg')
    $msg.addClass('failed').text('There was an error: '+error_msg)
    this.$imageContainer.removeClass('uploading')
    if( this.$imageThumb.prop('src') === ''){
      this.$imageContainer.addClass('empty')
    }
    this.$el.find('.picture-buttons').removeClass('hidden')
  },

  toggleButtons: function() {
    this.$el.find('.picture-buttons').removeClass('hidden')
    var $buttons = this.$el.find('.picture-buttons a')
    _($buttons).each(function(button){
      $(button).hasClass('hidden') ? $(button).removeClass('hidden') : $(button).addClass('hidden')
    })
  }
})
