gp.Views['pages/components/cols1_pics1_v1/setup'] = Backbone.View.extend({
  initialize: function(options){
    _.bindAll(
      this,
      'uploaded',
      'removed'
    )
    new gp.Views['pages/components/picture_form']({
      el: this.$el.find('.picture-form'),
      componentName: 'Cols1Pics1V1'
    }).on({
      'uploaded': this.uploaded,
      'removed': this.removed
    })

    new gp.Views['pages/components/mobile_picture_form']({
      el: this.$el.find('.mobile-picture-form'),
      componentName: 'Cols1Pics1V1'
    }).on({
      'uploaded': this.uploadedMobile,
      'removed': this.removedMobile
    })
  },

  uploaded: function(data) {
    this.$el.find('input.desktop_picture_id').val(data.id)
  },

  removed: function() {
    this.$el.find('input.desktop_picture_id').val('')
  },

  uploadedMobile: function(data) {
    this.$el.parent().find('input.mobile_picture_id').val(data.id)
  },

  removedMobile: function() {
    this.$el.find('input.mobile_picture_id').val('')
  }
})