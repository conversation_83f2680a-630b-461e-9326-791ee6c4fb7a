gp.Views['pages/components/cols2_variants_v1/setup'] = Backbone.View.extend({
  initialize: function(options){
    _.bindAll(
      this,
      'initSelect2',
      'variantFormatResult'
    )

    var $el = this.$el

    this.$variantInput = $el.find('.featured-product-input')
    this.initSelect2()
    this
  },

  initSelect2: function() {
    var url = this.$variantInput.data('variants_url')
    var data = {
        product_sku: this.$variantInput.data('variant_product_sku'),
        id: this.$variantInput.data('product_id'),
        text: this.$variantInput.data('product_title')
    };
    var newOption = new Option(data.text, data.id, false, false);
    this.$variantInput.append(newOption).trigger('change');


    this.$variantInput.select2({
      width: '100%',
      minimumInputLength: 2,
      placeholder: "Select a Product",
      /*initSelection: function (element, callback) {
        callback({ id: 1, text: element.data('product_title') });
      },*/
      ajax: {
        url: url,
        dataType: 'json',
        quietMillis: 500,
        data: function (params) { // page is the one-based page number tracked by Select2
          return {
            search: params.term,
            page: params.page || 1,
            landing_id: $(this).data('landing_id')
          };
        },
        processResults: function (data, page) {
          var more = (page * 10) < data.total; // whether or not there are more results available

          // notice we return the value of more so Select2 knows if more results can be loaded
          return {
            results: data,
            more: more
          };
        }
      },
      templateResult: this.variantFormatResult,
      escapeMarkup: function (m) { return m; }
    })

    this.$variantInput.on('select2:select', function (e) {
      var variant = e.params.data;
      if(variant){
        var $chose = $(this).parent().find('.chose')
        var $variantId = $(this).parent().find('.variant-sku')
        $chose.find('.variant-image img').prop('src', variant.img)
        $variantId.val(variant.product_sku)
      }
    });
  },

  variantFormatResult: function (variant) {
    var markup = "<div class='variant-result'>";
    markup += "<div class='variant-info'><div class='variant-title'>" + variant.text + "</div></div>";
    markup += "<div class='variant-info'><div class='variant-title'>" + variant.shop + "</div></div>";
    markup += "<div class='variant-info'><div class='variant-title'>" + "$" + variant.price + "</div></div>";
    markup += "<div class='variant-info'><div class='variant-title'>" + variant.percentage + "%" + "</div></div>";
    markup += "<div class='variant-info'><div class='variant-title'>" + variant.stock + "u" + "</div></div>";
    if (variant.img !== undefined) {
      markup += "<div class='variant-image img-thumbnail'><img src='" + variant.img + "'/></div>";
    }
    markup += "</div>";
    return markup;
  }
})
