gp.Views['pages/components/cols4_brands_v1/setup'] = Backbone.View.extend({
  events: {
    'change .brand-input': 'onBrandChange'
  },

  initialize: function(options){
    _.bindAll(
      this,
      'initSelect2',
      'brandFormatResult'
    )

    var $el = this.$el

    this.$brandInput = $el.find('.brand-input')
    this.initSelect2()
    this
  },

  initSelect2: function() {
    var url = this.$brandInput.data('brands_url')

    this.$brandInput.select2({
      width: '100%',
      minimumInputLength: 3,
      placeholder: "Select a Brand",
      initSelection: function (element, callback) {
        callback({ id: 1, text: element.data('brand_name') })
      },
      ajax: {
        url: url,
        dataType: 'json',
        quietMillis: 500,
        data: function (title, page) { // page is the one-based page number tracked by Select2
          return {
            q: title, //search term
            page_limit: 10, // page size
            page: page, // page number
          }
        },
        results: function (data, page) {
          var more = (page * 10) < data.total // whether or not there are more results available

          // notice we return the value of more so Select2 knows if more results can be loaded
          return {results: data, more: more}
        },
      },
      formatResult: this.brandFormatResult,
      escapeMarkup: function (m) { return m }
    })

    var $eventSelect = $(".js-example-events")
    $eventSelect.on('select2:select', this.onBrandChange)
  },

  brandFormatResult: function (brand) {
    var markup = "<table class='brand-result'><tr>"
    if (brand.img !== undefined) {
      markup += "<td class='brand-image'><img src='" + brand.img + "'/></td>"
    }
    markup += "<td class='brand-info'><div class='brand-title'>" + brand.text + "</div>"
    markup += "</td></tr></table>"
    return markup
  },

  onBrandChange: function (evt) {
    var brand = evt.added
    var $chose = this.$el.find('.chose')
    var $brandId = this.$el.find('.brand-id')
    $chose.find('.brand-image img').prop('src', brand.img)
    $chose.find('.brand-name').text('@' + brand.login)
    $brandId.val(brand.id)
  }
})
