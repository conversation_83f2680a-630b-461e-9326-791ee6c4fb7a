gp.Views['pages/components/cols5_generic_v1/setup'] = Backbone.View.extend({
  initialize: function(options){
    new gp.Views['pages/components/picture_form']({
      el: this.$el.find('.picture-form'),
      componentName: 'Cols5GenericV1'
    }).on({
      'uploaded': this.uploaded,
      'removed': this.removed
    }, this)
  },

  uploaded: function(data) {
    this.$el.find('input.desktop_picture_id').val(data.id)
  },

  removed: function() {
    this.$el.find('input.desktop_picture_id').val('')
  }
})