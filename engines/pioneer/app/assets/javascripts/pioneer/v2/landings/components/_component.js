gp.Views['pages/component'] = Backbone.View.extend({
  events: {
    'submit form': 'save',
    'click .remove': 'remove',
    'click .deleteDateStart': 'deleteDateStart',
    'click .deleteDateEnd': 'deleteDateEnd'
  },

  initialize: function(options){
    var $el = this.$el
    this.$form = $el.find('form')
    this.$formActions = this.$form.find('.actions')
    this.$removeBtn = this.$formActions.find('.remove')
    // Initialize the Hero Components
    _($el.find('.setup')).each(function(setup){
      new gp.Views['pages/components/' + $el.data('component-name') + '/setup']({
        el: setup
      })
    })

    this.on({
      'saving': this.saving,
      'save:success': this.saved,
      'removing': this.removing,
      'remove:success': this.removed,
      'save:fail remove:fail': this.failed,
      'deleteDateStart': this.deleteDateStart,
      'deleteDateEnd': this.deleteDateEnd
    }, this)

    this.enableForm(true)
  },

  save: function(evt) {
    evt.preventDefault()
    var that = this,
        url = this.$form.prop('action'),
        method = this.$form.prop('method'),
        data = new FormData(this.$form[0])
    this.trigger('saving')
    $.ajax({
      url:      url,
      data:     data,
      dataType: 'json',
      type:     method,
      cache:    false,
      processData: false,
      contentType: false
    }).done(function(response){
      that.trigger('save:success', response['data'])
    }).fail(function(){
      that.trigger('save:fail', arguments)
    })
  },

  deleteDateStart: function(evt) {
    $(evt.delegateTarget).find('input#component_starts_on').val("NULL")
  },

  deleteDateEnd: function(evt) {
    $(evt.delegateTarget).find('input#component_ends_on').val("NULL")
  },

  remove: function(evt) {
    evt.preventDefault()
    var that = this,
        $field = $(evt.currentTarget)
    if ($field.data('action')) {
      this.trigger('removing')
      $.ajax({
        url:      $field.data('action'),
        dataType: 'json',
        type:     'delete',
        cache:    false,
        processData: false,
        contentType: false
      }).done(function(response){
        that.trigger('remove:success', response['data'])
      }).fail(function(){
        that.trigger('remove:fail', arguments)
      })
    } else {
      this.undelegateEvents()
      this.$el.remove()
    }
  },

  saving: function(){
    this.enableForm(false)
    this.progressMessage('Saving component')
  },
  removing: function(){
    this.enableForm(false)
    this.progressMessage('Removing component')
  },

  saved: function(data){
    this.enableForm(true)
    if( typeof data.update_path === 'string' ){
      this.setupUpdatePath(data.update_path)
    }
    this.progressMessage('Component successfully saved at '+ moment(data.component.updated_at).format('LT')).addClass('success')
  },
  removed: function(data){
    this.$el.remove()
  },

  failed: function(){
    this.enableForm(true)

    this.progressMessage('There was a problem trying to complete the action').addClass('failed')
  },

  enableForm: function(status){
    this.$form.find('input, select, textarea').each(function(){ this.disabled = !status })
    if( status ){
      this.delegateEvents()
      this.$removeBtn.removeClass('disabled')
    } else {
      this.undelegateEvents()
      this.$removeBtn.addClass('disabled')
    }
  },

  progressMessage: function(text){
    var $progressMsg = this.$formActions.find('.progress-msg')
    if( $progressMsg.length === 0 ){
      $progressMsg = $("<span class='progress-msg'></span>").prependTo(this.$formActions.find('.buttons'))
    }
    $progressMsg.removeClass('success failed').text(text)
    return $progressMsg
  },

  setupUpdatePath: function(path){
    var $updateMethod = this.$form.find('input[name="_method"]')
    if( $updateMethod.length === 0 ){
      this.$form.prop('action', path)
      this.$form.prepend("<input type='hidden' name='_method' value='put'>")
      this.$removeBtn.data('action', path)
    }
  }

})
