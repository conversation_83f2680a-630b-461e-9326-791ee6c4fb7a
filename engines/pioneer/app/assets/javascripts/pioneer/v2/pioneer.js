;(function(){
  $window = $(window)
  $document = $(document)

  var gp = this.gp = {
    action: {},
    controller: {},
    Helpers: {},
    Views: {}
  }
  gp.pubSub = _({}).extend( Backbone.Events )
  gp.baseUrl = '/admin/pioneer'

  // Set controller and action variables.
  var $body = $('body')
  var method = $body.attr('id').split('-')
  gp.controller[method[0]] = true
  gp.action[method[1]] = true
  gp.menuSection = $body.data('section')

}).call(this)
