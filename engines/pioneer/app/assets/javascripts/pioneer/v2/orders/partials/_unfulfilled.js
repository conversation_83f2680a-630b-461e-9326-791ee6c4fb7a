gp.Views['orders/show/unfulfilled'] = Backbone.View.extend({
  events: {
    'click .actions .fulfill-items-with-shipnow':   'initFulfillWithShipnow'
  },

  initialize: function(options){
    var $self = this.$el
    this.unfulfilledShipment = $self.data('unfulfilled-shipment')
    this.$title = $self.find('> h5')
    this.$items = $self.find('> .items')
    this.$actions = $self.find('> .actions')

    // Pub/Sub Events (need to explicit bind the "this")
    gp.pubSub.on({
      'order:fulfillment:init': this.hideElements,
      'order:fulfillment:cancelled order:fulfillment:init:fail': this.fulfillmentOver
    }, this)
  },

  initFulfillWithShipnow: function(evt){
    var items_ids = $(evt.target).data('ids')
    this.fulfillmentFlow = new gp.Views['order/show/fulfillment/shipnow']({
      items_ids: items_ids,
      shipment_id: this.unfulfilledShipment
    })
    this.$el.append( this.fulfillmentFlow.render().el )
  },

  hideElements: function(){
    this.$title.hide()
    this.$items.hide()
    this.$actions.hide()
  },

  fulfillmentOver: function(){
    this.fulfillmentFlow.remove()
    this.displayElements()
  },

  displayElements: function(){
    this.$title.show()
    this.$items.show()
    this.$actions.show()
  }
})