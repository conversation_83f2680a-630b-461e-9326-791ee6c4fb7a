;if( !!gp.controller.orders && !!gp.action.show ) (function(){

  gp.Views['orders/show'] = Backbone.View.extend({
    initialize: function(){
      var $self = this.$el

      // Initialize the Unfulfilled Items Component
      var items_to_fulfill = $self.find('main .unfulfilled')
      if( items_to_fulfill.length > 0 ) {
        new gp.Views['orders/show/unfulfilled']({
          el: items_to_fulfill
        })
      }
    }

  })

  // Instantiate the ride view
  new gp.Views['orders/show']({
    el: $('#orders-show .main-wrapper')
  })
})()
