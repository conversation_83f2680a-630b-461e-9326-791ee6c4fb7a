gp.Views['order/show/fulfillment/shipnow'] = Backbone.View.extend({
  className: "shipnow-fulfillment",
  events: {
  'click .actions .cancel-it':      'cancel',
  'click .actions .confirm-button': 'submit'
  },

  initialize: function(options){
    this.items = options.items_ids
    this.shipment = options.shipment_id
    gp.pubSub.trigger('order:fulfillment:init')
  },

  cancel: function(){
    gp.pubSub.trigger('order:fulfillment:cancelled')
  },

  submit: function(){
    var that = this
    var _processUrl = function(){
      return gp.baseUrl+'/ajax/shipments/'+ that.shipment +'/process_with_gateway'
    }
    this.trigger('submit:init')
    $.ajax({
        url: _processUrl(),
        data: {
          gateway: 'Shipnow',
          items_ids: this.items
        },
        type: 'post',
        cache: false
    }).done(function(data){
      that.trigger('submit:success', data)
    }).fail(function(){
      that.trigger('submit:fail')
    })
  },

  render: function(){
    this.initFlow()
    return this;
  },

  initFlow: function(){
    var that = this
    var _initFlowUrl = function(){
      return gp.baseUrl+'/ajax/shipments/'+ that.shipment +'/init_process'
    }
    $.ajax({
        url: _initFlowUrl(),
        data: {
          gateway: 'Shipnow',
          items_ids: this.items
        },
        type:'post',
        cache: false
    }).done(function(data){
      that.$el.html(data)
      that.ready()
    }).fail(function(){
      gp.pubSub.trigger('order:fulfillment:init:fail')
    })
  },

  ready: function(){
    // Local Events
    this.on('submit:init', this.submitInit)
    this.on('submit:success', this.submitSuccess)
    this.on('submit:fail', this.submitFail)
  },

  submitInit: function(){
    this.enableForm(false)
  },

  submitSuccess: function(response){
    if( response.status === 'success' ){
      window.location.reload(true)
    }
  },

  submitFail: function(){
    this.enableForm(true)
  },

  enableForm: function(enable){
    var $cancelBtn = this.$el.find('.actions .cancel-it')
    var $submitBtn = this.$el.find('.actions .confirm-button')
    if( enable ){
      $cancelBtn.removeClass('disabled')
      $submitBtn.removeClass('disabled')
      this.delegateEvents()
    } else {
      this.undelegateEvents()
      $cancelBtn.addClass('disabled')
      $submitBtn.addClass('disabled')
    }
  }
})