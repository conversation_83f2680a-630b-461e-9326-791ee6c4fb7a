;(function(){
  if( !gp.controller.menu || !( gp.action.edit || gp.action.new ) ) return

  function loadItemTemplate(){
    return $('.hide .new-menu-item').html()
  }

  function loadSubitemTemplate(){
    return $('.hide .new-subitem-menu-item').html()
  }

  function initNewItemButton(){
    $('.new-menu-item-link').click(function(e){
      e.preventDefault()
      $(this).parent().parent().before(newMenuItemTemplate)
      $(this).parent().parent().prev().find('.remove-item').click(function(e){
        $(this).parent().parent().parent().remove()
      })
      $(this).parent().parent().prev().find('.new-subitem-menu-item-link').click(function(e){
        e.preventDefault()
        $(this).parent().parent().before(newLeafMenuItemTemplate)
        $(this).parent().parent().prev().find('.remove-subitem').click(function(e){
          $(this).parent().parent().remove()
        })
      })
    })
  }

  function initNewSubitemButton(){
    $('.new-subitem-menu-item-link').click(function(e){
      e.preventDefault()
      $(this).parent().parent().before(newLeafMenuItemTemplate)
      $(this).parent().parent().prev().find('.remove-subitem').click(function(e){
        $(this).parent().parent().remove()
      })
    })
  }

  function initRemoveItemButton(){
    $('.remove-item').click(function(e){
      $(this).parent().parent().parent().remove()
    })
  }

  function initRemoveSubitemButton() {
    $('.remove-subitem').click(function(e){
      $(this).parent().parent().remove()
    })
  }

  function initSaveButton() {
    $('form').on('submit', function(e){
      function stopSubmit(){
        e.stopPropagation()
        e.preventDefault()
        return false
      }

      var $inputs_required = $('form input.required');
      var halt = false

      $.each($inputs_required, function(index, input){
        $input = $(input)
        if($input.val() == ""){
          $input.addClass('error')
          halt = true
          $input.click(function(){
            $input.removeClass('error')
          })
        }else{
          $input.removeClass('error')
        }
      })

      if(halt){
        alert('Please fill all required fields before submit')
        return stopSubmit()
      }
    })
  }

  var newMenuItemTemplate = loadItemTemplate()
  var newLeafMenuItemTemplate = loadSubitemTemplate()

  initNewItemButton()
  initNewSubitemButton()
  initRemoveItemButton()
  initRemoveSubitemButton()
  initSaveButton()

}).call(this)
