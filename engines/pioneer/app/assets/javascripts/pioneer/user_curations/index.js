$(function(){
    if( !gp.controller.user_curations ) return

    function initUsersSelector() {
      var usersSelector = $('#users')
      var CuratedUsersUrl = usersSelector.data('users_url')

      usersSelector.select2({
        placeholder: 'Specify login...',
        multiple: true,
        minimumInputLength: 2,
        ajax: {
          url: CuratedUsersUrl,
          data: function(term) { return {login: term} },
          results: function (data, page) {
            return {results: data}
          }
        }
      })

      return usersSelector
    }

    $('.remove').click(function(){
      $(this).closest('form').submit()
    })

    $('#add-curated-user').submit(function(e){
      if(usersSelector.select2('val').length < 1){
        e.preventDefault()
        return false
      }
    })

    var usersSelector = initUsersSelector()

});
