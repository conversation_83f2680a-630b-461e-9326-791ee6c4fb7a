;(function(){
  var $add_fields = $('.add_fields')
  var $remove_fields = $('.remove_fields')

  $add_fields.on('click', addAdminRole)
  $remove_fields.on('click', removeAdminRole)

  function addAdminRole(e) {
    var regexp, time;
    time = new Date().getTime();
    regexp = new RegExp($('.add_fields').data('id'), 'g');
    $('.add_fields').before($('.add_fields').data('fields').replace(regexp, time));
    return e.preventDefault();
  }

  function removeAdminRole(e) {
    selector = e.currentTarget;
    fieldset = $(selector).closest('fieldset');
    fieldset.find('input[type=hidden]').val('true');
    fieldset.hide();
    return e.preventDefault();
  }

})()
