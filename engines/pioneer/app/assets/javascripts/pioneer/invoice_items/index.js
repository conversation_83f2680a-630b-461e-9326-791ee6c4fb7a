;(function(){
  $(document).ready(function() {
    $('select#with_shop_id').select2({
      placeholder: "Seleccione",
      width: '100%',
      multiple: true,
      allowClear: true
    });
  });

  $(document).ready(function() {
    $('select#shops').select2({
      placeholder: "Seleccione",
      width: '100%',
      multiple: true,
      allowClear: true
    });
  });

  $(document).on('click', '.create-invoice-order', function(){
    store_id = document.getElementById('with_store_id').value
    shop_id = $('#with_shop_id').val()
    created_from = document.getElementById('created_from').value
    created_to = document.getElementById('created_to').value
    fulfilled_from = document.getElementById('fulfilled_from').value
    fulfilled_to = document.getElementById('fulfilled_to').value
    with_shipment_status = document.getElementById('with_shipment_status').value
    with_invoice_status = document.getElementById('with_invoice_status').value
    search_query = document.getElementById('search_query').value

    url = "/admin/pioneer/invoice_reports/new?" +
        "store_id=" + store_id +
        "&shop_ids=" + shop_id +
        "&created_from=" + created_from +
        "&created_to=" + created_to +
        "&fulfilled_from=" + fulfilled_from +
        "&fulfilled_to=" + fulfilled_to +
        "&with_shipment_status=" + with_shipment_status +
        "&with_invoice_status=" + with_invoice_status +
        "&search_query=" + search_query

    window.location.replace(url);
  });

  $(document).on('click', '.in-process-invoice-items', function(){
    store_id = document.getElementById('with_store_id').value
    shop_id = $('#with_shop_id').val()
    created_from = document.getElementById('created_from').value
    created_to = document.getElementById('created_to').value
    fulfilled_from = document.getElementById('fulfilled_from').value
    fulfilled_to = document.getElementById('fulfilled_to').value
    with_shipment_status = document.getElementById('with_shipment_status').value
    with_invoice_status = 'in_process'
    search_query = document.getElementById('search_query').value

    url = "/admin/pioneer/invoice_items?" +
        "with_store_id=" + store_id +
        "&created_from=" + created_from +
        "&created_to=" + created_to +
        "&fulfilled_from=" + fulfilled_from +
        "&fulfilled_to=" + fulfilled_to +
        "&with_shipment_status=" + with_shipment_status +
        "&with_invoice_status=" + with_invoice_status +
        "&search_query=" + search_query

    if (shop_id) {
      shop_id.forEach(value => url = url + "&with_shop_id%5B%5D=" + value);
    };

    window.location.replace(url);
  });

  $(document).on('click', '.pending-invoice-items', function(){
    store_id = document.getElementById('with_store_id').value
    shop_id = $('#with_shop_id').val()
    created_from = document.getElementById('created_from').value
    created_to = document.getElementById('created_to').value
    fulfilled_from = document.getElementById('fulfilled_from').value
    fulfilled_to = document.getElementById('fulfilled_to').value
    with_shipment_status = document.getElementById('with_shipment_status').value
    with_invoice_status = 'pendig'
    search_query = document.getElementById('search_query').value

    url = "/admin/pioneer/invoice_items?" +
        "with_store_id=" + store_id +
        "&created_from=" + created_from +
        "&created_to=" + created_to +
        "&fulfilled_from=" + fulfilled_from +
        "&fulfilled_to=" + fulfilled_to +
        "&with_shipment_status=" + with_shipment_status +
        "&with_invoice_status=" + with_invoice_status +
        "&search_query=" + search_query

    shop_id.forEach(value => url = url + "&with_shop_id%5B%5D=" + value);

    window.location.replace(url);
  });

  $(document).on('click', '.invoiced-invoice-items', function(){
    store_id = document.getElementById('with_store_id').value
    shop_id = $('#with_shop_id').val()
    created_from = document.getElementById('created_from').value
    created_to = document.getElementById('created_to').value
    fulfilled_from = document.getElementById('fulfilled_from').value
    fulfilled_to = document.getElementById('fulfilled_to').value
    with_shipment_status = document.getElementById('with_shipment_status').value
    with_invoice_status = 'invoiced'
    search_query = document.getElementById('search_query').value

    url = "/admin/pioneer/invoice_items?" +
        "with_store_id=" + store_id +
        "&created_from=" + created_from +
        "&created_to=" + created_to +
        "&fulfilled_from=" + fulfilled_from +
        "&fulfilled_to=" + fulfilled_to +
        "&with_shipment_status=" + with_shipment_status +
        "&with_invoice_status=" + with_invoice_status +
        "&search_query=" + search_query

    shop_id.forEach(value => url = url + "&with_shop_id%5B%5D=" + value);

    window.location.replace(url);
  });

  $(document).on('click', '.show-errors-details', function(){
    id = $(this).data("id")
    dialog = $('#errors-details')
    dialog.modal('show')
  });
})()