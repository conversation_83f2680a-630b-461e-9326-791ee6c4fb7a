;if( gp.controller.promotions && (gp.action.new || gp.action.edit) ) (function(){

  var $el = $("#promotion-form")
  var $form = $el.find('form')
  var $submit = $el.find('.button')
  var $dates = $el.find('.date')
  $dates.datetimepicker({
    timepicker:false,
    format:'Y/m/d'
  });

  var $condition = $el.find('#mkp_promotion_condition_operant')
  $condition.on('change', function(e){
    var value = $(this).val()
    var $optionSelected = $el.find("option[value="+value+"]")
    var $arguments = $el.find(".arguments")

    $arguments.select2('val', {}, true)
    $el.find('.optional').removeClass('active').addClass('hide')
    $el.find('.optional-condition').removeClass('active').addClass('hide')
    if ($optionSelected.data('arguments')){
      var argument = $optionSelected.data('arguments')
      $el.find('.'+argument).removeClass('hide').addClass('active')
    }

    $el.find('.optional-condition').removeClass('required')
    if ($optionSelected.data('operators')){
      $el.find('.optional-condition').removeClass('hide').addClass('active')
      $el.find('.optional-condition').addClass('required')
    }
  })

  var $action = $el.find('#mkp_promotion_action_method')
  $action.on('change', function(e){
    var value = $(this).val()
    var $optionSelected = $el.find("option[value="+value+"]")
    var $arguments = $el.find(".action-arguments")

    $arguments.select2('val', {}, true)
    $el.find('.optional-arguments-action').removeClass('active').addClass('hide')
    if ($optionSelected.data('arguments')  == 'free_item_involved'){
      var argument = $optionSelected.data('action-arguments')
      $el.find('.optional-arguments-action').removeClass('hide').addClass('active')
    }

  })

  var $selects = $el.find('.select-wrapper select')
  $selects.select2()

  $submit.click(function(e){
    e.preventDefault();

    var validForm = true
    if ($el.find(".optional.active").length == 1 && $el.find(".optional.active select").val() == null) {
      $el.find(".optional.active select").addClass('error')
      validForm = false
    }else{
      $el.find(".optional.active select").removeClass('error')
      validForm = true
    }
    $el.find('.required').each(function(){
      var $field = $(this).find('input, select')
      if ($field.val().length == 0) {
        $(this).addClass('error')
        validForm = false
      } else {
        $(this).removeClass('error')
      }
    })
    if (validForm) {
      $form.submit();
    }
  })

})()
