;(function(){
  var $el = $('#searches-container')
  if( !$el.length ) return

  var $formContainer = $('.suggestion-form-container')
  var $suggestions = $el.find('.suggestion-select')
  var $asAliasLink = $el.find('.convert_to_alias')

  $suggestions.select2()

  $el.find('.convert').on('click', convertClick)
  $el.find('.convert_to_alias').on('click', convertToAlias)

  function convertClick(e) {
    $field = $(e.currentTarget)
    data = {
      id: $field.data('id'),
      term: $field.data('term'),
      count: $field.data('count')
    }
    $el.trigger('searches:convert', data)
    $formContainer.show()
  }

  function convertToAlias(e) {
    var $field = $(e.currentTarget)
    var $modal = $el.find('#alias-modal')
    var $form = $modal.find('form')

    $modal.find('.search-term').text($field.data('term'))
    $form.prop('action', $field.data('action'))
  }


})()
