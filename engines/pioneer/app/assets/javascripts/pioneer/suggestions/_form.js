;(function(){
  var $suggestions = $('#suggestions-container')
  var $searches = $('#searches-container')
  var $el = $('.suggestion_form')
  if( !$el.length ) return

  var $formContainer = $('.suggestion-form-container')
  var $form = $el.find('form')
  var $category = $el.find('#category-select')
  var $sports = $el.find('#sports-select')
  var $gender = $el.find('#gender-select')
  var $previewLink = $el.find('.preview')
  var $preview = $el.find('.preview-container')
  var $submit = $el.find('.submit input')

  var $termField = $form.find('#suggestion_term')
  var $scoreField = $form.find('#suggestion_score')
  var $queryField = $form.find('#suggestion_data_query')
  var $onsaleCheckBox = $form.find('#suggestion_data_d')
  var $formButtons = $form.find('.form-buttons')

  $category.select2()
  $sports.select2()
  $gender.select2()

  $suggestions.on('suggestion:edit', loadEditForm)
  $searches.on('searches:convert', loadConvert)
  $previewLink.on('click', clickPreview)

  function clickPreview(e) {
    var url = $(e.currentTarget).data('action')
    $.ajax({
      url: url,
      data: $form.serialize()
    }).done(function(data){
      $preview.loadTemplate($('#preview-results-template'), data)
    })
  }

  function loadEditForm(e, suggestion) {
    clearForm()

    $form.data('edit-suggestion-id', suggestion.id)
    $form.prop('action', $form.prop('action') + '/' + suggestion.id)
    $form.prepend("<input type='hidden' name='_method' value='put' class='hidden-method'>")
    $form.find('.submit input').val('Edit')
    $formButtons.find('.cancel').remove()
    $formButtons.append("<div class='small button alert cancel'>Cancel</div>")
    $form.find('.cancel').on('click', clearForm)

    $termField.val(suggestion.term)
    $scoreField.val(suggestion.score)
    $queryField.val(suggestion.data.query)
    $onsaleCheckBox.prop("checked",suggestion.data.d)
    $sports.val(suggestion.data.sp).trigger("change")
    $gender.val(suggestion.data.g).trigger("change")
    $category.val(suggestion.data.c).trigger("change")

    $(window).scrollTop(0)
  }

  function loadConvert(e, search) {
    clearForm()

    $formContainer.find('.search-message').show()
    $form.data('edit-suggestion-id', search.id)
    $form.prop('action', $form.prop('action') + '/' + search.id)
    $form.prepend("<input type='hidden' name='_method' value='put' class='hidden-method'>")
    $termField.val(search.term)
    $scoreField.val(search.count)
    $formButtons.find('.cancel').remove()
    $formButtons.append("<div class='small button alert cancel'>Cancel</div>")
    $form.find('.cancel').on('click', cancelConvert)

    $(window).scrollTop(0)
  }

  function cancelConvert(e) {
    $formContainer.hide()
    clearForm()
  }

  function clearForm(e) {
    $form.find('.form-message').hide()
    $form.removeData('edit-suggestion-id')
    $form.find('.hidden-method').remove()
    $form.prop('action', $el.data('create-action'))
    $form[0].reset()
    $termField.prop('readonly', false)
    $termField.removeClass('readonly')
    $form.find('.submit input').val('Create Suggestion')
    $sports.trigger('change')
    $gender.trigger('change')
    $category.trigger('change')
    $form.find('.cancel').remove()
    $preview.html('')
    $form.find('.required').removeClass('error')
  }

  $submit.click(function(e){
    e.preventDefault();
    var validForm = true

    $form.find('.required').each(function(){
      var $field = $(this).find('input')
      if ($field.val().length == 0) {
        $(this).addClass('error')
        validForm = false
      } else {
        $(this).removeClass('error')
      }
    })

    if (validForm) {
      $form.submit();
    }
  })

})()
