;(function(){
  var $global = $('#suggestions-container')
  var $el = $('.suggestion-detail')
  if( !$el.length ) return

  $el.find('.edit').on('click', editClick)
  $('.more').on('click', clickSuggestionDetail)

  function editClick(e) {
    var $field = $(e.currentTarget)
    var id = $field.closest('.suggestion-detail').data('id')
    var $form = $global.find('form')
    if ($form.data('edit-suggestion-id') == id) {
      $(window).scrollTop(0)
    } else {
      $.ajax({
        url: getActionUrl($field),
      }).done(function(data){
        $global.trigger('suggestion:edit', data)
      })
    }
  }

  function clickSuggestionDetail(e) {
    $(e.currentTarget).closest('.suggestion-detail').find('.suggestion-attributes').toggle()
  }

  function getActionUrl($field) {
    return $field.closest('.suggestion-detail').data('action')
  }

})()
