;(function(){
    $(document).on('click', '.process-customer-delete', function(){
      that = this
      id = $(that).data("id")
      email = $(that).data("email")
      type = $(that).data("type")
      dialogId = $('#dialog-customer-delete-' + id)
      $('#link-' + id).hide()
      $(that).hide()
      call_delete_ajax("customers", email, dialogId, id)
    });

    
    async function call_delete_ajax(object_kind, email, dialog_id, id){
      customer = document.querySelector(`.customer-${id}`)
      dialog = $('#confirm-modal');
      dialog.modal('show');
      body = $("#confirm-body");
      body_confirm_text = "¿Seguro desea cancelar el cliente " + email + "?"
      
      body.empty().append(body_confirm_text)
      
      $.ajax({
        url: object_kind + `/${ id }`,
        type: 'post',
        data: {"_method":"delete"},
        async: true
      }).done(function () {
        $('.status-' + id).text("Cancelled");
        cancel = dialog_id.find('.cancel')
        dialog_id.modal('hide');
        customer.remove();
      }).fail(function(jqXHR, textStatus) {
        cancel = dialog_id.find('.cancel')
        dialog.modal('hide');
        dialog_id.modal('hide');
      })
    }
  })()