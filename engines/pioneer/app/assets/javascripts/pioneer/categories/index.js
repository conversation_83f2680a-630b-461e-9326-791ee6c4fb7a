;if( gp.controller.categories) (function(){
  var $mass_unit = $('#mkp_category_mass_unit')
  var $length_unit = $('#mkp_category_length_unit')

  $mass_unit.on('change', function(){

    var mass = $mass_unit.val()
    if(mass == 'kilograms')
      $length_unit.val('millimeters')
    else if (mass == 'pounds')
      $length_unit.val('inches')
  })

  $length_unit.on('change', function(){
    var length = $length_unit.val()
    if(length == 'millimeters')
      $mass_unit.val('kilograms')
    else if (length == "inches")
      $mass_unit.val('pounds')
  })
})()