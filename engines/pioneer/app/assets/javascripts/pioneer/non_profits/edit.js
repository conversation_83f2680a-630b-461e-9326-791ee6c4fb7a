;(function(){
  if( !gp.controller.non_profits || !gp.action.edit ) return

  $('#shop_id, #profile_manufacturer_id, #profile_sports_ids').select2({
    width: '100%',
    allowClear: true
  })

  var $startDate = $('#start_date')
  var $endDate = $('#end_date')

  $startDate.fdatepicker({ format: 'yyyy-mm-dd' })
  $endDate.fdatepicker({ format: 'yyyy-mm-dd' })

  var $startDateText = $('.start-date-text')
  var $endDateText = $('.end-date-text')

  $startDate.on('input changeDate', function(){
    $startDateText.html(moment($startDate.val()).format('LL'))
  })

  $endDate.on('input changeDate', function(){
    $endDateText.html(moment($endDate.val()).format('LL'))
  })

  $startDate.add($endDate).trigger('input')

  $('#non_profit_profile_attributes_manufacturer_id').select2({
    width: '100%',
    allowClear: true
  })

  ;(function(){
    var $usersSelector = $('.add-non-profit-manager #managers_user_id')
    var restrictionUsersUrl = $usersSelector.data('users-url')

    $usersSelector.select2({
      placeholder: 'User login...',
      multiple: false,
      minimumInputLength: 2,
      ajax: {
        url: restrictionUsersUrl,
        data: function(term) { return {login: term} },
        results: function (data, page) {
          return { results: data }
        }
      }
    })
  })()

})()
