;if( gp.controller.coupons && gp.action.new ) (function(){

  var $dates = $('#mkp_coupon_network_starts_at, #mkp_coupon_network_expires_at')
  $dates.datetimepicker();

  var $policy = $('#mkp_coupon_network_policy')
  var $policies = $('.coupon-policy')
  $policy.on('change', function(){
    var policy = $policy.val()
    $policies
      .hide()
        .find('input')
          .prop('disabled', true)
          .end()
      .filter('#coupon-policy-' + policy)
        .show()
        .find('input')
          .prop('disabled', false)
  })

  function initUsersRestrictionSelector() {
    var usersSelector = $('#users-restriction')
    var restrictionUsersUrl = usersSelector.data('restriction_users_url')

    usersSelector.select2({
      placeholder: 'Type an email...',
      multiple: true,
      minimumInputLength: 2,
      ajax: {
        url: restrictionUsersUrl,
        data: function(params) {
          console.log(params);
          return {
            q: params.term,
            search: params.term,
            email: params.term
          }
        },
        processResults: function (data) {
          var result = {
            results: data
          }
          console.log(result);
          return result
        }
      }
    }).prop("disabled", true)

    return usersSelector
  }

  function initRestriction(name, selector) {
    var actionRadioButtons = $('input[name=' + name + '_restriction_action]')
    actionRadioButtons.click(function(e) {
      selector.prop("disabled", false)
      var action = $(this).val()
      var selectorPrevName = selector.attr('name')
      var selectorNewName = selectorPrevName.replace('included_' + name, action).replace('excluded_' + name, action)
      selector.attr('name', selectorNewName)
    })
  }

  function initManufacturersRestrictionSelector() {
    var manufacturersSelector = $('#manufacturers-restriction')
    manufacturersSelector.select2({
      theme: "bootstrap"
    }).prop("disabled", true)

    return manufacturersSelector
  }

  function initCategoriesRestrictionSelector() {
    var categoriesSelector = $("#categories-restriction")
    categoriesSelector.select2({
      theme: 'bootstrap',
      formatResult: formatCategoryForSelector
    }).prop("disabled", true)

    return categoriesSelector
  }

  function initProductsRestrictionSelector() {
    var productsSelector = $("#products-restriction")
    var restrictionProductsUrl = productsSelector.data('restriction_products_url')

    productsSelector.select2({
      multiple: true,
      minimumInputLength: 2,
      ajax: {
        url: restrictionProductsUrl,
        data: function(params) {
          console.log(params);
          return { q: params.term };
        },
        processResults: function (data) {
          var result = { results: data };
          console.log(result);
          return result
        }
      }
    }).prop("disabled", true)

    return productsSelector
  }

  function initShopsRestrictionSelector() {
    var shopsSelector = $("#shops-restriction")
    shopsSelector.select2({
      theme: 'bootstrap'
    })
    shopsSelector.select2('enable', false)

    return shopsSelector
  }

  var formatCategoryForSelector = function(category) {
    var originalOption = category.element
    var depth = $(originalOption).data('depth')
    if(depth){
      var indent = Array(depth*4).join('&nbsp;')
      var name = indent + category.text
    } else {
      var name = category.text
    }
    return name;
  }

  function initRestrictions() {
    var manufacturersSelector = initManufacturersRestrictionSelector()
    initRestriction('manufacturers', manufacturersSelector)

    var usersSelector = initUsersRestrictionSelector()
    initRestriction('users', usersSelector)

    var categoriesSelector = initCategoriesRestrictionSelector()
    initRestriction('categories', categoriesSelector)

    var shopsSelector = initShopsRestrictionSelector()
    initRestriction('shops', shopsSelector)

    var productsSelector = initProductsRestrictionSelector()
    initRestriction('products', productsSelector)
  }

  initRestrictions()

})()
