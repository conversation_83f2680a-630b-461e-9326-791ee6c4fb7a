;(function(){
  if( !gp.controller.products ) return

  validateCheckedItems();

  $(document).on('click', '#check-all', function(){
    var checked = $(this).is(':checked');
    $('.check-product').each(function(i, obj) {
      $(obj).prop('checked', checked);
    });

    validateCheckedItems();
  });

  $(document).on('click', '.check-product', function(){
    validateCheckedItems();
  });

  $(document).on('click', '.approve-selected-button', function(e){
    e.preventDefault();
    if($(this).attr('disabled') != 'disabled'){
      $(this).attr('disabled', true);
      var items = [];

      $('.check-product').each(function(i, obj) {
        if($(obj).is(':checked')){
          items.push({
            product_id: $(obj).attr('data-product-id'),
            store_id: $(obj).attr('data-store-id')
          })
        }
      });

      $.ajax({
        url: "/admin/pioneer/products_stores/batch_update",
        type: 'post',
        data: JSON.stringify({ items: items, approve: true }),
        contentType: 'application/json',
        dataType: 'json'
      }).done(function(){
        location.reload();
      });
    }
  });

  $(document).on('click', '.reject-selected-button', function(e){
    e.preventDefault();
    if($(this).attr('disabled') != 'disabled'){
      $(this).attr('disabled', true);
      var items = [];

      $('.check-product').each(function(i, obj) {
        if($(obj).is(':checked')){
          items.push({
            product_id: $(obj).attr('data-product-id'),
            store_id: $(obj).attr('data-store-id')
          })
        }
      });

      $.ajax({
        url: "/admin/pioneer/products_stores/batch_update",
        type: 'post',
        data: JSON.stringify({ items: items, approve: false }),
        contentType: 'application/json',
        dataType: 'json'
      }).done(function(){
        location.reload();
      });
    }
  });

  $(document).on('click', '.order-visibility-selected-button', function(e){
    e.preventDefault();
    if($(this).attr('disabled') != 'disabled'){
      $(this).attr('disabled', true);
      var items = [];

      $('.check-product').each(function(i, obj) {
        if($(obj).is(':checked')){
          items.push({
            product_id: $(obj).attr('data-product-id'),
            store_id: $(obj).attr('data-store-id')
          })
        }
      });
      order_visibility_number = $('#order-visibility-number').val();
      $.ajax({
        url: "/admin/pioneer/products_stores/change_order_visibility",
        type: 'post',
        data: JSON.stringify({ items: items, 'order_visibility': order_visibility_number }),
        contentType: 'application/json',
        dataType: 'json'
      }).done(function(){
        location.reload();
      });
    }
  });

})()

function validateCheckedItems(){
  var checkedCount = 0;
  $('.check-product').each(function(i, obj) {
    if($(obj).is(':checked')){
      checkedCount++;
    }
  });

  if(checkedCount > 0){
    $('.approve-selected-button').attr('disabled', false);
    $('.reject-selected-button').attr('disabled', false);
  }else{
    $('.approve-selected-button').attr('disabled', true);
    $('.reject-selected-button').attr('disabled', true);
  }
}