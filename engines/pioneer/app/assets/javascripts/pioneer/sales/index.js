;(function(){
  if( !gp.controller.sales ) return
  $(document).on( "click", "input:checkbox", function( event ) {
    class_to_find = "." + this.getAttribute('class')
    flag = 0;
    $(class_to_find).each(function(){
      if($(this).is(':checked'))
        flag = 1;
    });

    $(".process-suborder-refund").each(function(){
      if(this.getAttribute("data-suborder-id") == class_to_find.replace(/[^0-9]/gi, '')){
        this.disabled = (flag == 0)
      }
    });
    $(".process-order-refund").each(function(){
      if(this.getAttribute("data-id") == class_to_find.replace(/[^0-9]/gi, '')){
        this.disabled = (flag == 0)
      }
    });
  });

  $(document).on('click', '.process-order', function(){
    that = this
    id = $(that).data("id")
    dialogId = $('#dialog-' + id)
    $('#link-' + id).hide()
    $(that).hide()
    product_ids = $("#dialog-refund-" + id + " input:checkbox:checked").map(function() {return this.value;}).get()

    cancel_data = {
      id: id,
      type: dialogId.find('.types').val(),
      reason: dialogId.find('.reasons').val(),
      products: product_ids.join()
    }

    call_cancel_ajax("cancel", "orders", id, "orden", cancel_data, dialogId, -1)
  });

  $(document).on('click', '.process-suborder', function(){
    that = this
    id = $(that).data("id")
    suborder_id = $(this).data("suborder-id")
    dialogId = $('#dialog-refund-' + id + '-' + suborder_id )
    $('#link-' + id + '-' + suborder_id).hide()
    $(that).hide()
    product_ids = $("#dialog-refund-" + id + " input:checkbox:checked").map(function() {return this.value;}).get()

    cancel_data = {
      type: dialogId.find('.types').val(),
      reason: dialogId.find('.reasons').val(),
      suborder_id: suborder_id,
      products: product_ids.join()
    },


    call_cancel_ajax("cancel_suborder", "orders", id, "suborden", cancel_data, dialogId, -1)
  });

  $(document).on('click', '.process-purchase-cancel', function(){
    that = this
    id = $(that).data("id")
    type = $(that).data("type")
    dialogId = $('#dialog-purchase-cancel-' + id)
    $('#link-' + id).hide()
    $(that).hide()
    cancel_data = {
      id: id,
          type: type,
          reason: dialogId.find('.reasons').val(),
          products: product_ids.join()
    }

    call_cancel_ajax("cancel", "sales", id, "orden", cancel_data, dialogId, product_ids.length)
  });

  $(document).on('click', '.process-state-change', function(){
    that = this
    id = $(that).data("id")
    suborderId = $(that).data("suborder-id")
    type = $(that).data("type")
    dialogId = $('#dialog-state-change-' + id + "-" + suborderId)
    //$('#link-' + id).hide()
    //$(that).hide()
    apply_data = {
      id: id,
      suborder_id: suborderId,
      type: type,
      next_state_action: dialogId.find('.states').val(),
    }

    call_state_change_ajax(id, apply_data, dialogId)
  });

  

  $(document).on('click', '.process-order-refund', function(){
    that = this
    id = $(that).data("id")
    type = $(that).data("type")
    dialogId = $('#dialog-refund-' + id)
    $('#link-' + id).hide()
    $(that).hide()
    product_ids = $("#dialog-refund-" + id + " input:checkbox:checked").map(function() {return this.value;}).get()

    refund_data = {
      id: id,
      type: dialogId.find('.types').val(),
      reason: dialogId.find('.reasons').val(),
      products: product_ids.join()
    }

    call_refund_ajax("refund", "orders", id, "orden", refund_data, dialogId, product_ids.length)
  });

  $(document).on('click', '.process-suborder-refund', function(){
    that = this
    id = $(that).data("id")
    suborderId = $(that).data("suborder-id")
    dialogId = $('#dialog-refund-' + id + '-' + suborderId)
    $('#link-' + id + '-' + suborderId).hide()
    $(that).hide()
    product_ids = $("#dialog-refund-" + id + " input:checkbox:checked").map(function() {return this.value;}).get()

    refund_data= {
      id: id,
      suborder_id: suborderId,
      type: dialogId.find('.types').val(),
      reason: dialogId.find('.reasons').val(),
      products: product_ids.join()
    },

    call_refund_ajax("refund_suborder", "orders", id, "suborden", refund_data, dialogId, product_ids.length)

  });

  $(document).on('click', '.process-purchase-refund', function(){
    that = this
    id = $(that).data("id")
    type = $(that).data("type")
    dialogId = $('#dialog-purchase-refund-' + id)
    $('#link-' + id).hide()
    $(that).hide()
    product_ids = $("#dialog-refund-" + id + " input:checkbox:checked").map(function() {return this.value;}).get()

    refund_data = {
      id: id,
      type: type,
      reason: dialogId.find('.reasons').val(),
      products: product_ids.join()
    }

    call_refund_ajax("refund", "sales", id, "orden", refund_data, dialogId, product_ids.length)
  });

  $(document).on('click', '.process-edit', function(){
    that = this
    id = $(that).data("id")
    type = $(that).data("type")
    dialogId = $('#dialog-edit-' + id)
    $('#link-' + id).hide();

    $.ajax({
      url: "/admin/pioneer/orders/"+ id +"/update_address",
      type: 'post',
      data:{
        reason: dialogId.find('.reasons').val(),
        email: dialogId.find('.email').val(),
        address: dialogId.find('.address').val(),
        street_number: dialogId.find('.street_number').val(),
        address_2: dialogId.find('.address_2').val(),
        type: type
      },
      async: true
    }).done(function(){
        dialogId.modal('hide');
        $('.status-' + id).text("Updated");
    }).fail(function(){
      updateAddress = dialogId.find('.edit')
      updateAddress.html('<h3>La orden no pudo ser actualizada</h3>')
      $(that).hide()
    })
  });

  $(document).on('click', '.show-shipment-details', function(){
    id = $(this).data("id")
    dialog = $('#shipment-details');
    body = dialog.find('.modal-body');

    $.ajax({
      url: "/admin/pioneer/sales/tracking?label_id=" + id,
      type: 'get',
      async: true
    }).done(function(data){
      html = ''
      html += '<b>Operador logístico:</b> ' + data.courier + '</br>';
      html += '<b>Número de seguimiento:</b> ' + data.tracking + '</br>';
      html += '</br>';

      if (data.events.length > 0) {
        html += "<table class='table' style='width:100%'>";
        html +="<tr>"
        html +="<th>Descripción</th>"
        html +="<th>Fecha</th> "
        html +="</tr>"

        $.each(data.events, function(index, value){
          html +="<tr>"
          html +="<td>" + value.description + "</td>"
          html +="<td>" + value.date + "</td>"
          html +="</tr>"
        });

        html += "</table>";
      }else{
        html += '</br>No hay detalles adicionales para este envío';
      }

      body.html(html);
      dialog.modal('show');
    }).fail(function(data){
      body.html(data.responseJSON.error);
      dialog.modal('show');
    });
  });

  $(document).on('click', '.payment-edit', function() {
    var that = this;
    var id = $(that).data("id");
    var dialogId = $('#dialog-payment-edit-' + id);
    var order_id = $(that).data('order_id');

    $('#link-' + id).hide();

    $.ajax({
      url: "/admin/pioneer/orders/" + id + "/update_status_payment",
      type: 'post',
      data: {
        status: dialogId.find('.status').val(),
        order_id: order_id
      },
      async: true
    }).done(function(response){
      dialogId.modal('hide');
      $('.status-' + id).text("Updated");
      $('#successModal').modal('show');
      setTimeout(function(){
        $('#successModal').modal('hide');
      }, 3000);
    }).fail(function(xhr, status, error) {
      console.log("AJAX request failed.");
      var updatePayment = dialogId.find('.edit');
      updatePayment.html('<h3>El estado del pago no pudo ser actualizado</h3>');
      $(that).hide();
    });
  });

  $(document).on('click', '.comment-edit', function(){
    that = this
    id = $(that).data("id")
    dialogId = $('#dialog-comment-edit-' + id)
    $.ajax({
      url: "/admin/pioneer/orders/"+ id +"/update_comments",
      type: 'post',
      data:{
        comment: dialogId.find('.comment').val(),
      },
      async: true
    }).done(function(){
        dialogId.modal('hide');
        $('#link-' + id).hide();
        $('.status-' + id).text("Updated");
        location.reload();
    }).fail(function(){
      updateAddress = dialogId.find('.edit')
      updateAddress.html('<h3>La orden no pudo ser actualizada</h3>')
      that.disabled = true
    })
  });


  $('.chevron').on('click','a',function(e){
    var listableId = e.currentTarget.getAttribute('data-listable-id');
    var listableType = e.currentTarget.getAttribute('data-listable-type');

    expanded = $(e.target).closest(".chevron").find('i').hasClass("fa-chevron-down");

    if(expanded){
      $('#content-'+listableType+'-'+listableId).collapse('hide');
    }else{
      $.ajax({
        url: "/admin/pioneer/sales/" + listableId + "?type=" + listableType,
        type: 'get',
        async: true
      }).done(function(data){
         $('#content-'+listableType+'-'+listableId).html(data);
         $('#content-'+listableType+'-'+listableId).collapse('show');
      }).fail(function(){

      });
    }

    $(e.target).closest(".chevron").find('i').toggleClass('fa-chevron-right fa-chevron-down');
    $(e.target).closest(".chevron").find('i').toggleClass('fa-chevron-right fa-chevron-right');
    return false
  });


  async function call_state_change_ajax(order_id, action_data, dialog_id){
    dialog = await $('#confirm-modal');
    dialog.modal('show');
    body = await $("#confirm-body");
    body.empty().append("¿Está seguro de cambiar el estado de la suborden?")

    setTimeout(() => {
      
      let buttonConfirm = document.getElementById('accept-confirm')
      buttonConfirm.onclick = () => {
        $.ajax({
          url: "/admin/pioneer/orders/" + order_id + "/apply_state_change_suborder",
          type: 'post',
          data: action_data,
          async: true
        }).done(function () {
          state_change = dialog_id.find('.state-change')
          state_change.html('<h3>La suborden ha sido actualizada</h3>')
          dialog.modal('hide');
        }).fail(function () {
          state_change = dialog_id.find('.state-change')
          state_change.html('<h3>La suborden no pudo ser actualizada</h3>')
          dialog.modal('hide');
        })
      }
    
    }, 1000);

  }
  
  async function call_cancel_ajax(action, object_kind, object_id, object_text, action_data, dialog_id, products_amount){
    dialog = await $('#confirm-modal');
    dialog.modal('show');
    body = await $("#confirm-body");
    body_confirm_text = "";
    if (products_amount == -1)
    {
      body_confirm_text = "¿Seguro desea cancelar la " + object_text + " (ID: "+ object_id + ") ?";
    }
    else 
    {
      body_confirm_text = "Esta seguro que desea cancelar los "+ products_amount +" productos seleccionados";
    }

    body.empty().append(body_confirm_text)
      
    //
    // Begin Modal and set confirm button selector
    //

    setTimeout(() => {
      
      let buttonConfirm = document.getElementById('accept-confirm')
      buttonConfirm.onclick = () => {
        $(buttonConfirm).attr('disabled', true)
        $.ajax({
          url: "/admin/pioneer/" + object_kind + "/" + object_id + "/" + action,
          type: 'post',
          data: action_data,
          async: true
        }).done(function () {
          $('.status-' + id).text("Cancelled");
          cancel = dialog_id.find('.cancel')
          cancel.html('<h3>La orden ha sido cancelada</h3>')
          dialog.modal('hide');
        }).fail(function(jqXHR, textStatus) {
          cancel = dialog_id.find('.cancel')
          cancel.html('<h3>La ' + object_text + ' no pudo ser cancelada</h3>')
          dialog.modal('hide');
        }).always(function () {
          $(buttonConfirm).attr('disabled', false)
        })
      }
    
    }, 1000);

  }

  async function call_refund_ajax(action, object_kind, object_id, object_text, action_data, dialog_id, products_amount) {
    dialog = await $('#confirm-modal');
    dialog.modal('show');
    body = await $("#confirm-body");
    
    body.empty().append("Esta seguro que desea cambiar/devolver los "+ products_amount +" productos seleccionados")

    //
    // Begin Modal and set confirm button selector
    //
    
    setTimeout(() => {
      let buttonConfirm = document.getElementById('accept-confirm')
      buttonConfirm.onclick = () => {
        
        $.ajax({
          url: "/admin/pioneer/" + object_kind + "/" + object_id + "/" + action,
          type: 'post',
          data: action_data,
          async: true
        }).done(function () {
          $('.status-' + id).text(dialog_id.find('.reasons').val());
          cancel = dialog_id.find('.cancel')
          cancel.html('<h3>Solicitud de ' + dialog_id.find('.reasons').val() + ' realizada exitosamente</h3>')
          dialog.modal('hide');
        }).fail(function () {
          cancel = dialog_id.find('.cancel')
          cancel.html('<h3>La ' + object_text + ' no pudo ser devuelta</h3>')
          dialog.modal('hide');
        })

      }

    }, 1000);

  }
})()
