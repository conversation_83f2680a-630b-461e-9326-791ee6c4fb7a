;if( gp.controller.brands && gp.action.new )(function(){
  $('#sports-tree').select2({ width: '100%' })

  var $manufacturer = $('#brand_profile_attributes_manufacturer_id')
  var $createMfr = $('#create_manufacturer')

  $manufacturer.select2({
    width: '100%',
    allowClear: true
  }).on('change', function(){
    if( $createMfr.is(':checked') ) $createMfr.prop('checked', false)
  })

  $createMfr.on('change', function(e){
    if( $createMfr.is(':checked') ) {
      if( confirm('Are you sure you want to create a Manufacturer with the same name as this Brand? Maybe there\'s one already created.') ){
        $manufacturer.select2('val', '')
      } else {
        $createMfr.prop('checked', false)
      }
    }
  })
})()
