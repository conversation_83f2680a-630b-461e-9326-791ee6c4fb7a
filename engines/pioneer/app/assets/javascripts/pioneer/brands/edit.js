;(function(){
  if( !gp.controller.brands || !gp.action.edit ) return

  $('#shop_id, #brand_profile_attributes_manufacturer_id, #profile_sports_ids').select2({
    width: '100%',
    allowClear: true
  })

  var $startDate = $('#start_date')
  var $endDate = $('#end_date')

  $startDate.fdatepicker({ format: 'yyyy-mm-dd' })
  $endDate.fdatepicker({ format: 'yyyy-mm-dd' })

  var $startDateText = $('.start-date-text')
  var $endDateText = $('.end-date-text')

  $startDate.on('input changeDate', function(){
    $startDateText.html(moment($startDate.val()).format('LL'))
  })

  $endDate.on('input changeDate', function(){
    $endDateText.html(moment($endDate.val()).format('LL'))
  })

  $startDate.add($endDate).trigger('input')

  function loadSelect2Selector(selector, url, msg){
    selector.select2({
      placeholder: msg,
      multiple: false,
      minimumInputLength: 2,
      ajax: {
        url: url,
        data: function(params) {
                    return {
                      q: params.term,
                      login: params.term,
                      search: params.term
                    }
                  },
        processResults: function (data) {
          return { results: data }
        }
      }
    });
  }

  function loadUsersSelector(){
    var usersSelector = $("#managers_user_id")
    var restrictionUsersUrl = usersSelector.data('users-url')

    loadSelect2Selector(usersSelector, restrictionUsersUrl, 'User login...')
  }

  function laodNonProfitsSelector(){
    nonProfitsSelector = $('select#non_profit_id')
    nonProfitsUrl = nonProfitsSelector.data('non-profits-url')

    loadSelect2Selector(nonProfitsSelector, nonProfitsUrl, 'Non Profit login...')
  }

  function loadProAtheletesSelector(){
    proAthleteSelector = $('select#pro_athlete_id')
    proAthleteUrl = proAthleteSelector.data('pro-athletes-url')

    loadSelect2Selector(proAthleteSelector, proAthleteUrl, 'Pro Athlete login...')
  }

  loadUsersSelector()
  laodNonProfitsSelector()
  loadProAtheletesSelector()
})()
