;(function(){
	$window = $(window)
	$document = $(document)

  var gp = this.gp = {
    action: {},
    controller: {},
    Helpers: {},
    Views: {}
  }

  if (typeof _ === 'function') {
    gp.pubSub = _({}).extend( Backbone.Events )
  }

  // Set controller and action variables.
  var method = $('body').attr('id').split('-')
  gp.controller[method[0]] = true
  gp.action[method[1]] = true

  $document.foundation()
}).call(this)
