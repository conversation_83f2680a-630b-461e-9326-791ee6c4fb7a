;(function(){
  if( !gp.controller.orders ) return

  var $startDate = $('#start_date')
  var $endDate = $('#end_date')

  var $paymentStartDate = $('#payment_start_date')
  var $paymentEndDate = $('#payment_end_date')

  $startDate.fdatepicker({ format: 'yyyy-mm-dd' })
  $endDate.fdatepicker({ format: 'yyyy-mm-dd' })

  $paymentStartDate.fdatepicker({ format: 'yyyy-mm-dd' })
  $paymentEndDate.fdatepicker({ format: 'yyyy-mm-dd' })

  var $startDateText = $('.start-date-text')
  var $endDateText = $('.end-date-text')

  var $paymentStartDate = $('.payment-start-date-text')
  var $paymentEndDate = $('.paymentend-date-text')

  $(document).on('click', '.process-edit', function(){
    that = this
    id = $(that).data("id")
    dialogId = $('#dialog-edit-' + id)
    $.ajax({
      url: "/admin/pioneer/orders/"+ id +"/update_address",
      type: 'post',
      data:{
        reason: dialogId.find('.reasons').val(),
        email: dialogId.find('.email').val(),
        address: dialogId.find('.address').val(),
        street_number: dialogId.find('.street_number').val(),
        address_2: dialogId.find('.address_2').val(),
      },
      async: true
    }).done(function(){
        dialogId.modal('hide');
        $('#link-' + id).hide();
        $('.status-' + id).text("Updated");
    }).fail(function(){
      updateAddress = dialogId.find('.edit')
      updateAddress.html('<h3>La orden no pudo ser actualizada</h3>')
      that.disabled = true
    })
  });

  $(document).on('click', '.process-order', function(){
    that = this
    id = $(that).data("id")
    dialogId = $('#dialog-' + id)
    $.ajax({
      url: "/admin/pioneer/orders/"+ id +"/cancel",
      type: 'post',
      data:{type: dialogId.find('.types').val(), reason: dialogId.find('.reasons').val()},
      async: true
    }).done(function(){
        dialogId.modal('hide');
        $('#link-' + id).hide();
        $('.status-' + id).text("Cancelled");
        window.alert("La orden ha sido cancelada");
    }).fail(function(){
      cancel = dialogId.find('.cancel')
      cancel.html('<h3>La orden no pudo ser cancelada</h3>')
      that.disabled = true
    })
  });

  $(document).on('click', '.process-suborder', function(){
    that = this
    id = $(that).data("id")
    suborder_id = $(this).data("suborder-id")
    dialogId = $('#dialog-' + id + '-' + suborder_id )
    $.ajax({
      url: "/admin/pioneer/orders/" + id + "/cancel_suborder",
      type: 'post',
      data:{type: dialogId.find('.types').val(), reason: dialogId.find('.reasons').val(), suborder_id: suborder_id},
      async: true
    }).done(function(){
        dialogId.modal('hide');
        $('#link-' + id + '-' + suborder_id).hide();
        $('.status-' + id).text("Cancelled");
        window.alert("La suborden a sido cancelada");
    }).fail(function(){
      cancel = dialogId.find('.cancel')
      cancel.html('<h3>La orden no pudo ser cancelada</h3>')
      that.disabled = true
    })
  });

  $(document).on('click', '.process-suborder-refund', function(){
    that = this
    id = $(that).data("id")
    suborder_id = $(this).data("suborder-id")
    dialogId= $('#dialog-' + id + '-' + suborder_id )
    $.ajax({
      url: "/admin/pioneer/orders/" + id + "/refund_suborder",
      type: 'post',
      data:{type: dialogId.find('.types').val(), reason: dialogId.find('.reasons').val(), suborder_id: suborder_id},
      async: true
    }).done(function(){
        dialogId.modal('hide');
        $('#link-' + id + '-' + suborder_id).hide();
        $('.status-' + id).text("Returned");
        window.alert("Solicitud de " + dialogId.find('.types').val() + "  realizada exitosamente");
    }).fail(function(){
      cancel = dialogId.find('.cancel')
      cancel.html('<h3>La orden no pudo ser devuelta</h3>')
      that.disabled = true
    })
  });

  $(document).on('click', '..process-order-refund', function(){
    that = this
    that.disabled = true
    id = $(that).data("id")
    dialogId= $('#dialog-' + id)
    $.ajax({
      url: "/admin/pioneer/orders/"+ id +"/refund",
      type: 'post',
      data:{type: dialogId.find('.types').val(), reason: dialogId.find('.reasons').val()},
      async: true
    }).done(function(){
      dialogId.modal('hide');
      $('#link-' + id).hide();
      $('.status-' + id).text("Returned");
      window.alert("Solicitud de " + dialogId.find('.types').val() + "  realizada exitosamente");
    }).fail(function(){
      cancel = dialogId.find('.cancel')
      cancel.html('<h3>La orden no pudo ser devuelta</h3>')
    })
  });

  $('[data-toggle="tooltip"]').tooltip();
  $('.chevron').on('click','a',function(e){
    $('.chevron i').addClass('fa-chevron-right').removeClass('fa-chevron-down');
    $(event.target).closest(".chevron").find('i').toggleClass('fa-chevron-right fa-chevron-down');
    $('.collapse').collapse('hide');
  });
})()
