class CreateRenaperAnswers < ActiveRecord::Migration
  def change
    create_table :renaper_attempted_answers do |t|
      t.string :question_type, limit: 25
      t.string :doc_number, limit: 25
      t.string :gender, limit: 1
      t.integer :question_id
      t.string :question
      t.string :answer
      t.string :ip, limit: 46
      t.string :purchase_id
      t.boolean :correct

      t.timestamps null: false
    end
    add_index :renaper_attempted_answers, :purchase_id

    create_table :renaper_answers do |t|
      t.string :question_type, limit: 25
      t.integer :question_id
      t.string :doc_number, limit: 25
      t.string :gender, limit: 1
      t.integer :question_id
      t.string :question
      t.string :correct_answer
      t.string :purchase_id
      t.boolean :answered
      t.string :token, limit: 30
      t.timestamps null: false
    end
    add_index :renaper_answers, :purchase_id
  end
end
