class AddColumnsToShopSettings < ActiveRecord::Migration
  def change
    add_column :mkp_shop_settings, :company_name, :string, limit: 50, default: nil
    add_column :mkp_shop_settings, :contact_email, :text, default: nil
    add_column :mkp_shop_settings, :contact_number, :text, default: nil
    add_column :mkp_shop_settings, :business_number, :text, default: nil
    add_column :mkp_shop_settings, :account_executive, :string, limit: 50, default: nil
  end
end
