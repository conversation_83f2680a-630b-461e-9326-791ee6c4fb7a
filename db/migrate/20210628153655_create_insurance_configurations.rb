class CreateInsuranceConfigurations < ActiveRecord::Migration
  def change
    create_table :insurance_configurations do |t|
      t.references :store
      t.boolean :active
      t.text :categories
      t.float :percentage
      t.string :installments

      t.timestamps null: false
    end

    add_index :insurance_configurations, :store_id
    add_foreign_key :insurance_configurations, :mkp_stores, column: "store_id"
  end
end
