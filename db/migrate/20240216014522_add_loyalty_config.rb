class AddLoyaltyConfig < ActiveRecord::Migration
  def change
    create_table :loyalty_configs do |t|
      t.string :name
      t.string :slug
      t.text :description
      t.text :value
      t.text :value_type
      t.timestamps null: false
    end
    add_index :loyalty_configs, :name, unique: true
    add_index :loyalty_configs, :slug, unique: true

    add_index :mkp_payments, :gateway
    add_index :mkp_payments, :status

    LoyaltyConfig.create(slug: 'active', name: 'Activo', description: 'Activa o desactiva el sistema de loyalty', value: 'true', value_type: 'boolean')
    LoyaltyConfig.create(slug: 'conversion', name: 'Conversión', description: 'Indica cuantos pesos equivale 1 punto. Ej: 0.5 es 1 punto son $0.5', value: '0.2', value_type: 'decimal')
    LoyaltyConfig.create(slug: 'max_amount_per_month', name: 'Monto máximo mensual', description: 'Indica la cantidad máxima de puntos que pueden usar los usuarios por mes', value: '1000000', value_type: 'integer')
  end
end
