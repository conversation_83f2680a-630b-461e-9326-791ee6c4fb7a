class CreateRenaperFailedServices < ActiveRecord::Migration
  def change
    create_table :renaper_failed_services do |t|
      t.references :store
      t.string :purchase_id
      t.string :doc_type, limit: 10
      t.string :doc_number
      t.string :description
      t.datetime :deleted_at
      t.timestamps null: false
    end
    
    add_index :renaper_failed_services, :deleted_at
    add_index :renaper_failed_services, :store_id
    add_foreign_key :renaper_failed_services, :mkp_stores, column: "store_id"
  end
end
