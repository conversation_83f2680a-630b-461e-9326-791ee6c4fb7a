class CreateEquifaxIdValidatorConfigurations < ActiveRecord::Migration
  def change
    create_table :equifax_id_validator_configurations do |t|
      t.references :store
      t.boolean :active
      t.string :user
      t.string :client
      t.string :matrix
      t.string :password
      t.string :sector
      t.string :office
      t.string :s2s_user
      t.string :s2s_password
      t.string :questionnaire

      t.timestamps null: false
    end

    add_index :equifax_id_validator_configurations, :store_id
    add_foreign_key :equifax_id_validator_configurations, :mkp_stores, column: "store_id"
  end
end
