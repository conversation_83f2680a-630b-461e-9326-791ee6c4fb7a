class RenameRenaperAttemptedAnswersToAttemptedAnswers < ActiveRecord::Migration
  def up
    rename_table :renaper_attempted_answers, :attempted_answers
    add_column :attempted_answers, :type, :string
    AnswerAttempt::AttemptedAnswer.unscoped.update_all({type: "AnswerAttempt::Ren<PERSON>"})
  end
  
  def down
    rename_table :attempted_answers, :renaper_attempted_answers
    remove_column :renaper_attempted_answers, :type, :string
  end
end
