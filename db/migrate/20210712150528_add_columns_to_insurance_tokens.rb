class AddColumnsToInsuranceTokens < ActiveRecord::Migration
  def change
    add_column :insurance_tokens, :installments, :integer
    add_column :insurance_tokens, :name, :string
    add_column :insurance_tokens, :email, :string
    add_column :insurance_tokens, :gender, :string
    add_column :insurance_tokens, :birthday, :string
    add_column :insurance_tokens, :birth_location, :string
    add_column :insurance_tokens, :nationality, :string
    add_column :insurance_tokens, :doc_type, :string
    add_column :insurance_tokens, :doc_number, :string
    add_column :insurance_tokens, :phone, :string
    add_column :insurance_tokens, :address, :string
    add_column :insurance_tokens, :street_number, :string
    add_column :insurance_tokens, :dept, :string
    add_column :insurance_tokens, :country, :string
    add_column :insurance_tokens, :state, :string
    add_column :insurance_tokens, :city, :string
    add_column :insurance_tokens, :postal_code, :string
    add_column :insurance_tokens, :civil_status, :string
    add_column :insurance_tokens, :profession, :string
    add_column :insurance_tokens, :political_exposure, :boolean
    add_column :insurance_tokens, :reference_id, :integer
    add_column :insurance_tokens, :extra_data, :string
  end
end
