class AddPointsToCartAndOrder < ActiveRecord::Migration
  def change
    add_column :mkp_carts, :points, :integer, default: 0
    add_column :mkp_carts, :points_money, :decimal, precision: 11, scale: 2, default: 0
    add_column :mkp_carts, :points_uuid, :string
    add_index :mkp_carts, :points_uuid, unique: true
    add_column :mkp_orders, :points, :integer, default: 0
    add_column :mkp_orders, :points_money, :decimal, precision: 11, scale: 2, default: 0
    add_column :mkp_orders, :points_uuid, :string
    add_column :mkp_orders, :points_status, :string
    add_index :mkp_orders, :points_uuid, unique: true
    add_index :mkp_orders, :points_status
  end
end
