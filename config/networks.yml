# This config file defines properties and behavior of each network.
#
# Below there is a list of available properties. Note that, by convention,
# boolean properties end with a question mark. Also, omitted boolean properties
# have a false value by default.
#
#
# - default?: Sets this network as the default one. Note that only one network
# must have this attribute set to true.
#
# - active?: When true, this network is available in the app; otherwise, it is
# ignored.
# - visible: When true, this network is visible to end user
#
# - title: Human-readable name of the network.
#
# - locale: Locale used by the app when the user is in this network.
#
# - shipping_methods: Available shipping methods for this network. The names
# must match subclasses of shipping::Base (without the namespace).
#
# - payment_gateways: Available payment gateways for this network. The names must
# match subclasses of Payment (without the namespace).
#
# - charges_taxes?: When true, purchases in this network include taxes in the
# total amount.
#
# - requires_product_dimensions?: When true, weight, height, length and width
# fields are mandatory when creating a product (in Lux).
#
# - uses_2_address_fields?: When true, while creating an address in the
# checkout process in this network, a second address line is shown, customary in
# the US.
#
# - requires_telephone?: When true, while creating an address in the checkout
# process in this network, phone number field is mandatory.
#
# - extends_footer?: When true, this network shows an extended version of the
# footer, with additional info.
#
# - manually_set_tracking_id: When true the shops of this network have to set
# the tracking id manually from lux.
#
# - currency_id: Reference to the currency used on the marketplace in this
# network.
#
# - mass_unit: Default mass unit for products on the marketplace.
#
# - length_unit: Default length unit for products on the marketplace.
#
# - marketplace_customer_email: Marketplace contact address for customers.
#
# - marketplace_customer_phone: Marketplace contact phone for customers.
#
# - marketplace_shop_email: Marketplace contact address for shops.
#
# - twitter_account_username: (optional) Twitter account name, for sharing content.
#
# - display_shipping_warn_legend: Display shipping warn msg when choosing
#  shipping speed
#
# - social_profiles: Links to social profiles on other sites
#
# - show_non_profits: Show Non Profits Links (on footer, etc)
#

US:
  active?: false
  charges_taxes?: true
  coupon_defaults:
    amount: 50
    minimum_value: 1
    percent: 10
  currency_id: 2
  default_brand_id: 4105
  default_shop_id: 2
  display_shipping_warn_legend: true
  fallback_locale: es
  footer:
    non_profits: true
  free_shipping_from: 1_000_000
  icbm: "37.781929, -122.404176"
  identifier: US
  label_gateways: [Easypost]
  locale: en
  length_unit: inches
  mail_menu_setup:
    limit: 6
  mass_unit: pounds
  marketplace_customer_email: <EMAIL>
  marketplace_customer_phone: 
  marketplace_shop_email: <EMAIL>
  network_affiliates: ['Pampa']
  payment_gateways: [Braintree]
  placename: San Francisco
  position: "37.781929; -122.404176"
  price_range_for_recommended_products: 20
  recommendations_types: ['SocialUser', 'Brand']
  requires_telephone?: true
  requires_product_dimensions?: true
  region: US-CA
  shipment_gateways: []
  shipping_methods: [Easypost, Fixed]
  shop_integrations: [Shopify]
  shops_can_be_fulfilled?: false
  social_profiles:
    instagram: http://instagram.com/avenida_ar
    facebook: http://www.facebook.com/avenida.com.ar
    twitter: https://twitter.com/avenida_ar
    youtube: http://www.youtube.com/c/AvenidaOnline
  time_zone: 'Pacific Time (US & Canada)'
  title: United States
  twitter_account_username: GoodPeople
  uses_2_address_fields?: true
  visible: true

AR:
  active?: true
  currency_id: 1
  coupon_defaults:
    amount: 10
    minimum_value: 50
    percent: 15
  default?: true
  default_brand_id: 2
  default_shop_id: 1
  display_shipping_warn_legend: false
  extends_footer?: true
  fallback_locale: en
  footer:
    non_profits: false
  free_shipping_from: 2999
  gp_meli:
    buying_mode: buy_it_now
    condition: new
    extra_title: 'Envíos A Todo El País'
    listing_type: bronze
    official_store_id: 9
    warranty: 'Por fallas de fabricación'
  icbm: "-34.568081, -58.446804"
  identifier: AR
  invoice_gateway: Colppy
  label_gateways: [OcaEpak]
  length_unit: millimeters
  locale: es
  manually_set_tracking_id?: true
  mail_menu_setup:
    limit: 3
    extra: 'ofertas'
  marketplace_customer_email: <EMAIL>
  marketplace_customer_phone: <EMAIL>
  marketplace_shop_email: <EMAIL>
  mass_unit: kilograms
  network_affiliates: []
  payment_gateways: [Mercadopago, Decidir, DecidirDistributed, DecidirDistributedMacro, faceTodopago, Empty, Tarjetadigital, FirstData, FirstDataDistributed, VisaPuntos, AvenidaDecidirDistributed, AvenidaMercadopago]
  placename: Buenos Aires
  position: "-34.568081; -58.446804"
  price_range_for_recommended_products: 20
  recommendations_types: ['SocialUser', 'Brand']
  region: AR
  shipment_gateways: [Shipnow]
  shipping_methods: [Oca, Fixed, Express]
  shops_can_be_fulfilled?: true
  #shop_integrations: ['Mercadolibre', 'Shopify', 'TiendaNube', 'Vtex']
  shop_integrations: ['Mercadolibre']
  pickit_warehouse:
    address: Coronel Uzal 3490
    city: Olivos
    name: Deposito Pickit
    state: Buenos Aires
    zip_code: 1636
  pioneer_integrations: [GPMercadolibre]
  social_profiles:
    instagram: http://instagram.com/avenida_ar
    facebook: http://www.facebook.com/avenida.com.ar
    twitter: https://twitter.com/avenida_ar
    youtube: http://www.youtube.com/c/AvenidaOnline
  time_zone: 'Buenos Aires'
  title: Argentina
  twitter_account_username: avenida_ar
  visible: true

CL:
  active?: false
  coupon_defaults:
    minimum_value: 120000
    amount: 6000
    percent: 10
  currency_id: 3
  default_shop_id: 1
  display_shipping_warn_legend: false
  extends_footer?: false
  fallback_locale: es
  footer:
    non_profits: false
  identifier: CL
  length_unit: millimeters
  locale: es
  manually_set_tracking_id?: true
  mass_unit: kilograms
  marketplace_customer_email: <EMAIL>
  marketplace_customer_phone:
  marketplace_shop_email: <EMAIL>
  network_affiliates: []
  payment_gateways: [Webpay]
  recommendations_types: ['SocialUser', 'Brand']
  shipping_methods: [Fixed]
  shops_can_be_fulfilled?: false
  social_profiles:
    instagram: http://instagram.com/avenida_ar
    facebook: http://www.facebook.com/avenida.com.ar
    twitter: https://twitter.com/avenida_ar
    youtube: http://www.youtube.com/c/AvenidaOnline
  title: Chile
  twitter_account_username: GoodPeopleArg
  visible: false

PE:
  active?: false
  identifier: PE
  locale: es-PE
  thin?: true
  title: Perú
  visible: false
#   fallback_locale: es
#   currency_id: 6
#   network_affiliates: []

BR:
  active?: false
  identifier: BR
  thin?: true
  title: Brasil
  visible: false
#   locale: pt
#   fallback_locale: es
#   currency_id: 6
#   network_affiliates: []

MX:
  active?: false
  identifier: MX
  thin?: true
  title: México
  visible: false
#   locale: es-MX
#   fallback_locale: es
#   currency_id: 6
#   network_affiliates: []

EC:
  active?: false
  identifier: EC
  thin?: true
  title: Ecuador
  visible: false
#   locale: es-EC
#   fallback_locale: es
#   currency_id: 6
#   network_affiliates: []

UY:
  active?: false
  identifier: UY
  thin?: true
  title: Uruguay
  visible: false
#   locale: es-UY
#   fallback_locale: es
#   currency_id: 6
#   network_affiliates: []

CR:
  active?: false
  identifier: CR
  thin?: true
  title: Costa Rica
  visible: false
#   locale: es-CR
#   fallback_locale: es
#   currency_id: 5
#   network_affiliates: []

CO:
  active?: false
  identifier: CO
  thin?: true
  title: Colombia
  visible: false
#   locale: es-CO
#   fallback_locale: es
#   currency_id: 4
#   network_affiliates: []
