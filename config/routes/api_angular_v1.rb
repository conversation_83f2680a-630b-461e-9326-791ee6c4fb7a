ANGULAR_APP_VERSION = "v1"
namespace :api, defaults: { format: 'json' } do
  namespace :angular_app do
    scope module: ANGULAR_APP_VERSION do
      resources :bines, only: :index
      resources :brands, only: [:show]
      resources :categories,  only: :index
      resources :contact, only: :create
      resources :coupons, only: :create
      resources :verify_card, only: [:create, :index]

      resources :checkout, only: [] do
        collection do
          get :bines
          get :first_purchase
          post :init
          post :cart_quantity
          post :coupon
          post :remove_coupon
          post :address
          post :delivery_options
          post :subscription_newsletter
          post :withdrawal_branch
          post :payment
          get  :loan_form
          post :done
          post :authorize_todopago
          post :installments
          post :installments_no_bines
          post :jubilo_credit_request
          post 'identity_questions', :to => 'checkout#identity_questions_data_entry', :as => 'identity_questions'
          get :read_summary
          get :extended_warranties
          post :extended_warranties
          post 'payment_intent/:gateway', :to => 'checkout#payment_intent', :as => 'payment_intent'
        end
      end

      resources :insurances, only: [:create] do
        collection do
          get 'new/:token', :to => "insurances#new", :as => 'new'
        end
      end

      resources :addresses, only: [] do
        collection do
          get :provinces, to: 'addresses#provinces'
          get :cities,    to: 'addresses#cities'
          get :zip_codes, to: 'addresses#zip_codes'
        end
      end
      post :answers_attempted, to: 'application#answers_attempted', as: 'answers_attempted'

      post :password_recovery, to: 'application#password_recovery', as: 'password_recovery'

      resources :landings, only: [:show] do
        get :home, on: :collection
      end

      resources :legacy, only: :create
      resources :menus, only: [:index] do
        get :sticky_navbars, on: :collection
      end

      resources :my_account, only: [] do
        collection do
          resources :addresses, only: [:index, :show, :create, :update, :destroy]
          resources :orders, only: [:index, :show]
          resource :profiles, only: [:show, :update]
          resources :wishlists, only: [:index, :show, :update, :destroy]
          resources :transactions_point, only: [:index, :show]
          get :coupons, to: 'coupons#index'
        end
      end

      resources :newsletters, only: [:create]

      resources :orders, only: [] do
        post :tracking, on: :collection
      end

      resources :ping, only: :index

      resources :products, only: [:show] do
        collection do
          post :calculate_shipping
          post :stock_by_shopping
        end
      end

      resources :search, only: [:index, :show] do
        get :suggestions, on: :collection
      end

      resources :search, only: [:index, :show]

      resources :sessions, only: [:create] do
        post :macro_login, on: :collection
      end

      resources :stores, only: [] do
        collection do
          get :gateway
          get :current_store
        end
      end

      resources :users, only: [:create] do
        collection do
          get :me
          post :omniauth
          post :validate_attributes
        end
      end

      resources :users, only: [:logout] do
        collection do
          get :logout
        end
      end

      namespace :boca do
        resource :registers, only: [] do
          collection do
            post :sign_up
            post :payment
          end
        end
      end
      namespace :jubilo do
        post :auth, to: 'auth#authorizate'
        resource :resources, only: [] do
          collection do
            get :credit
            get :client
            get :search
            get :sellers
            get :payment_options
            post :grant_credit
            delete :referrer
          end
        end
      end

      namespace :integration do
        resources :sube_cards, only: [:index, :show, :create, :update, :destroy] do
          get :verify, on: :collection
        end
        post :points_exchange, to: 'purchases#points_exchange', as: 'points_exchange'
        post :points_refund, to: 'purchases#points_refund', as: 'points_refund'
        post :reservable_purchase, to: 'purchases#reservable_purchase', as: 'reservable_purchase'
        post :voucher_points_exchange, to: 'purchases#voucher_points_exchange', as: 'voucher_points_exchange'
      end
    end
  end
end
