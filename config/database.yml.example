<%
  # git config --bool branch.feature.database true
  # http://mislav.uniqpath.com/rails/branching-the-database-along-with-your-code/
  branch = `git symbolic-ref HEAD 2>/dev/null`.chomp.sub('refs/heads/', '')
  suffix = `git config --bool branch.#{branch}.database`.chomp == 'true' ? "#{branch}" : "development"
%>

development: &mysql
  adapter: mysql2
  encoding: utf8
  database: goodpeople_<%= suffix %>
  pool: 20
  username: root
  password:
  host: 127.0.0.1
  port: 3306

test:
  <<: *mysql
  database: goodpeople_<%= suffix %>_test<%= ENV['TEST_ENV_NUMBER'] %>

production:
  <<: *mysql
  database: goodpeople_production

profile:
  <<: *mysql
  database: goodpeople_profile
