#
# This file configures the New Relic Agent.  New Relic monitors Ruby, Java,
# .NET, PHP, Python, Node, and Go applications with deep visibility and low
# overhead.  For more information, visit www.newrelic.com.
#
# Generated October 28, 2022
#
# This configuration file is custom generated for NewRelic Administration
#
# For full documentation of agent configuration options, please refer to
# https://docs.newrelic.com/docs/agents/ruby-agent/installation-configuration/ruby-agent-configuration
common: &default_settings

  # Required license key associated with your New Relic account.
  license_key: 'ac0cf67867fb48f0c7f2853b9d5e9740FFFFNRAL'

  # Your application name. Renaming here affects where data displays in New
  # Relic.  For more details, see https://docs.newrelic.com/docs/apm/new-relic-apm/maintenance/renaming-applications
  app_name: 'bna_platform'
  log_level: error

  distributed_tracing:
    enabled: true

  # Configuración de transacciones lentas
  slow_sql:
    enabled: true # Habilitar el seguimiento de SQL lento
    threshold: 2.0 # Umbral en segundos para considerar una consulta como lenta

  # Configuración de trazas de error
  error_collector:
    enabled: true # Habilitar la recolección de errores
    capture_events: true # Capturar eventos de errores

  # Configuración de recolección de datos de rendimiento
  transaction_tracer:
    enabled: true # Habilitar el trazador de transacciones
    transaction_threshold: apdex_f # Umbral de apdex para las trazas de transacciones

  # Configuración de recolección de eventos
  analytics_events:
    enabled: true # Habilitar la recolección de eventos analíticos
    max_samples_stored: 1200 # Máximo de muestras de eventos almacenados

# Environment-specific settings are in this section.
# RAILS_ENV or RACK_ENV (as appropriate) is used to determine the environment.
# If your application has other named environments, configure them here.
development:
  <<: *default_settings
  app_name: 'bna_platform (Development)'
  monitor_mode: false

test:
  <<: *default_settings
  # It doesn't make sense to report to New Relic from automated test runs.
  monitor_mode: false

staging:
  <<: *default_settings
  app_name: 'bna_platform (Staging)'

production:
  <<: *default_settings
  audit_log:
    enabled: false # Desactivar el registro de auditoría para mejorar el rendimiento
  transaction_tracer:
    transaction_threshold: 4.0 # Aumentar el umbral para registrar menos trazas de transacciones
  slow_sql:
    enabled: false # Desactivar el seguimiento de SQL lento
  error_collector:
    capture_events: true # Capturar eventos de errores
